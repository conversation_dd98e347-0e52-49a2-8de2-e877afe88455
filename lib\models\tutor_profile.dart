import 'package:flutter/material.dart';

class TutorProfileScreen extends StatelessWidget {
  final String name;
  final String subject;
  final String avatar;

  const TutorProfileScreen({
    super.key,
    required this.name,
    required this.subject,
    required this.avatar,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(name),
        backgroundColor: Colors.blue.shade700,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            CircleAvatar(
              radius: 48,
              backgroundImage: AssetImage(avatar),
              backgroundColor: Colors.blue.shade100,
              onBackgroundImageError: (_, __) {},
              child: avatar.isEmpty ? const Icon(Icons.person, size: 48, color: Colors.white) : null,
            ),
            const SizedBox(height: 16),
            Text(name, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            Text(subject, style: TextStyle(color: Colors.blue.shade700, fontSize: 18)),
            const SizedBox(height: 24),
            Text(
              'Bio: Experienced tutor in $subject. Ready to help you succeed!',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const Spacer(),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 14),
              ),
              onPressed: () {
                // Navigate to booking page or chat
              },
              child: const Text('Book Now', style: TextStyle(color: Colors.white, fontSize: 18)),
            ),
          ],
        ),
      ),
    );
  }
}