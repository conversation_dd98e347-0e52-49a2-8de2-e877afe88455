<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
// use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\Student;
use App\Models\Tutor;
use App\Models\AdminUser;

class AuthController extends Controller
{
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
            'user_type' => 'required|in:student,tutor,admin'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $email = $request->email;
        $password = $request->password;
        $userType = $request->user_type;

        // Get user based on type
        $user = $this->getUserByType($email, $userType);

        if (!$user || !Hash::check($password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if user is active
        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated'
            ], 403);
        }

        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;

        // Update last active
        $user->update(['last_active_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'user_type' => $userType,
                'token' => $token
            ]
        ]);
    }

    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:6|confirmed',
            'user_type' => 'required|in:student,tutor',
            'phone' => 'nullable|string|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if email already exists in any user type
        if ($this->emailExists($request->email)) {
            return response()->json([
                'success' => false,
                'message' => 'Email already exists'
            ], 422);
        }

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'is_active' => true,
            'last_active_at' => now()
        ];

        // Create user based on type
        if ($request->user_type === 'student') {
            $user = Student::create($userData);
        } else {
            $user = Tutor::create($userData);
        }

        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => $user,
                'user_type' => $request->user_type,
                'token' => $token
            ]
        ], 201);
    }

    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    public function user(Request $request): JsonResponse
    {
        $user = $request->user();
        $userType = $this->getUserType($user);

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'user_type' => $userType
            ]
        ]);
    }

    private function getUserByType(string $email, string $userType)
    {
        switch ($userType) {
            case 'student':
                return Student::where('email', $email)->first();
            case 'tutor':
                return Tutor::where('email', $email)->first();
            case 'admin':
                return AdminUser::where('email', $email)->first();
            default:
                return null;
        }
    }

    private function emailExists(string $email): bool
    {
        return Student::where('email', $email)->exists() ||
               Tutor::where('email', $email)->exists() ||
               AdminUser::where('email', $email)->exists();
    }

    private function getUserType($user): string
    {
        if ($user instanceof Student) return 'student';
        if ($user instanceof Tutor) return 'tutor';
        if ($user instanceof AdminUser) return 'admin';
        return 'unknown';
    }
}
