import 'dart:io';
import '../models/api_response.dart';
import '../models/tutor_post.dart';
import 'api_client.dart';

class PostService {
  static final PostService _instance = PostService._internal();
  factory PostService() => _instance;
  PostService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get all posts with filtering and pagination
  Future<ApiResponse<PaginatedPostResponse>> getPosts({
    int? categoryId,
    String? postType,
    String? search,
    int? tutorId,
    int? page,
    int? perPage,
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (categoryId != null) queryParameters['category_id'] = categoryId;
    if (postType != null) queryParameters['post_type'] = postType;
    if (search != null && search.isNotEmpty) queryParameters['search'] = search;
    if (tutorId != null) queryParameters['tutor_id'] = tutorId;
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;

    return await _apiClient.get<PaginatedPostResponse>(
      '/posts',
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedPostResponse.fromJson(data),
    );
  }

  // Get featured posts
  Future<ApiResponse<List<TutorPost>>> getFeaturedPosts() async {
    final response = await _apiClient.get<Map<String, dynamic>>(
      '/posts/featured',
      fromJson: (data) => data,
    );

    if (response.isSuccess && response.data != null) {
      final List<dynamic> postsJson = response.data!['data'] as List<dynamic>;
      final posts = postsJson.map((json) => TutorPost.fromJson(json as Map<String, dynamic>)).toList();
      
      return ApiResponse<List<TutorPost>>(
        success: true,
        data: posts,
        message: response.message,
      );
    }

    return ApiResponse<List<TutorPost>>(
      success: false,
      data: null,
      message: response.message ?? 'Failed to load featured posts',
    );
  }

  // Get a specific post by slug
  Future<ApiResponse<TutorPost>> getPost(String slug) async {
    final response = await _apiClient.get<Map<String, dynamic>>(
      '/posts/$slug',
      fromJson: (data) => data,
    );

    if (response.isSuccess && response.data != null) {
      final post = TutorPost.fromJson(response.data!['data'] as Map<String, dynamic>);
      
      return ApiResponse<TutorPost>(
        success: true,
        data: post,
        message: response.message,
      );
    }

    return ApiResponse<TutorPost>(
      success: false,
      data: null,
      message: response.message ?? 'Failed to load post',
    );
  }

  // Get posts by tutor
  Future<ApiResponse<PaginatedPostResponse>> getPostsByTutor(
    int tutorId, {
    int? page,
    int? perPage,
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;

    return await _apiClient.get<PaginatedPostResponse>(
      '/posts/tutor/$tutorId',
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedPostResponse.fromJson(data),
    );
  }

  // Create a new post (requires authentication)
  Future<ApiResponse<TutorPost>> createPost(CreatePostRequest request, {
    File? featuredImage,
    List<File>? attachments,
  }) async {
    try {
      // For now, we'll use regular POST. In a real app, you'd use multipart for file uploads
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/posts',
        data: request.toJson(),
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        final post = TutorPost.fromJson(response.data!['data'] as Map<String, dynamic>);
        
        return ApiResponse<TutorPost>(
          success: true,
          data: post,
          message: response.message ?? 'Post created successfully',
        );
      }

      return ApiResponse<TutorPost>(
        success: false,
        data: null,
        message: response.message ?? 'Failed to create post',
      );
    } catch (e) {
      return ApiResponse<TutorPost>(
        success: false,
        data: null,
        message: 'Error creating post: $e',
      );
    }
  }

  // Update a post (requires authentication)
  Future<ApiResponse<TutorPost>> updatePost(int postId, UpdatePostRequest request) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/posts/$postId',
        data: request.toJson(),
        fromJson: (data) => data,
      );

      if (response.isSuccess && response.data != null) {
        final post = TutorPost.fromJson(response.data!['data'] as Map<String, dynamic>);
        
        return ApiResponse<TutorPost>(
          success: true,
          data: post,
          message: response.message ?? 'Post updated successfully',
        );
      }

      return ApiResponse<TutorPost>(
        success: false,
        data: null,
        message: response.message ?? 'Failed to update post',
      );
    } catch (e) {
      return ApiResponse<TutorPost>(
        success: false,
        data: null,
        message: 'Error updating post: $e',
      );
    }
  }

  // Delete a post (requires authentication)
  Future<ApiResponse<void>> deletePost(int postId) async {
    try {
      final response = await _apiClient.delete<Map<String, dynamic>>(
        '/posts/$postId',
        fromJson: (data) => data,
      );

      return ApiResponse<void>(
        success: response.isSuccess,
        data: null,
        message: response.message ?? (response.isSuccess ? 'Post deleted successfully' : 'Failed to delete post'),
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        data: null,
        message: 'Error deleting post: $e',
      );
    }
  }

  // Like/unlike a post (requires authentication)
  Future<ApiResponse<void>> toggleLike(int postId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/posts/$postId/like',
        fromJson: (data) => data,
      );

      return ApiResponse<void>(
        success: response.isSuccess,
        data: null,
        message: response.message ?? (response.isSuccess ? 'Like toggled successfully' : 'Failed to toggle like'),
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        data: null,
        message: 'Error toggling like: $e',
      );
    }
  }

  // Bookmark/unbookmark a post (requires authentication)
  Future<ApiResponse<void>> toggleBookmark(int postId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/posts/$postId/bookmark',
        fromJson: (data) => data,
      );

      return ApiResponse<void>(
        success: response.isSuccess,
        data: null,
        message: response.message ?? (response.isSuccess ? 'Bookmark toggled successfully' : 'Failed to toggle bookmark'),
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        data: null,
        message: 'Error toggling bookmark: $e',
      );
    }
  }

  // Helper methods for filtering and sorting
  static List<TutorPost> filterPostsByType(List<TutorPost> posts, String postType) {
    return posts.where((post) => post.postType == postType).toList();
  }

  static List<TutorPost> filterPostsByCategory(List<TutorPost> posts, int categoryId) {
    return posts.where((post) => post.categoryId == categoryId).toList();
  }

  static List<TutorPost> searchPosts(List<TutorPost> posts, String query) {
    final lowerQuery = query.toLowerCase();
    return posts.where((post) {
      return post.title.toLowerCase().contains(lowerQuery) ||
             post.content.toLowerCase().contains(lowerQuery) ||
             (post.excerpt?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  static List<TutorPost> sortPosts(List<TutorPost> posts, String sortBy, {bool ascending = true}) {
    switch (sortBy) {
      case 'date':
        posts.sort((a, b) => ascending 
            ? a.publishedAt!.compareTo(b.publishedAt!)
            : b.publishedAt!.compareTo(a.publishedAt!));
        break;
      case 'views':
        posts.sort((a, b) => ascending 
            ? a.viewsCount.compareTo(b.viewsCount)
            : b.viewsCount.compareTo(a.viewsCount));
        break;
      case 'likes':
        posts.sort((a, b) => ascending 
            ? a.likesCount.compareTo(b.likesCount)
            : b.likesCount.compareTo(a.likesCount));
        break;
      case 'title':
        posts.sort((a, b) => ascending
            ? a.title.compareTo(b.title)
            : b.title.compareTo(a.title));
        break;
      default:
        // Default sort by published date (newest first)
        posts.sort((a, b) => b.publishedAt!.compareTo(a.publishedAt!));
    }
    return posts;
  }
}
