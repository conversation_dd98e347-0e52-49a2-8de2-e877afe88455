import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/auth.dart';
import '../models/user.dart';
import '../services/auth_service.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  AuthState _state = AuthState.initial;
  String? _errorMessage;
  AuthResponse? _currentUser;
  bool _isLoading = false;

  // Getters
  AuthState get state => _state;
  String? get errorMessage => _errorMessage;
  AuthResponse? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated && _currentUser != null;
  bool get isStudent => _currentUser?.isStudent ?? false;
  bool get isTutor => _currentUser?.isTutor ?? false;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  String? get userName => _currentUser?.userName;
  String? get userEmail => _currentUser?.userEmail;
  int? get userId => _currentUser?.userId;
  String? get userType => _currentUser?.userType;
  Student? get studentUser => _currentUser?.studentUser;
  Tutor? get tutorUser => _currentUser?.tutorUser;
  Admin? get adminUser => _currentUser?.adminUser;

  // Initialize auth state
  Future<void> initialize() async {
    // Use a small delay to ensure we're not in the build phase
    await Future.delayed(const Duration(milliseconds: 10));

    _setState(AuthState.loading);

    try {
      final isLoggedIn = await _authService.isLoggedIn();

      if (isLoggedIn) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          _currentUser = user;
          _setState(AuthState.authenticated);
        } else {
          _setState(AuthState.unauthenticated);
        }
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  // Student registration
  Future<bool> registerStudent({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = StudentRegisterRequest(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
      );

      final response = await _authService.registerStudent(request);

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(response.message ?? 'Registration failed');
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Tutor registration
  Future<bool> registerTutor({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
    String? description,
    String? location,
    double? hourlyRate,
    int? experienceYears,
    String? qualifications,
    List<String>? languages,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = TutorRegisterRequest(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
        description: description,
        location: location,
        hourlyRate: hourlyRate,
        experienceYears: experienceYears,
        qualifications: qualifications,
        languages: languages,
      );

      final response = await _authService.registerTutor(request);

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(response.message ?? 'Registration failed');
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login({
    required String email,
    required String password,
    required String userType,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = LoginRequest(
        email: email,
        password: password,
        userType: userType,
      );

      final response = await _authService.login(request);

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(response.message ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError('Login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _authService.logout();
    } catch (e) {
      print('Logout error: $e');
    } finally {
      _currentUser = null;
      _setState(AuthState.unauthenticated);
      _setLoading(false);
    }
  }

  // Update profile
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? location,
    String? bio,
    String? description,
    double? hourlyRate,
    int? experienceYears,
    String? qualifications,
    List<String>? languages,
    bool? isOnline,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final request = UpdateProfileRequest(
        name: name,
        phone: phone,
        dateOfBirth: dateOfBirth,
        gender: gender,
        location: location,
        bio: bio,
        description: description,
        hourlyRate: hourlyRate,
        experienceYears: experienceYears,
        qualifications: qualifications,
        languages: languages,
        isOnline: isOnline,
      );

      final response = await _authService.updateProfile(request);

      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Profile update failed');
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user data
  Future<void> refreshUser() async {
    if (!isAuthenticated) return;

    try {
      final response = await _authService.getProfile();
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data!;
        notifyListeners();
      }
    } catch (e) {
      print('Error refreshing user: $e');
    }
  }

  // Clear error
  void clearError() {
    _clearError();
  }

  // Private methods
  void _setState(AuthState newState) {
    _state = newState;
    // Use scheduleMicrotask to avoid calling notifyListeners during build
    scheduleMicrotask(() {
      notifyListeners();
    });
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    scheduleMicrotask(() {
      notifyListeners();
    });
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = AuthState.error;
    scheduleMicrotask(() {
      notifyListeners();
    });
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _state = isAuthenticated ? AuthState.authenticated : AuthState.unauthenticated;
    }
    scheduleMicrotask(() {
      notifyListeners();
    });
  }

  // Validation methods
  String? validateEmail(String? email) => AuthService.validateEmail(email);
  String? validatePassword(String? password) => AuthService.validatePassword(password);
  String? validateName(String? name) => AuthService.validateName(name);
  String? validatePhone(String? phone) => AuthService.validatePhone(phone);
  String? validatePasswordConfirmation(String? password, String? confirmation) =>
      AuthService.validatePasswordConfirmation(password, confirmation);
}
