<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sender_id');
            $table->string('sender_type'); // 'student', 'tutor', 'admin'
            $table->unsignedBigInteger('receiver_id');
            $table->string('receiver_type'); // 'student', 'tutor', 'admin'
            $table->text('message');
            $table->string('message_type')->default('text'); // text, image, file
            $table->string('attachment_url')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_deleted_by_sender')->default(false);
            $table->boolean('is_deleted_by_receiver')->default(false);
            $table->timestamps();

            // Indexes for better performance
            $table->index(['sender_id', 'sender_type']);
            $table->index(['receiver_id', 'receiver_type']);
            $table->index(['created_at']);
            $table->index(['is_read']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('messages');
    }
};
