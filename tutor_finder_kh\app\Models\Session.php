<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Session extends Model
{
    use HasFactory;

    protected $fillable = [
        'tutor_id',
        'student_id',
        'session_date',
        'duration_minutes',
        'price',
        'status',
        'notes',
        'meeting_link',
        'session_type',
        'location',
        'started_at',
        'ended_at',
        'cancellation_reason',
    ];

    protected $casts = [
        'session_date' => 'datetime',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'price' => 'decimal:2',
    ];

    /**
     * Get the tutor for this session.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class);
    }

    /**
     * Get the student for this session.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the chats for this session.
     */
    public function chats(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Chat::class);
    }

    /**
     * Get the review for this session.
     */
    public function review(): HasOne
    {
        return $this->hasOne(TutorReview::class);
    }

    /**
     * Get the duration in hours.
     */
    public function getDurationHoursAttribute(): float
    {
        return $this->duration_minutes / 60;
    }

    /**
     * Check if session can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['booked', 'confirmed']) && 
               $this->session_date > now()->addHours(24);
    }

    /**
     * Check if session can be started.
     */
    public function canBeStarted(): bool
    {
        return $this->status === 'confirmed' && 
               $this->session_date <= now()->addMinutes(15) &&
               $this->session_date >= now()->subMinutes(15);
    }

    /**
     * Check if session can be completed.
     */
    public function canBeCompleted(): bool
    {
        return $this->status === 'in_progress';
    }

    /**
     * Check if session can be reviewed.
     */
    public function canBeReviewed(): bool
    {
        return $this->status === 'completed' && !$this->review()->exists();
    }

    /**
     * Scope a query to only include upcoming sessions.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('session_date', '>', now())
                    ->whereIn('status', ['booked', 'confirmed']);
    }

    /**
     * Scope a query to only include past sessions.
     */
    public function scopePast($query)
    {
        return $query->where('session_date', '<', now())
                    ->orWhereIn('status', ['completed', 'cancelled', 'no_show']);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('session_date', [$startDate, $endDate]);
    }
}
