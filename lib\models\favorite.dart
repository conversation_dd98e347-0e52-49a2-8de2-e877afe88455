import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'favorite.g.dart';

@JsonSerializable()
class Favorite {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'student_id')
  final int studentId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'tutor_id')
  final int tutorId;
  final Tutor? tutor;
  final Student? student;
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  Favorite({
    required this.id,
    required this.studentId,
    required this.tutorId,
    this.tutor,
    this.student,
    this.createdAt,
    this.updatedAt,
  });

  factory Favorite.fromJson(Map<String, dynamic> json) => _$FavoriteFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteToJson(this);

  DateTime? get createdDateTime {
    try {
      return createdAt != null ? DateTime.parse(createdAt!) : null;
    } catch (e) {
      return null;
    }
  }

  Favorite copyWith({
    int? id,
    int? studentId,
    int? tutorId,
    Tutor? tutor,
    Student? student,
    String? createdAt,
    String? updatedAt,
  }) {
    return Favorite(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      tutorId: tutorId ?? this.tutorId,
      tutor: tutor ?? this.tutor,
      student: student ?? this.student,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class FavoriteCheck {
  @JsonKey(name: 'tutor_id')
  final int tutorId;
  @JsonKey(name: 'is_favorited')
  final bool isFavorited;

  FavoriteCheck({
    required this.tutorId,
    required this.isFavorited,
  });

  factory FavoriteCheck.fromJson(Map<String, dynamic> json) => _$FavoriteCheckFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteCheckToJson(this);
}

@JsonSerializable()
class FavoriteCount {
  @JsonKey(name: 'favorites_count')
  final int favoritesCount;

  FavoriteCount({
    required this.favoritesCount,
  });

  factory FavoriteCount.fromJson(Map<String, dynamic> json) => _$FavoriteCountFromJson(json);
  Map<String, dynamic> toJson() => _$FavoriteCountToJson(this);
}
