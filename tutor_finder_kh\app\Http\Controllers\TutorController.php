<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Tutor;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class TutorController extends Controller
{
    /**
     * Get all approved tutors with filtering and pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Tutor::with(['categories', 'reviews'])
                     ->approved()
                     ->withCount(['reviews as review_count' => function ($query) {
                         $query->where('is_approved', true);
                     }])
                     ->withAvg(['reviews as average_rating' => function ($query) {
                         $query->where('is_approved', true);
                     }], 'rating');

        // Search by name or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->has('category_id')) {
            $query->byCategory($request->category_id);
        }

        // Filter by location
        if ($request->has('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by online availability
        if ($request->has('is_online')) {
            $query->where('is_online', $request->boolean('is_online'));
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('hourly_rate', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('hourly_rate', '<=', $request->max_price);
        }

        // Filter by minimum rating
        if ($request->has('min_rating')) {
            $query->having('average_rating', '>=', $request->min_rating);
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'rating':
                $query->orderBy('average_rating', $sortOrder);
                break;
            case 'price':
                $query->orderBy('hourly_rate', $sortOrder);
                break;
            case 'experience':
                $query->orderBy('experience_years', $sortOrder);
                break;
            default:
                $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = $request->get('per_page', 15);
        $tutors = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $tutors
        ]);
    }

    /**
     * Get a specific tutor by ID.
     */
    public function show(int $id): JsonResponse
    {
        $tutor = Tutor::with([
                'categories',
                'reviews' => function ($query) {
                    $query->where('is_approved', true)
                          ->with('student:id,name')
                          ->latest()
                          ->limit(10);
                },
                'documents' => function ($query) {
                    $query->where('verification_status', 'verified');
                }
            ])
            ->withCount(['reviews as review_count' => function ($query) {
                $query->where('is_approved', true);
            }])
            ->withAvg(['reviews as average_rating' => function ($query) {
                $query->where('is_approved', true);
            }], 'rating')
            ->approved()
            ->find($id);

        if (!$tutor) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $tutor
        ]);
    }

    /**
     * Get featured tutors.
     */
    public function featured(): JsonResponse
    {
        $tutors = Tutor::with(['categories'])
                      ->approved()
                      ->withCount(['reviews as review_count' => function ($query) {
                          $query->where('is_approved', true);
                      }])
                      ->withAvg(['reviews as average_rating' => function ($query) {
                          $query->where('is_approved', true);
                      }], 'rating')
                      ->having('review_count', '>=', 5)
                      ->having('average_rating', '>=', 4.5)
                      ->orderBy('average_rating', 'desc')
                      ->limit(10)
                      ->get();

        return response()->json([
            'success' => true,
            'data' => $tutors
        ]);
    }

    /**
     * Get tutors by category.
     */
    public function byCategory(int $categoryId): JsonResponse
    {
        $category = Category::find($categoryId);
        
        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }

        $tutors = $category->tutors()
                          ->with(['categories'])
                          ->approved()
                          ->withCount(['reviews as review_count' => function ($query) {
                              $query->where('is_approved', true);
                          }])
                          ->withAvg(['reviews as average_rating' => function ($query) {
                              $query->where('is_approved', true);
                          }], 'rating')
                          ->orderBy('average_rating', 'desc')
                          ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => [
                'category' => $category,
                'tutors' => $tutors
            ]
        ]);
    }

    /**
     * Get tutor availability.
     */
    public function availability(int $id): JsonResponse
    {
        $tutor = Tutor::approved()->find($id);
        
        if (!$tutor) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found'
            ], 404);
        }

        // Get upcoming sessions to determine availability
        $upcomingSessions = $tutor->sessions()
                                  ->upcoming()
                                  ->select('session_date', 'duration_minutes')
                                  ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'tutor_id' => $tutor->id,
                'is_online' => $tutor->is_online,
                'upcoming_sessions' => $upcomingSessions
            ]
        ]);
    }

    /**
     * Search tutors with advanced filters.
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'nullable|string|max:255',
            'category_ids' => 'nullable|array',
            'category_ids.*' => 'integer|exists:categories,id',
            'location' => 'nullable|string|max:255',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'min_rating' => 'nullable|numeric|min:1|max:5',
            'is_online' => 'nullable|boolean',
            'sort_by' => 'nullable|in:rating,price,experience,created_at',
            'sort_order' => 'nullable|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = Tutor::with(['categories'])
                     ->approved()
                     ->withCount(['reviews as review_count' => function ($query) {
                         $query->where('is_approved', true);
                     }])
                     ->withAvg(['reviews as average_rating' => function ($query) {
                         $query->where('is_approved', true);
                     }], 'rating');

        // Apply filters
        if ($request->filled('query')) {
            $searchQuery = $request->query;
            $query->where(function ($q) use ($searchQuery) {
                $q->where('name', 'like', "%{$searchQuery}%")
                  ->orWhere('description', 'like', "%{$searchQuery}%")
                  ->orWhere('qualifications', 'like', "%{$searchQuery}%");
            });
        }

        if ($request->filled('category_ids')) {
            $query->whereHas('categories', function ($q) use ($request) {
                $q->whereIn('category_id', $request->category_ids);
            });
        }

        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        if ($request->filled('min_price')) {
            $query->where('hourly_rate', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('hourly_rate', '<=', $request->max_price);
        }

        if ($request->filled('is_online')) {
            $query->where('is_online', $request->is_online);
        }

        if ($request->filled('min_rating')) {
            $query->having('average_rating', '>=', $request->min_rating);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'average_rating');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'rating':
                $query->orderBy('average_rating', $sortOrder);
                break;
            case 'price':
                $query->orderBy('hourly_rate', $sortOrder);
                break;
            case 'experience':
                $query->orderBy('experience_years', $sortOrder);
                break;
            default:
                $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = $request->get('per_page', 15);
        $tutors = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $tutors
        ]);
    }
}
