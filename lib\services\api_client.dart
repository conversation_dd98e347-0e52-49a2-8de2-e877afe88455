import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../models/api_response.dart';

class ApiClient {
  static const String _tokenKey = 'auth_token';
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  ApiClient._internal();

  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  String? _authToken;

  void initialize() async {
    // Load auth token if available
    _authToken = await _storage.read(key: 'auth_token');
  }

  Map<String, String> get _headers {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    return token != null && token.isNotEmpty;
  }



  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Get user type
  Future<String?> getUserType() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_type');
  }

  // Set user type
  Future<void> setUserType(String userType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_type', userType);
  }

  // Get user data
  Future<String?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_data');
  }

  // Set user data
  Future<void> setUserData(String userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', userData);
  }



  // GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, String>? queryParams,
    Map<String, dynamic>? queryParameters,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');
      final uriWithQuery =
          queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      print('🚀 GET REQUEST STARTED: $uriWithQuery');

      final response = await http.get(uriWithQuery, headers: _headers);

      print('✅ RESPONSE CODE: ${response.statusCode}');
      print('📥 RESPONSE BODY: ${response.body}');

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      print('❌ GET ERROR DETAILS: $e');
      return ApiResponse<T>(
        success: false,
        message: 'Network error: $e',
        data: null,
      );
    }
  }

  // POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');
      final body = data != null ? jsonEncode(data) : null;

      print('🚀 POST: $uri');
      print('📤 DATA: $body');

      final response = await http.post(
        uri,
        headers: _headers,
        body: body,
      );

      print('✅ RESPONSE: ${response.statusCode}');
      print('📥 DATA: ${response.body}');

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      print('❌ POST ERROR: $e');
      return ApiResponse<T>(
        success: false,
        message: 'Network error: $e',
        data: null,
      );
    }
  }

  // PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');
      final body = data != null ? jsonEncode(data) : null;

      print('🚀 PUT: $uri');
      print('📤 DATA: $body');

      final response = await http.put(
        uri,
        headers: _headers,
        body: body,
      );

      print('✅ RESPONSE: ${response.statusCode}');
      print('📥 DATA: ${response.body}');

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      print('❌ PUT ERROR: $e');
      return ApiResponse<T>(
        success: false,
        message: 'Network error: $e',
        data: null,
      );
    }
  }

  // DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');

      print('🚀 DELETE: $uri');

      final response = await http.delete(uri, headers: _headers);

      print('✅ RESPONSE: ${response.statusCode}');
      print('📥 DATA: ${response.body}');

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      print('❌ DELETE ERROR: $e');
      return ApiResponse<T>(
        success: false,
        message: 'Network error: $e',
        data: null,
      );
    }
  }

  // Raw GET request that returns Map<String, dynamic>
  Future<Map<String, dynamic>> rawGet(String endpoint, {Map<String, String>? queryParams}) async {
    try {
      final uri = Uri.parse('${ApiConfig.baseUrl}$endpoint');
      final uriWithQuery = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

      print('🚀 RAW GET REQUEST: $uriWithQuery');

      final response = await http.get(uriWithQuery, headers: _headers);

      print('✅ RAW RESPONSE CODE: ${response.statusCode}');
      print('📥 RAW RESPONSE BODY: ${response.body}');

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      return jsonData;
    } catch (e) {
      print('❌ RAW GET ERROR: $e');
      return {
        'success': false,
        'message': 'Network error: $e',
        'data': null,
      };
    }
  }

  // Handle HTTP response
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      final Map<String, dynamic> jsonData = jsonDecode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Success response
        T? data;
        if (fromJson != null && jsonData['data'] != null) {
          if (jsonData['data'] is List && T.toString().contains('List')) {
            // Handle list response - pass the entire list to fromJson
            data = jsonData['data'] as T;
          } else if (jsonData['data'] is List) {
            // Handle list response where T is the item type
            final List<dynamic> listData = jsonData['data'];
            data = listData.map((item) => fromJson(item as Map<String, dynamic>)).toList() as T;
          } else {
            // Handle single object response
            data = fromJson(jsonData['data'] as Map<String, dynamic>);
          }
        }

        return ApiResponse<T>(
          success: true,
          message: jsonData['message'] ?? 'Success',
          data: data,
        );
      } else {
        // Error response
        return ApiResponse<T>(
          success: false,
          message: jsonData['message'] ?? 'Request failed',
          data: null,
        );
      }
    } catch (e) {
      // JSON parsing error
      return ApiResponse<T>(
        success: false,
        message: 'Failed to parse response: $e',
        data: null,
      );
    }
  }

  // Token management
  Future<void> setToken(String token) async {
    _authToken = token;
    await _storage.write(key: 'auth_token', value: token);
  }

  Future<String?> getToken() async {
    _authToken ??= await _storage.read(key: 'auth_token');
    return _authToken;
  }

  Future<void> clearToken() async {
    _authToken = null;
    await _storage.delete(key: 'auth_token');
  }

  // Check if user is authenticated
  bool get isAuthenticated => _authToken != null;
}
