import 'package:flutter/material.dart';
import 'dart:async';
import '../models/tutor_profile.dart';
import '../services/api_client.dart';

class TutorSearchScreen extends StatefulWidget {
  const TutorSearchScreen({super.key});

  @override
  State<TutorSearchScreen> createState() => _TutorSearchScreenState();
}

class _TutorSearchScreenState extends State<TutorSearchScreen> {
  final TextEditingController _searchController = TextEditingController();

  // Filter variables
  String _selectedSubject = 'All';
  String _selectedLocation = 'All';
  String _selectedMajor = 'All';
  String _selectedGradeLevel = 'All';
  String _selectedPriceRange = 'All';
  String _selectedExperience = 'All';
  String _selectedAvailability = 'All';
  String _selectedLanguage = 'All';
  double _minRating = 0.0;
  bool _isOnlineOnly = false;
  bool _isOfflineOnly = false;
  bool _isVerifiedOnly = false;
  bool _isAvailableNow = false;

  List<Map<String, dynamic>> _tutors = [];
  List<Map<String, dynamic>> _filteredTutors = [];
  bool _isLoading = true;

  // Debouncing for search
  Timer? _debounceTimer;

  // Filter options
  final List<String> _subjects = [
    'All', 'Mathematics', 'English', 'Physics', 'Chemistry', 'Biology',
    'History', 'Geography', 'IT/Computer Science', 'Khmer Literature',
    'French', 'Chinese', 'Economics', 'Accounting', 'Art', 'Music'
  ];

  final List<String> _locations = [
    'All', 'Phnom Penh', 'Siem Reap', 'Battambang', 'Kampong Cham',
    'Kampong Speu', 'Kandal', 'Takeo', 'Kampot', 'Kep', 'Online Only'
  ];



  final List<String> _gradeLevels = [
    'All', 'Elementary (Grade 1-6)', 'Middle School (Grade 7-9)',
    'High School (Grade 10-12)', 'University/College', 'Adult Education',
    'Professional Certification', 'Language Learning'
  ];

  final List<String> _priceRanges = [
    'All', '\$5-10', '\$10-15', '\$15-20', '\$20-25', '\$25-30', '\$30-40', '\$40+'
  ];



  @override
  void initState() {
    super.initState();
    _loadMockData(); // Load mock data immediately for testing
    // _loadTutors(); // Uncomment this when API is ready

    // Handle arguments passed from other screens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleNavigationArguments();
    });
  }

  void _handleNavigationArguments() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      // Handle search query
      if (args['search'] != null) {
        _searchController.text = args['search'];
      }

      // Handle pre-selected subject
      if (args['selectedSubject'] != null) {
        _selectedSubject = args['selectedSubject'];
      }

      // Auto-apply filters if requested
      if (args['autoApplyFilter'] == true) {
        // Use post-frame callback to avoid setState during build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _applyFilters();
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _applyFilters();
    });
  }

  void _resetAllFilters() {
    _selectedSubject = 'All';
    _selectedLocation = 'All';
    _selectedMajor = 'All';
    _selectedGradeLevel = 'All';
    _selectedPriceRange = 'All';
    _selectedExperience = 'All';
    _selectedAvailability = 'All';
    _selectedLanguage = 'All';
    _minRating = 0.0;
    _isOnlineOnly = false;
    _isOfflineOnly = false;
    _isVerifiedOnly = false;
    _isAvailableNow = false;
    _applyFilters();
  }

  Future<void> _loadTutors() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final ApiClient apiClient = ApiClient();

      // Build query parameters for filtering
      Map<String, dynamic> queryParams = {};

      if (_searchController.text.isNotEmpty) {
        queryParams['search'] = _searchController.text;
      }
      if (_selectedSubject != 'All') {
        queryParams['subject'] = _selectedSubject;
      }
      if (_selectedLocation != 'All') {
        queryParams['location'] = _selectedLocation;
      }
      if (_selectedMajor != 'All') {
        queryParams['major'] = _selectedMajor;
      }
      if (_selectedGradeLevel != 'All') {
        queryParams['grade_level'] = _selectedGradeLevel;
      }
      if (_selectedPriceRange != 'All') {
        queryParams['price_range'] = _selectedPriceRange;
      }
      if (_selectedExperience != 'All') {
        queryParams['experience'] = _selectedExperience;
      }
      if (_selectedLanguage != 'All') {
        queryParams['language'] = _selectedLanguage;
      }
      if (_minRating > 0) {
        queryParams['min_rating'] = _minRating.toString();
      }
      if (_isOnlineOnly) {
        queryParams['online_only'] = 'true';
      }
      if (_isOfflineOnly) {
        queryParams['offline_only'] = 'true';
      }
      if (_isVerifiedOnly) {
        queryParams['verified_only'] = 'true';
      }
      if (_isAvailableNow) {
        queryParams['available_now'] = 'true';
      }

      // Build query string
      String queryString = '';
      if (queryParams.isNotEmpty) {
        queryString = '?${queryParams.entries
            .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
            .join('&')}';
      }

      // Call the Laravel API
      final response = await apiClient.get('/tutors/search$queryString');

      if (response.success && response.data != null) {
        setState(() {
          // Convert API response to the format expected by the UI
          _tutors = (response.data as List).map((tutor) => {
            'id': tutor['id'].toString(),
            'name': tutor['name'] ?? 'Unknown Tutor',
            'subject': tutor['subject'] ?? 'General',
            'location': tutor['location'] ?? 'Not specified',
            'major': tutor['major'] ?? 'Not specified',
            'gradeLevel': tutor['grade_level'] ?? 'All levels',
            'rating': (tutor['rating'] ?? 0.0).toDouble(),
            'reviews': tutor['reviews_count'] ?? 0,
            'price': tutor['hourly_rate'] ?? 0,
            'avatar': tutor['profile_image'] ?? 'assets/images/default_avatar.png',
            'experience': tutor['experience'] ?? 'Not specified',
            'isOnline': tutor['is_online'] ?? false,
            'isOffline': tutor['is_offline'] ?? true,
            'isVerified': tutor['is_verified'] ?? false,
            'isAvailableNow': tutor['is_available_now'] ?? false,
            'description': tutor['bio'] ?? 'No description available',
            'languages': tutor['languages'] ?? ['Khmer'],
            'availability': tutor['availability'] ?? 'Contact for availability',
            'specializations': tutor['specializations'] ?? [],
            'education': tutor['education'] ?? 'Not specified',
          }).toList();

          _filteredTutors = List.from(_tutors);
          _isLoading = false;
        });
      } else {
        // Fallback to mock data if API fails
        _loadMockData();
      }
    } catch (e) {
      print('Error loading tutors: $e');
      // Fallback to mock data on error
      _loadMockData();
    }
  }

  void _loadMockData() {
    setState(() {
      _tutors = [
        {
          'id': '1',
          'name': 'Dr. Sarah Johnson',
          'subject': 'Mathematics',
          'location': 'Phnom Penh',
          'major': 'Mathematics',
          'gradeLevel': 'High School (Grade 10-12)',
          'rating': 4.9,
          'reviews': 127,
          'price': 25,
          'avatar': 'assets/images/avatar1.png',
          'experience': 'Expert (5-10 years)',
          'isOnline': true,
          'isOffline': true,
          'isVerified': true,
          'isAvailableNow': true,
          'description': 'Expert in calculus and algebra with PhD in Mathematics',
          'languages': ['English', 'Khmer'],
          'availability': 'Available Now',
          'specializations': ['Algebra', 'Calculus', 'Statistics'],
          'education': 'PhD Mathematics - Royal University of Phnom Penh',
        },
        {
          'id': '2',
          'name': 'Prof. Michael Chen',
          'subject': 'Physics',
          'location': 'Siem Reap',
          'major': 'Engineering',
          'gradeLevel': 'University/College',
          'rating': 4.8,
          'reviews': 89,
          'price': 30,
          'avatar': 'assets/images/avatar2.png',
          'experience': 'Master (10+ years)',
          'isOnline': false,
          'isOffline': true,
          'isVerified': true,
          'isAvailableNow': false,
          'description': 'Specialized in quantum physics and mechanics',
          'languages': ['English', 'Chinese'],
          'availability': 'Available Today',
          'specializations': ['Quantum Physics', 'Mechanics', 'Thermodynamics'],
          'education': 'PhD Physics - MIT',
        },
        {
          'id': '3',
          'name': 'Ms. Emily Davis',
          'subject': 'English',
          'location': 'Battambang',
          'major': 'Education',
          'gradeLevel': 'Middle School (Grade 7-9)',
          'rating': 4.7,
          'reviews': 156,
          'price': 20,
          'avatar': 'assets/images/avatar3.png',
          'experience': 'Experienced (2-5 years)',
          'isOnline': true,
          'isOffline': false,
          'isVerified': true,
          'isAvailableNow': true,
          'description': 'Native speaker specializing in academic writing',
          'languages': ['English', 'Khmer'],
          'availability': 'Available Now',
          'specializations': ['Grammar', 'Speaking', 'Writing'],
          'education': 'MA TESOL - University of Cambridge',
        },
        {
          'id': '4',
          'name': 'Dr. James Wilson',
          'subject': 'Chemistry',
          'rating': 4.6,
          'reviews': 73,
          'price': 28,
          'avatar': 'assets/images/avatar1.png',
          'experience': '10 years',
          'isOnline': true,
          'description': 'Organic chemistry specialist with research background',
          'languages': ['English', 'French'],
          'availability': 'Available in 2 hours',
        },
        {
          'id': '5',
          'name': 'Ms. Lisa Park',
          'subject': 'Biology',
          'rating': 4.8,
          'reviews': 94,
          'price': 22,
          'avatar': 'assets/images/avatar2.png',
          'experience': '7 years',
          'isOnline': false,
          'description': 'Molecular biology expert with teaching certification',
          'languages': ['English', 'Korean'],
          'availability': 'Available tomorrow',
        },
      ];
      
      _filteredTutors = List.from(_tutors);
      _isLoading = false;
    });
  }

  void _applyFilters() {
    setState(() {
      _filteredTutors = _tutors.where((tutor) {
        // Search filter
        if (_searchController.text.isNotEmpty) {
          final searchTerm = _searchController.text.toLowerCase();
          if (!tutor['name'].toLowerCase().contains(searchTerm) &&
              !tutor['subject'].toLowerCase().contains(searchTerm) &&
              !tutor['description'].toLowerCase().contains(searchTerm)) {
            return false;
          }
        }

        // Subject filter
        if (_selectedSubject != 'All' && tutor['subject'] != _selectedSubject) {
          return false;
        }

        // Location filter
        if (_selectedLocation != 'All') {
          if (_selectedLocation == 'Online Only') {
            if (!tutor['isOnline']) return false;
          } else if (tutor['location'] != _selectedLocation) {
            return false;
          }
        }

        // Major filter
        if (_selectedMajor != 'All' && tutor['major'] != _selectedMajor) {
          return false;
        }

        // Grade level filter
        if (_selectedGradeLevel != 'All' && tutor['gradeLevel'] != _selectedGradeLevel) {
          return false;
        }

        // Experience filter
        if (_selectedExperience != 'All' && tutor['experience'] != _selectedExperience) {
          return false;
        }

        // Language filter
        if (_selectedLanguage != 'All') {
          final languages = tutor['languages'] as List<String>;
          if (!languages.contains(_selectedLanguage)) {
            return false;
          }
        }

        // Availability filter
        if (_selectedAvailability != 'All') {
          if (_selectedAvailability == 'Available Now' && !tutor['isAvailableNow']) {
            return false;
          } else if (_selectedAvailability != 'Available Now' &&
                     tutor['availability'] != _selectedAvailability) {
            return false;
          }
        }

        // Price range filter
        if (_selectedPriceRange != 'All') {
          final price = tutor['price'] as int;
          switch (_selectedPriceRange) {
            case '\$5-10':
              if (price < 5 || price > 10) return false;
              break;
            case '\$10-15':
              if (price < 10 || price > 15) return false;
              break;
            case '\$15-20':
              if (price < 15 || price > 20) return false;
              break;
            case '\$20-25':
              if (price < 20 || price > 25) return false;
              break;
            case '\$25-30':
              if (price < 25 || price > 30) return false;
              break;
            case '\$30-40':
              if (price < 30 || price > 40) return false;
              break;
            case '\$40+':
              if (price < 40) return false;
              break;
          }
        }

        // Rating filter
        if (tutor['rating'] < _minRating) return false;

        // Online/Offline filters
        if (_isOnlineOnly && !tutor['isOnline']) return false;
        if (_isOfflineOnly && !tutor['isOffline']) return false;

        // Verified only filter
        if (_isVerifiedOnly && !tutor['isVerified']) return false;

        // Available now filter
        if (_isAvailableNow && !tutor['isAvailableNow']) return false;

        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Find Tutors',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: Colors.blue.shade700),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildQuickFilters(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildTutorsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Search tutors by name or subject...',
          hintStyle: TextStyle(color: Colors.grey.shade400),
          prefixIcon: Icon(Icons.search, color: Colors.blue.shade700),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: Colors.grey.shade600),
                  onPressed: () {
                    _searchController.clear();
                    _applyFilters();
                  },
                )
              : null,
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickFilters() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('All Subjects', _selectedSubject == 'All', () {
            setState(() {
              _selectedSubject = 'All';
            });
            _applyFilters();
          }),
          const SizedBox(width: 8),
          ..._subjects.skip(1).map((subject) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildFilterChip(subject, _selectedSubject == subject, () {
              setState(() {
                _selectedSubject = subject;
              });
              _applyFilters();
            }),
          )),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: Colors.white,
      selectedColor: Colors.blue.shade700,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? Colors.blue.shade700 : Colors.grey.shade300,
        ),
      ),
    );
  }

  Widget _buildTutorsList() {
    if (_filteredTutors.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No tutors found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredTutors.length,
      separatorBuilder: (_, __) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final tutor = _filteredTutors[index];
        return _buildTutorCard(tutor);
      },
    );
  }

  Widget _buildTutorCard(Map<String, dynamic> tutor) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => TutorProfileScreen(
                  name: tutor['name'],
                  subject: tutor['subject'],
                  avatar: tutor['avatar'],
                ),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Stack(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundImage: AssetImage(tutor['avatar']),
                          backgroundColor: Colors.blue.shade100,
                        ),
                        if (tutor['isOnline'])
                          Positioned(
                            right: 0,
                            bottom: 0,
                            child: Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tutor['name'],
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            tutor['subject'],
                            style: TextStyle(
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${tutor['experience']} experience',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.star, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${tutor['rating']}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        Text(
                          '(${tutor['reviews']} reviews)',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Text(
                          '\$${tutor['price']}/hr',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  tutor['description'],
                  style: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: tutor['isOnline'] ? Colors.green.shade50 : Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tutor['availability'],
                        style: TextStyle(
                          fontSize: 12,
                          color: tutor['isOnline'] ? Colors.green.shade700 : Colors.orange.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton(
                      onPressed: () {
                        // TODO: Navigate to booking
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Booking feature coming soon!')),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade700,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                      ),
                      child: const Text('Book Now'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    const Text(
                      'Find Your Perfect Tutor',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        setModalState(() {
                          _resetAllFilters();
                        });
                      },
                      child: Text(
                        'Reset All',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ),
                  ],
                ),
              ),

              // Progressive Filter Steps
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Step 1: Location Filter
                      _buildFilterStep(
                        stepNumber: 1,
                        title: 'Choose Location',
                        subtitle: 'Where do you want to study?',
                        child: _buildLocationFilter(setModalState),
                      ),

                      const SizedBox(height: 24),

                      // Step 2: Grade Level Filter (only show if location selected)
                      if (_selectedLocation != 'All')
                        _buildFilterStep(
                          stepNumber: 2,
                          title: 'Select Grade Level',
                          subtitle: 'What grade level do you need help with?',
                          child: _buildGradeLevelFilter(setModalState),
                        ),

                      if (_selectedLocation != 'All') const SizedBox(height: 24),

                      // Step 3: Major/Subject Filter (only show if grade selected)
                      if (_selectedLocation != 'All' && _selectedGradeLevel != 'All')
                        _buildFilterStep(
                          stepNumber: 3,
                          title: 'Choose Subject',
                          subtitle: 'What subject do you need tutoring in?',
                          child: _buildSubjectFilter(setModalState),
                        ),

                      if (_selectedLocation != 'All' && _selectedGradeLevel != 'All')
                        const SizedBox(height: 24),

                      // Step 4: Additional Filters (only show if subject selected)
                      if (_selectedLocation != 'All' &&
                          _selectedGradeLevel != 'All' &&
                          _selectedSubject != 'All')
                        _buildFilterStep(
                          stepNumber: 4,
                          title: 'Refine Your Search',
                          subtitle: 'Additional preferences',
                          child: _buildAdditionalFilters(setModalState),
                        ),

                      const SizedBox(height: 100), // Space for apply button
                    ],
                  ),
                ),
              ),

              // Apply Button
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // Apply filters after dialog closes to avoid setState during build
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (mounted) {
                              _applyFilters();
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade700,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          'Apply Filters (${_getFilteredCount()})',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Subject filter
              const Text('Subject', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _subjects.map((subject) => FilterChip(
                  label: Text(subject),
                  selected: _selectedSubject == subject,
                  onSelected: (selected) {
                    setModalState(() {
                      _selectedSubject = subject;
                    });
                  },
                )).toList(),
              ),

              const SizedBox(height: 24),

              // Price range filter
              const Text('Price Range', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _priceRanges.map((range) => FilterChip(
                  label: Text(range),
                  selected: _selectedPriceRange == range,
                  onSelected: (selected) {
                    setModalState(() {
                      _selectedPriceRange = range;
                    });
                  },
                )).toList(),
              ),

              const SizedBox(height: 24),

              // Rating filter
              Text('Minimum Rating: ${_minRating.toStringAsFixed(1)}',
                   style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              Slider(
                value: _minRating,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                onChanged: (value) {
                  setModalState(() {
                    _minRating = value;
                  });
                },
              ),

              const SizedBox(height: 16),

              // Online only filter
              CheckboxListTile(
                title: const Text('Online tutors only'),
                value: _isOnlineOnly,
                onChanged: (value) {
                  setModalState(() {
                    _isOnlineOnly = value ?? false;
                  });
                },
              ),

              const SizedBox(height: 24),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Apply Filters',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to build filter steps
  Widget _buildFilterStep({
    required int stepNumber,
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.blue.shade700,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    stepNumber.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  // Location Filter
  Widget _buildLocationFilter(StateSetter setModalState) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _locations.map((location) {
        final isSelected = _selectedLocation == location;
        return FilterChip(
          label: Text(location),
          selected: isSelected,
          onSelected: (_) {
            setModalState(() {
              _selectedLocation = location;
              // Reset dependent filters when location changes
              if (location == 'All') {
                _selectedGradeLevel = 'All';
                _selectedSubject = 'All';
              }
            });
          },
          backgroundColor: Colors.white,
          selectedColor: Colors.blue.shade700,
          labelStyle: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        );
      }).toList(),
    );
  }

  // Grade Level Filter
  Widget _buildGradeLevelFilter(StateSetter setModalState) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _gradeLevels.map((grade) {
        final isSelected = _selectedGradeLevel == grade;
        return FilterChip(
          label: Text(grade),
          selected: isSelected,
          onSelected: (_) {
            setModalState(() {
              _selectedGradeLevel = grade;
              // Reset dependent filters when grade changes
              if (grade == 'All') {
                _selectedSubject = 'All';
              }
            });
          },
          backgroundColor: Colors.white,
          selectedColor: Colors.blue.shade700,
          labelStyle: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        );
      }).toList(),
    );
  }

  // Subject Filter
  Widget _buildSubjectFilter(StateSetter setModalState) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _subjects.map((subject) {
        final isSelected = _selectedSubject == subject;
        return FilterChip(
          label: Text(subject),
          selected: isSelected,
          onSelected: (_) {
            setModalState(() {
              _selectedSubject = subject;
            });
          },
          backgroundColor: Colors.white,
          selectedColor: Colors.blue.shade700,
          labelStyle: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        );
      }).toList(),
    );
  }

  // Additional Filters
  Widget _buildAdditionalFilters(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Price Range
        const Text('Price Range', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _priceRanges.map((price) {
            final isSelected = _selectedPriceRange == price;
            return FilterChip(
              label: Text(price),
              selected: isSelected,
              onSelected: (_) {
                setModalState(() {
                  _selectedPriceRange = price;
                });
              },
              backgroundColor: Colors.white,
              selectedColor: Colors.blue.shade700,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
            );
          }).toList(),
        ),

        const SizedBox(height: 16),

        // Rating Filter
        Text('Minimum Rating: ${_minRating.toStringAsFixed(1)} stars'),
        Slider(
          value: _minRating,
          min: 0.0,
          max: 5.0,
          divisions: 10,
          onChanged: (value) {
            setModalState(() {
              _minRating = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // Toggle Filters
        CheckboxListTile(
          title: const Text('Online tutors only'),
          value: _isOnlineOnly,
          onChanged: (value) {
            setModalState(() {
              _isOnlineOnly = value ?? false;
              if (_isOnlineOnly) _isOfflineOnly = false;
            });
          },
        ),

        CheckboxListTile(
          title: const Text('Verified tutors only'),
          value: _isVerifiedOnly,
          onChanged: (value) {
            setModalState(() {
              _isVerifiedOnly = value ?? false;
            });
          },
        ),

        CheckboxListTile(
          title: const Text('Available now'),
          value: _isAvailableNow,
          onChanged: (value) {
            setModalState(() {
              _isAvailableNow = value ?? false;
            });
          },
        ),
      ],
    );
  }

  // Get filtered count for button (without calling setState)
  int _getFilteredCount() {
    // Calculate count without calling setState
    int count = _tutors.where((tutor) {
      // Search filter
      if (_searchController.text.isNotEmpty) {
        final searchTerm = _searchController.text.toLowerCase();
        if (!tutor['name'].toLowerCase().contains(searchTerm) &&
            !tutor['subject'].toLowerCase().contains(searchTerm) &&
            !tutor['description'].toLowerCase().contains(searchTerm)) {
          return false;
        }
      }

      // Subject filter
      if (_selectedSubject != 'All' && tutor['subject'] != _selectedSubject) {
        return false;
      }

      // Location filter
      if (_selectedLocation != 'All') {
        if (_selectedLocation == 'Online Only') {
          if (!tutor['isOnline']) return false;
        } else if (tutor['location'] != _selectedLocation) {
          return false;
        }
      }

      // Other filters...
      return true;
    }).length;

    return count;
  }
}
