import 'package:flutter/material.dart';
import '../services/api_client.dart';

class TutorAddContentScreen extends StatefulWidget {
  const TutorAddContentScreen({super.key});

  @override
  State<TutorAddContentScreen> createState() => _TutorAddContentScreenState();
}

class _TutorAddContentScreenState extends State<TutorAddContentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  String _selectedPostType = 'article';
  int? _selectedCategoryId;
  bool _isLoading = false;
  String? _errorMessage;
  List<Map<String, dynamic>> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final ApiClient apiClient = ApiClient();
      final response = await apiClient.get('/categories');

      if (!mounted) return;

      if (response.success && response.data != null) {
        // Handle both List<dynamic> and List<Map<String, dynamic>>
        final List<dynamic> rawData = response.data is List
            ? response.data
            : [response.data];

        final List<Map<String, dynamic>> categories = rawData
            .whereType<Map<String, dynamic>>()
            .toList();

        setState(() {
          _categories = categories;
          if (_categories.isNotEmpty) {
            // Safely convert the first category ID to int
            final firstCategory = _categories[0];
            final firstCategoryId = firstCategory['id'];

            if (firstCategoryId != null) {
              _selectedCategoryId = firstCategoryId is int
                  ? firstCategoryId
                  : int.tryParse(firstCategoryId.toString());
            }
          }
        });
      } else {
        setState(() => _errorMessage = response.message ?? 'Failed to load categories');
      }
    } catch (e) {
      if (mounted) {
        setState(() => _errorMessage = 'Error loading categories: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _submitContent() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    try {
      final ApiClient apiClient = ApiClient();
      final response = await apiClient.post<Map<String, dynamic>>(
        '/tutor/posts',
        data: {
          'title': _titleController.text,
          'content': _contentController.text,
          'post_type': _selectedPostType,
          'category_id': _selectedCategoryId,
          'is_published': true,
        },
      );
      
      if (response.success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Content published successfully!')),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          setState(() => _errorMessage = response.message ?? 'Failed to publish content');
        }
      }
    } catch (e) {
      setState(() => _errorMessage = 'Error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Educational Content'),
      ),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_errorMessage != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => 
                      value == null || value.isEmpty ? 'Please enter a title' : null,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedPostType,
                    decoration: const InputDecoration(
                      labelText: 'Content Type',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      'article', 'tutorial', 'announcement', 
                      'resource', 'tip'
                    ].map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.toUpperCase()),
                    )).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedPostType = value);
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: _selectedCategoryId,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                      hintText: 'Select a category',
                    ),
                    items: _categories.isEmpty
                        ? [
                            const DropdownMenuItem<int>(
                              value: null,
                              child: Text('No categories available'),
                            )
                          ]
                        : _categories.map((category) => DropdownMenuItem<int>(
                            value: category['id'] is int
                                ? category['id']
                                : int.tryParse(category['id'].toString()),
                            child: Text(category['name']?.toString() ?? 'Unknown Category'),
                          )).toList(),
                    onChanged: _categories.isEmpty
                        ? null
                        : (value) {
                            setState(() => _selectedCategoryId = value);
                          },
                    validator: (value) {
                      if (_categories.isEmpty) {
                        return 'No categories available. Please try again later.';
                      }
                      if (value == null) {
                        return 'Please select a category';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _contentController,
                    decoration: const InputDecoration(
                      labelText: 'Content',
                      border: OutlineInputBorder(),
                      alignLabelWithHint: true,
                    ),
                    maxLines: 10,
                    validator: (value) => 
                      value == null || value.isEmpty ? 'Please enter content' : null,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _submitContent,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('PUBLISH CONTENT'),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }
}