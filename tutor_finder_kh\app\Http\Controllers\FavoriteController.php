<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Favorite;
use App\Models\Tutor;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class FavoriteController extends Controller
{
    /**
     * Get user's favorite tutors.
     */
    public function index(Request $request): JsonResponse
    {
        $student = $request->user();
        
        $favorites = $student->favorites()
                            ->with([
                                'tutor' => function ($query) {
                                    $query->with(['categories'])
                                          ->withCount(['reviews as review_count' => function ($q) {
                                              $q->where('is_approved', true);
                                          }])
                                          ->withAvg(['reviews as average_rating' => function ($q) {
                                              $q->where('is_approved', true);
                                          }], 'rating');
                                }
                            ])
                            ->latest()
                            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $favorites
        ]);
    }

    /**
     * Add a tutor to favorites.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'tutor_id' => 'required|integer|exists:tutors,id'
        ]);

        $student = $request->user();
        $tutorId = $request->tutor_id;

        // Check if tutor is approved
        $tutor = Tutor::approved()->find($tutorId);
        if (!$tutor) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found or not approved'
            ], 404);
        }

        // Check if already favorited
        if ($student->hasFavorited($tutorId)) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor is already in favorites'
            ], 409);
        }

        $favorite = $student->favorites()->create([
            'tutor_id' => $tutorId
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Tutor added to favorites',
            'data' => $favorite->load('tutor')
        ], 201);
    }

    /**
     * Remove a tutor from favorites.
     */
    public function destroy(Request $request, int $tutorId): JsonResponse
    {
        $student = $request->user();
        
        $favorite = $student->favorites()->where('tutor_id', $tutorId)->first();
        
        if (!$favorite) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found in favorites'
            ], 404);
        }

        $favorite->delete();

        return response()->json([
            'success' => true,
            'message' => 'Tutor removed from favorites'
        ]);
    }

    /**
     * Check if a tutor is favorited.
     */
    public function check(Request $request, int $tutorId): JsonResponse
    {
        $student = $request->user();
        $isFavorited = $student->hasFavorited($tutorId);

        return response()->json([
            'success' => true,
            'data' => [
                'tutor_id' => $tutorId,
                'is_favorited' => $isFavorited
            ]
        ]);
    }

    /**
     * Get favorite tutors count.
     */
    public function count(Request $request): JsonResponse
    {
        $student = $request->user();
        $count = $student->favorites()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'favorites_count' => $count
            ]
        ]);
    }
}
