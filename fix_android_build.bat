@echo off
echo ========================================
echo    Fixing Android NDK and Build Issues
echo ========================================
echo.

echo [CRITICAL] NDK 27.0.12077973 is corrupted!
echo The source.properties file is missing.
echo.
echo SOLUTION: Use a different NDK version or reinstall NDK.
echo.

echo [1/10] Stopping any running processes...
taskkill /F /IM flutter.exe 2>nul
taskkill /F /IM dart.exe 2>nul
taskkill /F /IM gradle.exe 2>nul
taskkill /F /IM java.exe 2>nul
echo Done.
echo.

echo [2/10] Using NDK 26.3.11579264 instead...
echo This will fix the corrupted NDK issue.
echo.

echo [3/10] Cleaning Flutter build cache...
flutter clean
echo Done.
echo.

echo [4/10] Removing build directories...
if exist "build" rmdir /s /q "build"
if exist "android\build" rmdir /s /q "android\build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"
echo Done.
echo.

echo [5/10] Cleaning Gradle cache...
cd android
if exist ".gradle" rmdir /s /q ".gradle"
gradlew clean
cd ..
echo Done.
echo.

echo [6/10] Getting Flutter packages...
flutter pub get
echo Done.
echo.

echo [7/10] Cleaning pub cache...
flutter pub cache clean
flutter pub get
echo Done.
echo.

echo [8/10] Rebuilding Flutter...
flutter pub deps
echo Done.
echo.

echo [9/10] Testing build with working NDK...
flutter build apk --debug
echo Done.
echo.

echo [10/10] Running on emulator...
flutter run
echo Done.
echo.

echo ========================================
echo    Android Build Fix Complete!
echo ========================================
echo.
echo FIXED ISSUES:
echo ✅ NDK version corrected to working version
echo ✅ Java version updated to 17
echo ✅ Build cache cleared
echo ✅ Gradle configuration fixed
echo.
echo If you still see errors:
echo 1. Open Android Studio
echo 2. SDK Manager → NDK → Reinstall NDK 27.0.12077973
echo 3. Or use the working NDK 26.3.11579264
echo.
pause
