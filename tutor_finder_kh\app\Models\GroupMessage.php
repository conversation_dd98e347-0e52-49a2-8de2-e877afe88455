<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class GroupMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'group_id',
        'sender_id',
        'sender_type',
        'message',
        'message_type',
        'file_path',
        'reply_to_id',
    ];

    /**
     * Get the group this message belongs to.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the sender (polymorphic).
     */
    public function sender(): MorphTo
    {
        return $this->morphTo('sender', 'sender_type', 'sender_id');
    }

    /**
     * Get the message this is replying to.
     */
    public function replyTo(): BelongsTo
    {
        return $this->belongsTo(GroupMessage::class, 'reply_to_id');
    }

    /**
     * Get replies to this message.
     */
    public function replies()
    {
        return $this->hasMany(GroupMessage::class, 'reply_to_id');
    }

    /**
     * Check if message is from student.
     */
    public function getIsFromStudentAttribute(): bool
    {
        return $this->sender_type === 'student';
    }

    /**
     * Check if message is from tutor.
     */
    public function getIsFromTutorAttribute(): bool
    {
        return $this->sender_type === 'tutor';
    }

    /**
     * Check if message is a reply.
     */
    public function getIsReplyAttribute(): bool
    {
        return $this->reply_to_id !== null;
    }

    /**
     * Get sender name.
     */
    public function getSenderNameAttribute(): string
    {
        return $this->sender->name ?? 'Unknown User';
    }

    /**
     * Scope for text messages.
     */
    public function scopeTextMessages($query)
    {
        return $query->where('message_type', 'text');
    }

    /**
     * Scope for file messages.
     */
    public function scopeFileMessages($query)
    {
        return $query->where('message_type', 'file');
    }

    /**
     * Scope for image messages.
     */
    public function scopeImageMessages($query)
    {
        return $query->where('message_type', 'image');
    }
}
