import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_response.dart';
import '../models/auth.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiClient _apiClient = ApiClient();

  // Register student
  Future<ApiResponse<AuthResponse>> registerStudent(StudentRegisterRequest request) async {
    return await _apiClient.post<AuthResponse>(
      ApiConfig.registerStudent,
      data: request.toJson(),
      fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  // Register tutor
  Future<ApiResponse<AuthResponse>> registerTutor(TutorRegisterRequest request) async {
    return await _apiClient.post<AuthResponse>(
      ApiConfig.registerTutor,
      data: request.toJson(),
      fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  // Login
  Future<ApiResponse<AuthResponse>> login(LoginRequest request) async {
    final response = await _apiClient.post<AuthResponse>(
      ApiConfig.login,
      data: request.toJson(),
      fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
    );

    // Save auth data if login successful
    if (response.isSuccess && response.data != null) {
      await _saveAuthData(response.data!);
    }

    return response;
  }

  // Logout
  Future<ApiResponse<void>> logout() async {
    final response = await _apiClient.post<void>(ApiConfig.logout);
    
    // Clear local auth data regardless of API response
    await _clearAuthData();
    
    return response;
  }

  // Get profile
  Future<ApiResponse<AuthResponse>> getProfile() async {
    return await _apiClient.get<AuthResponse>(
      ApiConfig.profile,
      fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  // Update profile
  Future<ApiResponse<AuthResponse>> updateProfile(UpdateProfileRequest request) async {
    final response = await _apiClient.put<AuthResponse>(
      ApiConfig.profile,
      data: request.toJson(),
      fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
    );

    // Update local user data if successful
    if (response.isSuccess && response.data != null) {
      await _updateLocalUserData(response.data!);
    }

    return response;
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _apiClient.isLoggedIn();
  }

  // Get current user type
  Future<String?> getCurrentUserType() async {
    return await _apiClient.getUserType();
  }

  // Get current user data
  Future<AuthResponse?> getCurrentUser() async {
    try {
      final userDataJson = await _apiClient.getUserData();
      if (userDataJson != null) {
        final userData = jsonDecode(userDataJson);
        return AuthResponse.fromJson(userData);
      }
    } catch (e) {
      print('Error getting current user: $e');
    }
    return null;
  }

  // Get current student (if logged in as student)
  Future<Student?> getCurrentStudent() async {
    final user = await getCurrentUser();
    return user?.studentUser;
  }

  // Get current tutor (if logged in as tutor)
  Future<Tutor?> getCurrentTutor() async {
    final user = await getCurrentUser();
    return user?.tutorUser;
  }

  // Get current user ID
  Future<int?> getCurrentUserId() async {
    final user = await getCurrentUser();
    return user?.userId;
  }

  // Get current user name
  Future<String?> getCurrentUserName() async {
    final user = await getCurrentUser();
    return user?.userName;
  }

  // Get current user email
  Future<String?> getCurrentUserEmail() async {
    final user = await getCurrentUser();
    return user?.userEmail;
  }

  // Refresh token (if needed)
  Future<bool> refreshToken() async {
    try {
      final response = await getProfile();
      if (response.isSuccess && response.data != null) {
        await _updateLocalUserData(response.data!);
        return true;
      }
    } catch (e) {
      print('Error refreshing token: $e');
    }
    return false;
  }

  // Private methods
  Future<void> _saveAuthData(AuthResponse authResponse) async {
    try {
      await _apiClient.setToken(authResponse.token);
      await _apiClient.setUserType(authResponse.userType);
      await _apiClient.setUserData(jsonEncode(authResponse.toJson()));

      // Also save individual user data fields for profile screens
      await _saveIndividualUserData(authResponse);
    } catch (e) {
      print('Error saving auth data: $e');
    }
  }

  Future<void> _updateLocalUserData(AuthResponse authResponse) async {
    try {
      await _apiClient.setUserData(jsonEncode(authResponse.toJson()));
      // Also update individual user data fields for profile screens
      await _saveIndividualUserData(authResponse);
    } catch (e) {
      print('Error updating local user data: $e');
    }
  }

  Future<void> _saveIndividualUserData(AuthResponse authResponse) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save basic user info
      await prefs.setString('fullname', authResponse.userName);
      await prefs.setString('email', authResponse.userEmail ?? '');

      // Save user type specific data
      if (authResponse.isStudent) {
        final student = authResponse.studentUser;
        if (student != null) {
          await prefs.setString('phone', student.phone ?? '');
          await prefs.setString('location', student.location ?? '');
          await prefs.setString('bio', student.bio ?? '');
          if (student.dateOfBirth != null) {
            await prefs.setString('birthdate', student.dateOfBirth!);
          }
          if (student.gender != null) {
            await prefs.setString('gender', student.gender!);
          }
        }
      } else if (authResponse.isTutor) {
        final tutor = authResponse.tutorUser;
        if (tutor != null) {
          await prefs.setString('phone', tutor.phone ?? '');
          await prefs.setString('bio', tutor.description ?? ''); // Tutor uses 'description' field
          await prefs.setString('location', tutor.location ?? '');
          // Tutor model doesn't have dateOfBirth and gender fields
          // These would be in the User model, not the Tutor profile
        }
      }
    } catch (e) {
      print('Error saving individual user data: $e');
    }
  }

  Future<void> _clearAuthData() async {
    try {
      await _apiClient.clearAllData();
    } catch (e) {
      print('Error clearing auth data: $e');
    }
  }

  // Validation helpers
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }
    
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    
    return null;
  }

  static String? validateName(String? name) {
    if (name == null || name.isEmpty) {
      return 'Name is required';
    }
    
    if (name.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    
    return null;
  }

  static String? validatePhone(String? phone) {
    if (phone != null && phone.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
      if (!phoneRegex.hasMatch(phone)) {
        return 'Please enter a valid phone number';
      }
    }
    return null;
  }

  static String? validatePasswordConfirmation(String? password, String? confirmation) {
    if (confirmation == null || confirmation.isEmpty) {
      return 'Password confirmation is required';
    }
    
    if (password != confirmation) {
      return 'Passwords do not match';
    }
    
    return null;
  }
}
