<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Student;
use App\Models\Tutor;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Register a new student.
     */
    public function registerStudent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'user_type' => 'student',
        ]);

        // Create student profile
        $student = Student::create([
            'user_id' => $user->id,
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Student registered successfully',
            'data' => [
                'user' => $user,
                'token' => $token,
                'user_type' => 'student'
            ]
        ], 201);
    }

    /**
     * Register a new tutor.
     */
    public function registerTutor(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:50',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'hourly_rate' => 'nullable|numeric|min:0',
            'experience_years' => 'nullable|integer|min:0',
            'qualifications' => 'nullable|string',
            'languages' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'user_type' => 'tutor',
        ]);

        // Create tutor profile
        $tutor = Tutor::create([
            'user_id' => $user->id,
            'description' => $request->description,
            'location' => $request->location,
            'hourly_rate' => $request->hourly_rate,
            'experience_years' => $request->experience_years,
            'qualifications' => $request->qualifications,
            'languages' => $request->languages,
            'status' => 'pending', // Requires admin approval
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Tutor registered successfully. Account pending approval.',
            'data' => [
                'user' => $user,
                'token' => $token,
                'user_type' => 'tutor'
            ]
        ], 201);
    }

    /**
     * Login user (student or tutor).
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
            'user_type' => 'required|in:student,tutor',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $userType = $request->user_type;
        $model = $userType === 'student' ? Student::class : Tutor::class;
        
        $user = $model::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if tutor is approved
        if ($userType === 'tutor' && $user->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'Account is pending approval or has been rejected'
            ], 403);
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'token' => $token,
                'user_type' => $userType
            ]
        ]);
    }

    /**
     * Logout user.
     */
    public function logout(Request $request): JsonResponse
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Get authenticated user profile.
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user();
        $userType = $user instanceof Student ? 'student' : 'tutor';

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'user_type' => $userType
            ]
        ]);
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = $request->user();
        $userType = $user instanceof Student ? 'student' : 'tutor';

        $rules = [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:50',
        ];

        if ($userType === 'student') {
            $rules = array_merge($rules, [
                'date_of_birth' => 'sometimes|nullable|date',
                'gender' => 'sometimes|nullable|in:male,female,other',
                'location' => 'sometimes|nullable|string|max:255',
                'bio' => 'sometimes|nullable|string',
            ]);
        } else {
            $rules = array_merge($rules, [
                'description' => 'sometimes|nullable|string',
                'location' => 'sometimes|nullable|string|max:255',
                'hourly_rate' => 'sometimes|nullable|numeric|min:0',
                'experience_years' => 'sometimes|nullable|integer|min:0',
                'qualifications' => 'sometimes|nullable|string',
                'languages' => 'sometimes|nullable|array',
                'is_online' => 'sometimes|boolean',
            ]);
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($request->only(array_keys($rules)));

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'user' => $user->fresh(),
                'user_type' => $userType
            ]
        ]);
    }
}
