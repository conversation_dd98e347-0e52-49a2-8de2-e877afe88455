<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\Conversation;
use App\Models\Group;
use App\Models\GroupMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class MessageController extends Controller
{
    /**
     * Get user's conversations
     */
    public function conversations(Request $request)
    {
        try {
            $user = $request->user();
            
            $conversations = Conversation::where('user1_id', $user->id)
                ->orWhere('user2_id', $user->id)
                ->with(['user1', 'user2', 'lastMessage'])
                ->orderBy('updated_at', 'desc')
                ->paginate(20);

            // Format conversations for the response
            $formattedConversations = $conversations->getCollection()->map(function ($conversation) use ($user) {
                $otherUser = $conversation->user1_id === $user->id ? $conversation->user2 : $conversation->user1;
                
                return [
                    'id' => $conversation->id,
                    'other_user' => [
                        'id' => $otherUser->id,
                        'name' => $otherUser->name,
                        'user_type' => $otherUser->user_type,
                        'avatar' => $otherUser->avatar,
                    ],
                    'last_message' => $conversation->lastMessage ? [
                        'id' => $conversation->lastMessage->id,
                        'message' => $conversation->lastMessage->message,
                        'sender_id' => $conversation->lastMessage->sender_id,
                        'created_at' => $conversation->lastMessage->created_at,
                        'is_read' => $conversation->lastMessage->is_read,
                    ] : null,
                    'unread_count' => $conversation->messages()
                        ->where('receiver_id', $user->id)
                        ->where('is_read', false)
                        ->count(),
                    'updated_at' => $conversation->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedConversations,
                'pagination' => [
                    'current_page' => $conversations->currentPage(),
                    'last_page' => $conversations->lastPage(),
                    'per_page' => $conversations->perPage(),
                    'total' => $conversations->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch conversations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get messages for a specific conversation
     */
    public function getConversation(Request $request, $otherUserId, $otherUserType)
    {
        try {
            $user = $request->user();
            
            // Find or create conversation
            $conversation = $this->findOrCreateConversation($user->id, $otherUserId);
            
            $messages = Message::where('conversation_id', $conversation->id)
                ->with(['sender', 'receiver'])
                ->orderBy('created_at', 'asc')
                ->paginate(50);

            // Mark messages as read
            Message::where('conversation_id', $conversation->id)
                ->where('receiver_id', $user->id)
                ->where('is_read', false)
                ->update(['is_read' => true]);

            return response()->json([
                'success' => true,
                'data' => $messages->items(),
                'pagination' => [
                    'current_page' => $messages->currentPage(),
                    'last_page' => $messages->lastPage(),
                    'per_page' => $messages->perPage(),
                    'total' => $messages->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch conversation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a message
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required|integer|exists:users,id',
            'receiver_type' => 'required|string|in:student,tutor',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $receiverId = $request->receiver_id;
            
            // Check if receiver exists and has the correct user type
            $receiver = User::where('id', $receiverId)
                ->where('user_type', $request->receiver_type)
                ->first();

            if (!$receiver) {
                return response()->json([
                    'success' => false,
                    'message' => 'Receiver not found or invalid user type'
                ], 404);
            }

            // Find or create conversation
            $conversation = $this->findOrCreateConversation($user->id, $receiverId);

            // Create message
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'receiver_id' => $receiverId,
                'message' => $request->message,
                'is_read' => false,
            ]);

            // Update conversation timestamp
            $conversation->touch();

            // Load message with sender and receiver data
            $message->load(['sender', 'receiver']);

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark messages as read
     */
    public function markAsRead(Request $request, $conversationId)
    {
        try {
            $user = $request->user();

            // Verify user is part of this conversation
            $conversation = Conversation::where('id', $conversationId)
                ->where(function ($query) use ($user) {
                    $query->where('user1_id', $user->id)
                          ->orWhere('user2_id', $user->id);
                })
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found'
                ], 404);
            }

            // Mark messages as read
            $updatedCount = Message::where('conversation_id', $conversationId)
                ->where('receiver_id', $user->id)
                ->where('is_read', false)
                ->update(['is_read' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Messages marked as read',
                'data' => [
                    'updated_count' => $updatedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark messages as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread messages count
     */
    public function getUnreadCount(Request $request)
    {
        try {
            $user = $request->user();

            $unreadCount = Message::where('receiver_id', $user->id)
                ->where('is_read', false)
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'unread_count' => $unreadCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get unread count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a message (test version without authentication)
     */
    public function sendMessageTest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required|integer',
            'receiver_type' => 'required|string|in:student,tutor',
            'sender_type' => 'required|string|in:student,tutor',
            'sender_id' => 'integer',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Use provided sender info or create mock IDs based on sender type
            $senderType = $request->sender_type;
            $senderId = $request->sender_id ?? ($senderType === 'tutor' ? 888 : 999);
            $receiverId = $request->receiver_id;

            // Create a simple message record (without full conversation logic)
            $messageData = [
                'sender_id' => $senderId,
                'receiver_id' => $receiverId,
                'sender_type' => $senderType,
                'receiver_type' => $request->receiver_type,
                'message' => $request->message,
                'message_type' => $request->message_type ?? 'text',
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Try to insert into messages table if it exists
            try {
                DB::table('messages')->insert($messageData);

                return response()->json([
                    'success' => true,
                    'message' => 'Message sent successfully to database',
                    'data' => array_merge($messageData, ['id' => DB::getPdo()->lastInsertId()])
                ], 201);

            } catch (\Exception $dbError) {
                // If database insert fails, return success anyway for testing
                return response()->json([
                    'success' => true,
                    'message' => 'Message processed (database not available)',
                    'data' => $messageData,
                    'note' => 'Database error: ' . $dbError->getMessage()
                ], 201);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test method for tutor sending message to student
     */
    public function tutorSendMessageTest(Request $request)
    {
        // Force sender to be tutor type
        $request->merge([
            'sender_type' => 'tutor',
            'sender_id' => 888, // Mock tutor ID
        ]);

        // Call the main test method
        return $this->sendMessageTest($request);
    }

    /**
     * Delete a message
     */
    public function deleteMessage(Request $request, $messageId)
    {
        try {
            $user = $request->user();

            $message = Message::where('id', $messageId)
                ->where('sender_id', $user->id)
                ->first();

            if (!$message) {
                return response()->json([
                    'success' => false,
                    'message' => 'Message not found or you are not authorized to delete it'
                ], 404);
            }

            $message->delete();

            return response()->json([
                'success' => true,
                'message' => 'Message deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Find or create conversation between two users
     */
    private function findOrCreateConversation($user1Id, $user2Id)
    {
        // Ensure consistent ordering (smaller ID first)
        if ($user1Id > $user2Id) {
            $temp = $user1Id;
            $user1Id = $user2Id;
            $user2Id = $temp;
        }

        return Conversation::firstOrCreate([
            'user1_id' => $user1Id,
            'user2_id' => $user2Id,
        ]);
    }

    /**
     * Get group messages
     */
    public function getGroupMessages(Request $request, int $groupId)
    {
        try {
            $user = $request->user();

            // Check if user is a member of the group
            $group = Group::with('activeMembers')->find($groupId);
            if (!$group || !$group->hasMember($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied or group not found'
                ], 403);
            }

            $messages = GroupMessage::with(['sender', 'replyTo'])
                ->where('group_id', $groupId)
                ->orderBy('created_at', 'asc')
                ->paginate(50);

            $formattedMessages = $messages->getCollection()->map(function ($message) {
                return [
                    'id' => $message->id,
                    'group_id' => $message->group_id,
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->name,
                        'type' => $message->sender_type,
                        'avatar' => $message->sender->avatar ?? null,
                    ],
                    'message' => $message->message,
                    'message_type' => $message->message_type,
                    'file_path' => $message->file_path,
                    'reply_to' => $message->replyTo ? [
                        'id' => $message->replyTo->id,
                        'message' => $message->replyTo->message,
                        'sender_name' => $message->replyTo->sender_name,
                    ] : null,
                    'created_at' => $message->created_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedMessages,
                'pagination' => [
                    'current_page' => $messages->currentPage(),
                    'last_page' => $messages->lastPage(),
                    'per_page' => $messages->perPage(),
                    'total' => $messages->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch group messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a message to a group
     */
    public function sendGroupMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_id' => 'required|integer|exists:groups,id',
            'message' => 'required|string|max:1000',
            'message_type' => 'in:text,image,file',
            'reply_to_id' => 'nullable|integer|exists:group_messages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $groupId = $request->group_id;

            // Check if user is a member of the group
            $group = Group::find($groupId);
            if (!$group || !$group->hasMember($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied or group not found'
                ], 403);
            }

            $message = GroupMessage::create([
                'group_id' => $groupId,
                'sender_id' => $user->id,
                'sender_type' => $user->user_type,
                'message' => $request->message,
                'message_type' => $request->message_type ?? 'text',
                'reply_to_id' => $request->reply_to_id,
            ]);

            $message->load(['sender', 'replyTo']);

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'id' => $message->id,
                    'group_id' => $message->group_id,
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->name,
                        'type' => $message->sender_type,
                        'avatar' => $message->sender->avatar ?? null,
                    ],
                    'message' => $message->message,
                    'message_type' => $message->message_type,
                    'file_path' => $message->file_path,
                    'reply_to' => $message->replyTo ? [
                        'id' => $message->replyTo->id,
                        'message' => $message->replyTo->message,
                        'sender_name' => $message->replyTo->sender_name,
                    ] : null,
                    'created_at' => $message->created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's groups (for group list)
     */
    public function getUserGroups(Request $request)
    {
        try {
            $user = $request->user();

            $groups = Group::whereHas('members', function ($query) use ($user) {
                $query->where('user_id', $user->id)->where('group_members.is_active', true);
            })
            ->with(['creator', 'messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->where('groups.is_active', true)
            ->orderBy('updated_at', 'desc')
            ->get();

            $formattedGroups = $groups->map(function ($group) use ($user) {
                $lastMessage = $group->messages->first();

                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description,
                    'subject' => $group->subject,
                    'group_type' => $group->group_type,
                    'member_count' => $group->member_count,
                    'image' => $group->image,
                    'creator' => [
                        'id' => $group->creator->id,
                        'name' => $group->creator->name,
                        'type' => $group->creator_type,
                    ],
                    'last_message' => $lastMessage ? [
                        'id' => $lastMessage->id,
                        'message' => $lastMessage->message,
                        'sender_name' => $lastMessage->sender_name,
                        'created_at' => $lastMessage->created_at,
                    ] : null,
                    'updated_at' => $group->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedGroups
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch user groups',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
