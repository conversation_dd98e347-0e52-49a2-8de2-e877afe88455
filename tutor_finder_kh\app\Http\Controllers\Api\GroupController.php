<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\GroupMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;

class GroupController extends Controller
{
    /**
     * Get all groups (with pagination and filters)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Group::with(['creator', 'members'])
                ->active()
                ->orderBy('created_at', 'desc');

            // Filter by subject
            if ($request->has('subject')) {
                $query->bySubject($request->subject);
            }

            // Filter by creator type
            if ($request->has('creator_type')) {
                $query->where('creator_type', $request->creator_type);
            }

            // Search by name
            if ($request->has('search')) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }

            $groups = $query->paginate(20);

            // Format response
            $formattedGroups = $groups->getCollection()->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description,
                    'subject' => $group->subject,
                    'group_type' => $group->group_type,
                    'max_members' => $group->max_members,
                    'member_count' => $group->member_count,
                    'is_full' => $group->is_full,
                    'image' => $group->image,
                    'creator' => [
                        'id' => $group->creator->id,
                        'name' => $group->creator->name,
                        'type' => $group->creator_type,
                    ],
                    'created_at' => $group->created_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedGroups,
                'pagination' => [
                    'current_page' => $groups->currentPage(),
                    'last_page' => $groups->lastPage(),
                    'per_page' => $groups->perPage(),
                    'total' => $groups->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch groups',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new group
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'subject' => 'nullable|string|max:100',
            'max_members' => 'integer|min:2|max:100',
            'group_type' => 'in:study,discussion,announcement',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            
            // Only tutors can create groups for now
            if ($user->user_type !== 'tutor') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only tutors can create groups'
                ], 403);
            }

            DB::beginTransaction();

            $group = Group::create([
                'name' => $request->name,
                'description' => $request->description,
                'creator_id' => $user->id,
                'creator_type' => $user->user_type,
                'subject' => $request->subject,
                'max_members' => $request->max_members ?? 50,
                'group_type' => $request->group_type ?? 'study',
            ]);

            // Add creator as admin member
            $group->addMember($user->id, 'admin');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Group created successfully',
                'data' => [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description,
                    'subject' => $group->subject,
                    'group_type' => $group->group_type,
                    'max_members' => $group->max_members,
                    'member_count' => 1,
                    'creator' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'type' => $user->user_type,
                    ],
                    'created_at' => $group->created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific group with members
     */
    public function show(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = Group::with(['creator', 'activeMembers'])
                ->where('id', $groupId)
                ->active()
                ->first();

            if (!$group) {
                return response()->json([
                    'success' => false,
                    'message' => 'Group not found'
                ], 404);
            }

            $user = $request->user();
            $isMember = $group->hasMember($user->id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description,
                    'subject' => $group->subject,
                    'group_type' => $group->group_type,
                    'max_members' => $group->max_members,
                    'member_count' => $group->member_count,
                    'is_full' => $group->is_full,
                    'is_member' => $isMember,
                    'image' => $group->image,
                    'creator' => [
                        'id' => $group->creator->id,
                        'name' => $group->creator->name,
                        'type' => $group->creator_type,
                    ],
                    'members' => $group->activeMembers->map(function ($member) {
                        return [
                            'id' => $member->id,
                            'name' => $member->name,
                            'user_type' => $member->user_type,
                            'avatar' => $member->avatar,
                            'role' => $member->pivot->role,
                            'joined_at' => $member->pivot->joined_at,
                        ];
                    }),
                    'created_at' => $group->created_at,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Join a group
     */
    public function join(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = Group::active()->find($groupId);
            
            if (!$group) {
                return response()->json([
                    'success' => false,
                    'message' => 'Group not found'
                ], 404);
            }

            $user = $request->user();

            if ($group->hasMember($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are already a member of this group'
                ], 400);
            }

            if ($group->is_full) {
                return response()->json([
                    'success' => false,
                    'message' => 'Group is full'
                ], 400);
            }

            $success = $group->addMember($user->id);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully joined the group'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to join group'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to join group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Leave a group
     */
    public function leave(Request $request, int $groupId): JsonResponse
    {
        try {
            $group = Group::active()->find($groupId);
            
            if (!$group) {
                return response()->json([
                    'success' => false,
                    'message' => 'Group not found'
                ], 404);
            }

            $user = $request->user();

            if (!$group->hasMember($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a member of this group'
                ], 400);
            }

            // Creator cannot leave their own group
            if ($group->creator_id === $user->id && $group->creator_type === $user->user_type) {
                return response()->json([
                    'success' => false,
                    'message' => 'Group creator cannot leave the group'
                ], 400);
            }

            $success = $group->removeMember($user->id);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully left the group'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to leave group'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to leave group',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
