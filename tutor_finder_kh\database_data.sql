-- =====================================================
-- TUTOR FINDER KH - DATA INSERTION
-- =====================================================

USE tutor_finder_kh_app;

-- =====================================================
-- CATEGORIES DATA
-- =====================================================

INSERT INTO `categories` (`id`, `name`, `description`, `icon`, `color`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Mathematics', 'Algebra, Calculus, Geometry, Statistics', 'calculate', '#FF6B6B', 1, NOW(), NOW()),
(2, 'Physics', 'Mechanics, Thermodynamics, Electromagnetism', 'atom', '#4ECDC4', 1, NOW(), NOW()),
(3, 'Chemistry', 'Organic, Inorganic, Physical Chemistry', 'flask', '#45B7D1', 1, NOW(), NOW()),
(4, 'Biology', 'Cell Biology, Genetics, Ecology', 'leaf', '#96CEB4', 1, NOW(), NOW()),
(5, 'English', 'Grammar, Literature, Writing', 'book', '#FFEAA7', 1, NOW(), NOW()),
(6, 'Computer Science', 'Programming, Algorithms, Data Structures', 'computer', '#DDA0DD', 1, NOW(), NOW()),
(7, 'Programming', 'Web Development, Mobile Apps, Software', 'code', '#74B9FF', 1, NOW(), NOW()),
(8, 'Web Development', 'HTML, CSS, JavaScript, Frameworks', 'globe', '#00B894', 1, NOW(), NOW()),
(9, 'History', 'World History, Local History, Ancient Civilizations', 'history', '#FDCB6E', 1, NOW(), NOW()),
(10, 'Geography', 'Physical Geography, Human Geography, Maps', 'map', '#6C5CE7', 1, NOW(), NOW()),
(11, 'French', 'French Language, Grammar, Conversation', 'language', '#FD79A8', 1, NOW(), NOW()),
(12, 'Languages', 'Multiple Languages, Translation, Linguistics', 'translate', '#FDCB6E', 1, NOW(), NOW()),
(13, 'Economics', 'Microeconomics, Macroeconomics, Finance', 'trending-up', '#00B894', 1, NOW(), NOW()),
(14, 'Business Studies', 'Management, Marketing, Entrepreneurship', 'briefcase', '#0984E3', 1, NOW(), NOW()),
(15, 'Art', 'Drawing, Painting, Digital Art', 'palette', '#E17055', 1, NOW(), NOW()),
(16, 'Music', 'Theory, Instruments, Composition', 'music', '#A29BFE', 1, NOW(), NOW()),
(17, 'Science', 'General Science, Scientific Method', 'science', '#00CEC9', 1, NOW(), NOW()),
(18, 'Social Studies', 'Sociology, Psychology, Anthropology', 'users', '#FDCB6E', 1, NOW(), NOW()),
(19, 'Literature', 'Poetry, Novels, Literary Analysis', 'book-open', '#FD79A8', 1, NOW(), NOW()),
(20, 'Writing', 'Creative Writing, Essays, Technical Writing', 'edit', '#74B9FF', 1, NOW(), NOW()),
(21, 'Spanish', 'Spanish Language, Grammar, Culture', 'language', '#E84393', 1, NOW(), NOW()),
(22, 'Chinese', 'Mandarin, Cantonese, Characters', 'language', '#FF7675', 1, NOW(), NOW()),
(23, 'Korean', 'Korean Language, Hangul, Culture', 'language', '#6C5CE7', 1, NOW(), NOW()),
(24, 'Japanese', 'Japanese Language, Hiragana, Katakana', 'language', '#FD79A8', 1, NOW(), NOW()),
(25, 'Statistics', 'Descriptive Statistics, Probability', 'bar-chart', '#00B894', 1, NOW(), NOW()),
(26, 'Calculus', 'Differential Calculus, Integral Calculus', 'function', '#0984E3', 1, NOW(), NOW()),
(27, 'Algebra', 'Linear Algebra, Abstract Algebra', 'x', '#E17055', 1, NOW(), NOW()),
(28, 'Geometry', 'Euclidean Geometry, Analytical Geometry', 'triangle', '#A29BFE', 1, NOW(), NOW()),
(29, 'Trigonometry', 'Sine, Cosine, Tangent Functions', 'triangle', '#00CEC9', 1, NOW(), NOW()),
(30, 'Environmental Science', 'Ecology, Conservation, Climate', 'leaf', '#00B894', 1, NOW(), NOW()),
(31, 'Psychology', 'Cognitive Psychology, Behavioral Psychology', 'brain', '#6C5CE7', 1, NOW(), NOW()),
(32, 'Philosophy', 'Ethics, Logic, Metaphysics', 'lightbulb', '#FDCB6E', 1, NOW(), NOW()),
(33, 'Political Science', 'Government, Politics, International Relations', 'flag', '#E84393', 1, NOW(), NOW()),
(34, 'Sociology', 'Social Structures, Cultural Studies', 'users', '#74B9FF', 1, NOW(), NOW()),
(35, 'Anthropology', 'Cultural Anthropology, Archaeology', 'users', '#FD79A8', 1, NOW(), NOW()),
(36, 'Engineering', 'Mechanical, Electrical, Civil Engineering', 'settings', '#0984E3', 1, NOW(), NOW()),
(37, 'Architecture', 'Design, Construction, Urban Planning', 'home', '#E17055', 1, NOW(), NOW()),
(38, 'Medicine', 'Anatomy, Physiology, Medical Sciences', 'heart', '#FF6B6B', 1, NOW(), NOW()),
(39, 'Law', 'Legal Studies, Constitutional Law', 'scale', '#2D3436', 1, NOW(), NOW()),
(40, 'Accounting', 'Financial Accounting, Management Accounting', 'calculator', '#00B894', 1, NOW(), NOW()),
(41, 'Marketing', 'Digital Marketing, Brand Management', 'trending-up', '#E84393', 1, NOW(), NOW()),
(42, 'Finance', 'Corporate Finance, Investment Analysis', 'dollar-sign', '#0984E3', 1, NOW(), NOW()),
(43, 'Data Science', 'Machine Learning, Data Analysis', 'database', '#6C5CE7', 1, NOW(), NOW()),
(44, 'Artificial Intelligence', 'AI, Neural Networks, Deep Learning', 'cpu', '#A29BFE', 1, NOW(), NOW());

-- =====================================================
-- USERS DATA
-- =====================================================

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `user_type`, `phone`, `date_of_birth`, `gender`, `address`, `avatar`, `is_active`, `created_at`, `updated_at`) VALUES
-- Admin User
(1, 'Admin User', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', '+************', '1990-01-01', 'male', 'Phnom Penh, Cambodia', 'avatars/admin.jpg', 1, NOW(), NOW()),

-- Tutors
(2, 'Dr. Sarah Johnson', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456788', '1985-03-15', 'female', 'Phnom Penh, Cambodia', 'avatars/sarah_johnson.jpg', 1, NOW(), NOW()),
(3, 'Prof. Michael Chen', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456790', '1980-07-22', 'male', 'Siem Reap, Cambodia', 'avatars/michael_chen.jpg', 1, NOW(), NOW()),
(4, 'Ms. Emily Davis', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456791', '1988-11-08', 'female', 'Battambang, Cambodia', 'avatars/emily_davis.jpg', 1, NOW(), NOW()),
(5, 'Dr. James Wilson', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456792', '1982-05-30', 'male', 'Kampong Cham, Cambodia', 'avatars/james_wilson.jpg', 1, NOW(), NOW()),
(6, 'Ms. Lisa Park', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456793', '1990-09-12', 'female', 'Phnom Penh, Cambodia', 'avatars/lisa_park.jpg', 1, NOW(), NOW()),
(7, 'Dr. Robert Kim', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456794', '1983-12-03', 'male', 'Phnom Penh, Cambodia', 'avatars/robert_kim.jpg', 1, NOW(), NOW()),
(8, 'Ms. Anna Martinez', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456795', '1987-06-18', 'female', 'Siem Reap, Cambodia', 'avatars/anna_martinez.jpg', 1, NOW(), NOW()),
(9, 'Dr. David Thompson', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'tutor', '+855123456796', '1979-04-25', 'male', 'Battambang, Cambodia', 'avatars/david_thompson.jpg', 1, NOW(), NOW()),

-- Students
(10, 'John Smith', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654321', '2005-08-15', 'male', 'Phnom Penh, Cambodia', 'avatars/john_smith.jpg', 1, NOW(), NOW()),
(11, 'Emma Johnson', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654322', '2004-12-08', 'female', 'Siem Reap, Cambodia', 'avatars/emma_johnson.jpg', 1, NOW(), NOW()),
(12, 'Michael Brown', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654323', '2006-03-22', 'male', 'Battambang, Cambodia', 'avatars/michael_brown.jpg', 1, NOW(), NOW()),
(13, 'Sophia Davis', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654324', '2005-11-14', 'female', 'Phnom Penh, Cambodia', 'avatars/sophia_davis.jpg', 1, NOW(), NOW()),
(14, 'William Wilson', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654325', '2004-07-09', 'male', 'Kampong Cham, Cambodia', 'avatars/william_wilson.jpg', 1, NOW(), NOW()),
(15, 'Olivia Miller', '<EMAIL>', NOW(), '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', '+855987654326', '2006-01-28', 'female', 'Phnom Penh, Cambodia', 'avatars/olivia_miller.jpg', 1, NOW(), NOW());

-- =====================================================
-- TUTORS DATA
-- =====================================================

INSERT INTO `tutors` (`id`, `user_id`, `name`, `subjects`, `experience_years`, `education_level`, `hourly_rate`, `bio`, `qualifications`, `languages`, `availability`, `location`, `latitude`, `longitude`, `is_verified`, `is_available`, `rating`, `reviews_count`, `total_sessions`, `response_time`, `teaching_style`, `specializations`, `created_at`, `updated_at`) VALUES
(1, 2, 'Dr. Sarah Johnson', '["Mathematics", "Statistics", "Calculus"]', 8, 'PhD', 25.00, 'Expert in calculus and algebra with PhD in Mathematics from Royal University of Phnom Penh. Passionate about making complex mathematical concepts accessible to students.', '["PhD Mathematics", "Teaching Certificate", "Research Publications"]', '["English", "Khmer"]', '{"Monday": ["09:00-12:00", "14:00-17:00"], "Tuesday": ["09:00-12:00", "14:00-17:00"], "Wednesday": ["09:00-12:00", "14:00-17:00"], "Thursday": ["09:00-12:00", "14:00-17:00"], "Friday": ["09:00-12:00", "14:00-17:00"]}', 'Phnom Penh', 11.5564, 104.9282, 1, 1, 4.9, 127, 450, 30, 'Interactive and patient approach with real-world applications', '["Algebra", "Calculus", "Statistics", "Linear Algebra"]', NOW(), NOW()),

(2, 3, 'Prof. Michael Chen', '["Physics", "Mathematics", "Engineering"]', 12, 'PhD', 30.00, 'Physics professor with extensive research experience in quantum mechanics and theoretical physics. Published researcher with 15+ academic papers.', '["PhD Physics", "Research Publications", "University Professor"]', '["English", "Chinese", "Khmer"]', '{"Monday": ["10:00-13:00", "15:00-18:00"], "Wednesday": ["10:00-13:00", "15:00-18:00"], "Friday": ["10:00-13:00", "15:00-18:00"], "Saturday": ["09:00-12:00", "14:00-17:00"]}', 'Siem Reap', 13.3671, 103.8448, 1, 1, 4.8, 89, 320, 45, 'Research-based learning with practical experiments', '["Quantum Physics", "Mechanics", "Thermodynamics", "Electromagnetism"]', NOW(), NOW()),

(3, 4, 'Ms. Emily Davis', '["English", "Literature", "Creative Writing"]', 6, 'Masters', 20.00, 'English literature specialist with focus on creative writing and grammar. TESOL certified with experience teaching international students.', '["MA English Literature", "TESOL Certificate", "Creative Writing Workshop Leader"]', '["English", "Khmer"]', '{"Tuesday": ["08:00-11:00", "13:00-16:00"], "Thursday": ["08:00-11:00", "13:00-16:00"], "Saturday": ["09:00-12:00", "14:00-17:00"], "Sunday": ["09:00-12:00", "14:00-17:00"]}', 'Battambang', 13.0957, 103.2027, 1, 1, 4.7, 156, 280, 20, 'Creative and engaging methods with personalized feedback', '["Creative Writing", "Grammar", "Literature Analysis", "Essay Writing"]', NOW(), NOW()),

(4, 5, 'Dr. James Wilson', '["Chemistry", "Biology", "Biochemistry"]', 10, 'PhD', 28.00, 'Chemistry and biology expert with laboratory research background. Specializes in making complex scientific concepts understandable.', '["PhD Chemistry", "Lab Research Experience", "Scientific Publications"]', '["English", "Khmer"]', '{"Monday": ["09:00-12:00", "14:00-17:00"], "Tuesday": ["09:00-12:00", "14:00-17:00"], "Thursday": ["09:00-12:00", "14:00-17:00"], "Friday": ["09:00-12:00", "14:00-17:00"]}', 'Kampong Cham', 12.0045, 105.4603, 1, 1, 4.6, 98, 210, 35, 'Hands-on laboratory approach with visual demonstrations', '["Organic Chemistry", "Biochemistry", "Molecular Biology", "Lab Techniques"]', NOW(), NOW()),

(5, 6, 'Ms. Lisa Park', '["Biology", "Environmental Science", "Ecology"]', 5, 'Masters', 22.00, 'Biology teacher with passion for environmental conservation and ecology. Field research experience in Southeast Asian ecosystems.', '["MS Biology", "Environmental Science Certificate", "Field Research Experience"]', '["English", "Khmer", "Korean"]', '{"Wednesday": ["08:00-11:00", "13:00-16:00"], "Friday": ["08:00-11:00", "13:00-16:00"], "Saturday": ["09:00-12:00", "14:00-17:00"], "Sunday": ["09:00-12:00", "14:00-17:00"]}', 'Phnom Penh', 11.5564, 104.9282, 1, 1, 4.8, 134, 195, 25, 'Field-based learning and practical applications', '["Ecology", "Genetics", "Environmental Biology", "Conservation"]', NOW(), NOW()),

(6, 7, 'Dr. Robert Kim', '["Computer Science", "Programming", "Web Development"]', 7, 'Masters', 26.00, 'Full-stack developer and computer science educator. Expert in modern web technologies and programming languages.', '["MS Computer Science", "Industry Certifications", "Open Source Contributor"]', '["English", "Korean", "Khmer"]', '{"Monday": ["10:00-13:00", "15:00-18:00"], "Tuesday": ["10:00-13:00", "15:00-18:00"], "Wednesday": ["10:00-13:00", "15:00-18:00"], "Saturday": ["09:00-12:00", "14:00-17:00"]}', 'Phnom Penh', 11.5564, 104.9282, 1, 1, 4.7, 76, 165, 15, 'Project-based learning with real-world applications', '["JavaScript", "Python", "React", "Node.js", "Database Design"]', NOW(), NOW()),

(7, 8, 'Ms. Anna Martinez', '["Spanish", "French", "Languages"]', 9, 'Masters', 24.00, 'Multilingual educator specializing in Romance languages. Native Spanish speaker with extensive European teaching experience.', '["MA Applied Linguistics", "DELE Examiner", "European Teaching Certificate"]', '["Spanish", "French", "English", "Khmer"]', '{"Tuesday": ["09:00-12:00", "14:00-17:00"], "Wednesday": ["09:00-12:00", "14:00-17:00"], "Thursday": ["09:00-12:00", "14:00-17:00"], "Sunday": ["10:00-13:00", "15:00-18:00"]}', 'Siem Reap', 13.3671, 103.8448, 1, 1, 4.9, 112, 245, 20, 'Immersive conversation-based learning', '["Conversational Spanish", "Business French", "Language Certification Prep"]', NOW(), NOW()),

(8, 9, 'Dr. David Thompson', '["History", "Geography", "Social Studies"]', 15, 'PhD', 27.00, 'History professor with specialization in Southeast Asian history and geography. Published author and documentary consultant.', '["PhD History", "Published Author", "Documentary Consultant"]', '["English", "Khmer", "Thai"]', '{"Monday": ["08:00-11:00", "13:00-16:00"], "Wednesday": ["08:00-11:00", "13:00-16:00"], "Friday": ["08:00-11:00", "13:00-16:00"], "Saturday": ["09:00-12:00", "14:00-17:00"]}', 'Battambang', 13.0957, 103.2027, 1, 1, 4.8, 203, 387, 40, 'Storytelling approach with cultural context', '["Southeast Asian History", "World Geography", "Cultural Studies"]', NOW(), NOW());

-- =====================================================
-- STUDENTS DATA
-- =====================================================

INSERT INTO `students` (`id`, `user_id`, `name`, `grade_level`, `school`, `learning_preferences`, `subjects_of_interest`, `preferred_learning_style`, `goals`, `availability`, `budget_range`, `created_at`, `updated_at`) VALUES
(1, 10, 'John Smith', 'Grade 12', 'Phnom Penh International School', '["Visual Learning", "Interactive Sessions"]', '["Mathematics", "Physics", "Computer Science"]', 'Visual and Hands-on', 'Prepare for university entrance exams and improve problem-solving skills', '{"Monday": ["16:00-18:00"], "Tuesday": ["16:00-18:00"], "Wednesday": ["16:00-18:00"], "Thursday": ["16:00-18:00"], "Saturday": ["09:00-12:00", "14:00-17:00"]}', '$15-25 per hour', NOW(), NOW()),

(2, 11, 'Emma Johnson', 'Grade 11', 'Siem Reap High School', '["Discussion-based", "Practice-oriented"]', '["English", "Literature", "History"]', 'Discussion and Analysis', 'Improve English writing skills and literary analysis', '{"Tuesday": ["15:00-17:00"], "Thursday": ["15:00-17:00"], "Saturday": ["10:00-12:00"], "Sunday": ["10:00-12:00", "14:00-16:00"]}', '$18-22 per hour', NOW(), NOW()),

(3, 12, 'Michael Brown', 'Grade 10', 'Battambang Secondary School', '["Step-by-step guidance", "Practical examples"]', '["Chemistry", "Biology", "Mathematics"]', 'Structured and Methodical', 'Build strong foundation in sciences for future medical studies', '{"Monday": ["17:00-19:00"], "Wednesday": ["17:00-19:00"], "Friday": ["17:00-19:00"], "Sunday": ["09:00-11:00", "15:00-17:00"]}', '$20-28 per hour', NOW(), NOW()),

(4, 13, 'Sophia Davis', 'Grade 11', 'Royal University of Fine Arts Prep', '["Creative approach", "Multimedia learning"]', '["Art", "English", "French"]', 'Creative and Visual', 'Develop artistic skills and language proficiency for international art programs', '{"Tuesday": ["16:00-18:00"], "Thursday": ["16:00-18:00"], "Saturday": ["10:00-12:00", "14:00-16:00"], "Sunday": ["10:00-12:00"]}', '$20-25 per hour', NOW(), NOW()),

(5, 14, 'William Wilson', 'Grade 12', 'Kampong Cham High School', '["Problem-solving focus", "Real-world applications"]', '["Economics", "Business Studies", "Mathematics"]', 'Analytical and Practical', 'Prepare for business school and develop analytical thinking skills', '{"Monday": ["15:00-17:00"], "Wednesday": ["15:00-17:00"], "Friday": ["15:00-17:00"], "Saturday": ["09:00-11:00", "13:00-15:00"]}', '$22-30 per hour', NOW(), NOW()),

(6, 15, 'Olivia Miller', 'Grade 10', 'International School of Phnom Penh', '["Interactive sessions", "Group discussions"]', '["Biology", "Environmental Science", "Geography"]', 'Collaborative and Exploratory', 'Develop environmental awareness and scientific research skills', '{"Tuesday": ["17:00-19:00"], "Thursday": ["17:00-19:00"], "Saturday": ["10:00-12:00"], "Sunday": ["14:00-16:00"]}', '$18-24 per hour', NOW(), NOW());
