import 'dart:io';

import 'package:path_provider/path_provider.dart';

class FileStoragePref {
  static Future<File> _getFile() async {
    final dir = await getApplicationDocumentsDirectory();
    return File('${dir.path}/product.txt');
  }

  static Future<void> orderProduct(
      int productId, String name, double price, int qty, int discount) async {
    //file part
    // String filePart = _getLocalFile();
    //create file
    File file = await _getFile();
    //check file
    if (await file.exists() == false) {
      await file.create();
    }
    //create payload
    String payload =
        "Product ID = $productId, Name = $name, Price = $price, QTY = $qty, Dicount = $discount \n";
    //Write to file
    await file.writeAsString(payload, mode: FileMode.append);
  }

  static Future<List<String>> countProductLines() async {
    List<String> contents = [];
    // File file = File(filePart);
    File file = await _getFile();

    if (!await file.exists()) {
      return contents; // File doesn't exist, so no lines
    }

    // Read the file content
    contents = await file.readAsLines();
    // String contents = await file.readAsString();

    // Split into lines and count
    // List<String> lines = contents.trim().split('\n');

    // Filter out empty lines, just in case
    // lines = lines.where((line) => line.trim().isNotEmpty).toList();

    // return lines.length;
    return contents;
  }
// static Future<void> orderProduct(
//     ) async {
//     //file part
//     String filePart = "assets/data/product.txt";
//     //create file
//     File file = File(filePart);
//     //check file
//     // if (file.exists() == false) {
//     //   file.create();
//     // }
//     //create payload
//     String payload =
//         "Product ID = ";
//     //Write to file
//     await file.writeAsString(payload, mode: FileMode.append);
//   }
  //  Future<void> _loadDataFromFile() async {
  //   try {
  //     final data = await rootBundle.loadString('assets/data/product.txt');
  //     print(data);
  //     print('object');
  //   } catch (e) {
  //     print('error loading data: $e');
  //   }
  // }
}
