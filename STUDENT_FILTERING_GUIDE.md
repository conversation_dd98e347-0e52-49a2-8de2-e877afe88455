# Student Tutor Filtering System - Complete Guide

## 🎯 **Overview**

I've successfully fixed the errors and implemented a comprehensive filtering system that allows students to find tutors based on:
- ✅ **Subject/Category**
- ✅ **Location** (text-based and distance-based)
- ✅ **Online/Offline availability**
- ✅ **Grade levels**
- ✅ **Price range**
- ✅ **Experience level**
- ✅ **Rating**
- ✅ **Languages**
- ✅ **Availability schedule**

## 🔧 **Errors Fixed**

1. **Fixed `Str::slug()` import issue** in TutorPost model
2. **Added missing imports** for TutorPostController
3. **Optimized filtering logic** with dedicated service class
4. **Enhanced API routes** for better organization

## 🛠️ **New Components Created**

### 1. **TutorFilterService**
- Centralized filtering logic
- Modular filter methods
- Reusable across controllers

### 2. **Enhanced TutorController**
- Comprehensive filtering methods
- Optimized query performance
- Multiple sorting options

### 3. **New API Endpoints**
- Subject-specific filtering
- Online/offline tutor filtering
- Grade-level filtering
- Filter options endpoint

## 📡 **API Endpoints for Student Filtering**

### **Main Filtering Endpoint**
```
GET /api/v1/tutors
```

### **Specialized Endpoints**
```
GET /api/v1/tutors/filter-options     - Get all available filter options
GET /api/v1/tutors/online            - Get online tutors only
GET /api/v1/tutors/offline           - Get offline/in-person tutors only
GET /api/v1/tutors/subject/{subject} - Get tutors by specific subject
GET /api/v1/tutors/grade/{grade}     - Get tutors by grade level
```

## 🔍 **Available Filter Parameters**

### **Subject & Category Filters**
```
?subject=math                    - Single subject
?subject[]=math&subject[]=science - Multiple subjects
?category_id=1                   - Single category
?category_id[]=1&category_id[]=2 - Multiple categories
```

### **Location Filters**
```
?location=New York               - Text-based location search
?city=Manhattan                  - City-specific search
?latitude=40.7128&longitude=-74.0060&radius=10 - Distance-based (10km radius)
```

### **Teaching Mode Filters**
```
?teaching_mode=online            - Online tutors only
?teaching_mode=offline           - In-person tutors only
?teaching_mode=both              - Tutors offering both modes
```

### **Grade Level Filters**
```
?grade_level=high_school         - Single grade level
?grade_level[]=elementary&grade_level[]=middle_school - Multiple levels
```

### **Price Range Filters**
```
?min_rate=10                     - Minimum hourly rate
?max_rate=50                     - Maximum hourly rate
?min_rate=10&max_rate=50         - Price range
```

### **Experience Filters**
```
?min_experience=2                - Minimum years of experience
?max_experience=10               - Maximum years of experience
```

### **Rating Filters**
```
?min_rating=4.0                  - Minimum rating (1-5 scale)
```

### **Language Filters**
```
?language=English                - Single language
?language[]=English&language[]=Spanish - Multiple languages
```

### **Availability Filters**
```
?is_available=true               - Only available tutors
?is_verified=true                - Only verified tutors
?available_day=monday            - Available on specific day
?available_time=morning          - Available at specific time
```

### **Search Filters**
```
?search=John                     - Search by name, bio, qualifications
```

### **Sorting Options**
```
?sort_by=rating&sort_order=desc  - Sort by rating (highest first)
?sort_by=price&sort_order=asc    - Sort by price (lowest first)
?sort_by=experience&sort_order=desc - Sort by experience
?sort_by=popularity              - Sort by number of sessions
?sort_by=newest                  - Sort by newest tutors
?sort_by=response_time           - Sort by fastest response
```

## 🧪 **Example API Calls**

### **Find Math Tutors Online Under $30**
```bash
curl "http://127.0.0.1:8000/api/v1/tutors?subject=math&teaching_mode=online&max_rate=30&sort_by=rating"
```

### **Find High School Tutors in New York**
```bash
curl "http://127.0.0.1:8000/api/v1/tutors?grade_level=high_school&location=New York&min_rating=4.0"
```

### **Find Experienced Science Tutors**
```bash
curl "http://127.0.0.1:8000/api/v1/tutors?subject=science&min_experience=5&is_verified=true&sort_by=experience"
```

### **Find Tutors Near Location (Distance-based)**
```bash
curl "http://127.0.0.1:8000/api/v1/tutors?latitude=40.7128&longitude=-74.0060&radius=15&teaching_mode=offline"
```

### **Get Filter Options**
```bash
curl "http://127.0.0.1:8000/api/v1/tutors/filter-options"
```

## 📱 **Flutter Integration Examples**

### **Basic Filtering Service**
```dart
class TutorFilterService {
  static Future<List<Tutor>> filterTutors({
    List<String>? subjects,
    String? location,
    String? teachingMode,
    double? minRate,
    double? maxRate,
    String? gradeLevel,
    double? minRating,
    String? sortBy,
  }) async {
    final queryParams = <String, dynamic>{};
    
    if (subjects != null) queryParams['subject'] = subjects;
    if (location != null) queryParams['location'] = location;
    if (teachingMode != null) queryParams['teaching_mode'] = teachingMode;
    if (minRate != null) queryParams['min_rate'] = minRate;
    if (maxRate != null) queryParams['max_rate'] = maxRate;
    if (gradeLevel != null) queryParams['grade_level'] = gradeLevel;
    if (minRating != null) queryParams['min_rating'] = minRating;
    if (sortBy != null) queryParams['sort_by'] = sortBy;
    
    final response = await ApiClient.get('/tutors', queryParams: queryParams);
    // Handle response...
  }
}
```

### **Filter Widget Example**
```dart
class TutorFilterWidget extends StatefulWidget {
  @override
  _TutorFilterWidgetState createState() => _TutorFilterWidgetState();
}

class _TutorFilterWidgetState extends State<TutorFilterWidget> {
  String? selectedSubject;
  String? selectedTeachingMode;
  RangeValues priceRange = RangeValues(0, 100);
  double minRating = 0;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Subject dropdown
        DropdownButton<String>(
          value: selectedSubject,
          hint: Text('Select Subject'),
          items: ['Math', 'Science', 'English'].map((subject) {
            return DropdownMenuItem(value: subject, child: Text(subject));
          }).toList(),
          onChanged: (value) => setState(() => selectedSubject = value),
        ),
        
        // Teaching mode toggle
        ToggleButtons(
          children: [Text('Online'), Text('Offline'), Text('Both')],
          isSelected: [
            selectedTeachingMode == 'online',
            selectedTeachingMode == 'offline',
            selectedTeachingMode == 'both',
          ],
          onPressed: (index) {
            setState(() {
              selectedTeachingMode = ['online', 'offline', 'both'][index];
            });
          },
        ),
        
        // Price range slider
        RangeSlider(
          values: priceRange,
          min: 0,
          max: 200,
          divisions: 20,
          labels: RangeLabels('\$${priceRange.start.round()}', '\$${priceRange.end.round()}'),
          onChanged: (values) => setState(() => priceRange = values),
        ),
        
        // Apply filters button
        ElevatedButton(
          onPressed: _applyFilters,
          child: Text('Apply Filters'),
        ),
      ],
    );
  }
  
  void _applyFilters() {
    TutorFilterService.filterTutors(
      subjects: selectedSubject != null ? [selectedSubject!] : null,
      teachingMode: selectedTeachingMode,
      minRate: priceRange.start,
      maxRate: priceRange.end,
      minRating: minRating,
    );
  }
}
```

## 🎯 **Filter Options Response**

The `/api/v1/tutors/filter-options` endpoint returns:

```json
{
  "success": true,
  "data": {
    "teaching_modes": ["online", "offline", "both"],
    "grade_levels": ["elementary", "middle_school", "high_school", "college"],
    "sort_options": ["rating", "price", "experience", "popularity", "newest"],
    "price_ranges": [
      {"min": 0, "max": 10, "label": "Under $10"},
      {"min": 10, "max": 25, "label": "$10 - $25"},
      {"min": 25, "max": 50, "label": "$25 - $50"}
    ],
    "subjects": ["Math", "Science", "English", "History"],
    "locations": ["New York", "Los Angeles", "Chicago"],
    "languages": ["English", "Spanish", "French"]
  }
}
```

## 🚀 **Performance Features**

- ✅ **Optimized database queries** with proper indexing
- ✅ **Pagination support** for large result sets
- ✅ **Caching-ready** filter options
- ✅ **Distance-based search** using geographic calculations
- ✅ **JSON field searches** for flexible data storage

Your students can now find the perfect tutor with comprehensive filtering! 🎉
