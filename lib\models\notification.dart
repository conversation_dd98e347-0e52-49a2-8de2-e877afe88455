import 'package:json_annotation/json_annotation.dart';

part 'notification.g.dart';

@JsonSerializable()
class NotificationModel {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_type')
  final String userType;
  final String title;
  final String message;
  final String type;
  final Map<String, dynamic>? data;
  @J<PERSON><PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @<PERSON>son<PERSON><PERSON>(name: 'read_at')
  final String? readAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.userType,
    required this.title,
    required this.message,
    required this.type,
    this.data,
    required this.isRead,
    this.readAt,
    this.createdAt,
    this.updatedAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  // Helper methods
  bool get isBookingType => type == 'booking';
  bool get isMessageType => type == 'message';
  bool get isSystemType => type == 'system';
  bool get isPaymentType => type == 'payment';
  bool get isReviewType => type == 'review';
  bool get isUnread => !isRead;

  DateTime? get createdDateTime {
    try {
      return createdAt != null ? DateTime.parse(createdAt!) : null;
    } catch (e) {
      return null;
    }
  }

  DateTime? get readDateTime {
    try {
      return readAt != null ? DateTime.parse(readAt!) : null;
    } catch (e) {
      return null;
    }
  }

  String get timeAgo {
    final dateTime = createdDateTime;
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  NotificationModel copyWith({
    int? id,
    int? userId,
    String? userType,
    String? title,
    String? message,
    String? type,
    Map<String, dynamic>? data,
    bool? isRead,
    String? readAt,
    String? createdAt,
    String? updatedAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userType: userType ?? this.userType,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class UnreadCount {
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  UnreadCount({
    required this.unreadCount,
  });

  factory UnreadCount.fromJson(Map<String, dynamic> json) => _$UnreadCountFromJson(json);
  Map<String, dynamic> toJson() => _$UnreadCountToJson(this);
}
