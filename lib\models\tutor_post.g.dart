// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tutor_post.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TutorPost _$TutorPostFromJson(Map<String, dynamic> json) => TutorPost(
      id: (json['id'] as num).toInt(),
      tutorId: (json['tutorId'] as num).toInt(),
      categoryId: (json['categoryId'] as num?)?.toInt(),
      title: json['title'] as String,
      content: json['content'] as String,
      excerpt: json['excerpt'] as String?,
      postType: json['postType'] as String,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      featuredImage: json['featuredImage'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => PostAttachment.fromJson(e as Map<String, dynamic>))
          .toList(),
      isPublished: json['isPublished'] as bool,
      isFeatured: json['isFeatured'] as bool,
      publishedAt: json['publishedAt'] == null
          ? null
          : DateTime.parse(json['publishedAt'] as String),
      viewsCount: (json['viewsCount'] as num).toInt(),
      likesCount: (json['likesCount'] as num).toInt(),
      commentsCount: (json['commentsCount'] as num).toInt(),
      metaTitle: json['metaTitle'] as String?,
      metaDescription: json['metaDescription'] as String?,
      slug: json['slug'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      tutor: json['tutor'] == null
          ? null
          : Tutor.fromJson(json['tutor'] as Map<String, dynamic>),
      category: json['category'] == null
          ? null
          : TutorCategory.fromJson(json['category'] as Map<String, dynamic>),
      reviews: (json['reviews'] as List<dynamic>?)
          ?.map((e) => TutorReview.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TutorPostToJson(TutorPost instance) => <String, dynamic>{
      'id': instance.id,
      'tutorId': instance.tutorId,
      'categoryId': instance.categoryId,
      'title': instance.title,
      'content': instance.content,
      'excerpt': instance.excerpt,
      'postType': instance.postType,
      'tags': instance.tags,
      'featuredImage': instance.featuredImage,
      'attachments': instance.attachments,
      'isPublished': instance.isPublished,
      'isFeatured': instance.isFeatured,
      'publishedAt': instance.publishedAt?.toIso8601String(),
      'viewsCount': instance.viewsCount,
      'likesCount': instance.likesCount,
      'commentsCount': instance.commentsCount,
      'metaTitle': instance.metaTitle,
      'metaDescription': instance.metaDescription,
      'slug': instance.slug,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'tutor': instance.tutor,
      'category': instance.category,
      'reviews': instance.reviews,
    };

PostAttachment _$PostAttachmentFromJson(Map<String, dynamic> json) =>
    PostAttachment(
      name: json['name'] as String,
      path: json['path'] as String,
      size: (json['size'] as num).toInt(),
      type: json['type'] as String,
    );

Map<String, dynamic> _$PostAttachmentToJson(PostAttachment instance) =>
    <String, dynamic>{
      'name': instance.name,
      'path': instance.path,
      'size': instance.size,
      'type': instance.type,
    };

CreatePostRequest _$CreatePostRequestFromJson(Map<String, dynamic> json) =>
    CreatePostRequest(
      title: json['title'] as String,
      content: json['content'] as String,
      excerpt: json['excerpt'] as String?,
      postType: json['postType'] as String,
      categoryId: (json['categoryId'] as num?)?.toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isPublished: json['isPublished'] as bool? ?? false,
      isFeatured: json['isFeatured'] as bool? ?? false,
      metaTitle: json['metaTitle'] as String?,
      metaDescription: json['metaDescription'] as String?,
    );

Map<String, dynamic> _$CreatePostRequestToJson(CreatePostRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'content': instance.content,
      'excerpt': instance.excerpt,
      'postType': instance.postType,
      'categoryId': instance.categoryId,
      'tags': instance.tags,
      'isPublished': instance.isPublished,
      'isFeatured': instance.isFeatured,
      'metaTitle': instance.metaTitle,
      'metaDescription': instance.metaDescription,
    };

UpdatePostRequest _$UpdatePostRequestFromJson(Map<String, dynamic> json) =>
    UpdatePostRequest(
      title: json['title'] as String?,
      content: json['content'] as String?,
      excerpt: json['excerpt'] as String?,
      postType: json['postType'] as String?,
      categoryId: (json['categoryId'] as num?)?.toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isPublished: json['isPublished'] as bool?,
      isFeatured: json['isFeatured'] as bool?,
      metaTitle: json['metaTitle'] as String?,
      metaDescription: json['metaDescription'] as String?,
    );

Map<String, dynamic> _$UpdatePostRequestToJson(UpdatePostRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'content': instance.content,
      'excerpt': instance.excerpt,
      'postType': instance.postType,
      'categoryId': instance.categoryId,
      'tags': instance.tags,
      'isPublished': instance.isPublished,
      'isFeatured': instance.isFeatured,
      'metaTitle': instance.metaTitle,
      'metaDescription': instance.metaDescription,
    };

PaginatedPostResponse _$PaginatedPostResponseFromJson(
        Map<String, dynamic> json) =>
    PaginatedPostResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => TutorPost.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentPage: (json['currentPage'] as num).toInt(),
      lastPage: (json['lastPage'] as num).toInt(),
      perPage: (json['perPage'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );

Map<String, dynamic> _$PaginatedPostResponseToJson(
        PaginatedPostResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'currentPage': instance.currentPage,
      'lastPage': instance.lastPage,
      'perPage': instance.perPage,
      'total': instance.total,
      'hasNextPage': instance.hasNextPage,
      'hasPreviousPage': instance.hasPreviousPage,
    };
