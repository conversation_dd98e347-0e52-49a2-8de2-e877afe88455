<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Student;
use App\Models\Tutor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Register a new student
     */
    public function registerStudent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'user_type' => 'student',
                'phone' => $request->phone,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'address' => $request->address,
            ]);

            // Create student profile
            $student = Student::create([
                'user_id' => $user->id,
                'grade_level' => $request->grade_level,
                'school' => $request->school,
                'learning_preferences' => $request->learning_preferences,
            ]);

            // Create token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Student registered successfully',
                'data' => [
                    'user' => $user,
                    'token' => $token,
                    'user_type' => 'student'
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register a new tutor
     */
    public function registerTutor(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'address' => 'nullable|string|max:500',
            'subjects' => 'required|array',
            'experience_years' => 'required|integer|min:0',
            'education_level' => 'required|string',
            'hourly_rate' => 'required|numeric|min:0',
            'bio' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'user_type' => 'tutor',
                'phone' => $request->phone,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'address' => $request->address,
            ]);

            // Create tutor profile
            $tutor = Tutor::create([
                'user_id' => $user->id,
                'subjects' => json_encode($request->subjects),
                'experience_years' => $request->experience_years,
                'education_level' => $request->education_level,
                'hourly_rate' => $request->hourly_rate,
                'bio' => $request->bio,
                'is_verified' => false,
                'is_available' => true,
            ]);

            // Create token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Tutor registered successfully',
                'data' => [
                    'user' => $user,
                    'token' => $token,
                    'user_type' => 'tutor'
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = User::where('email', $request->email)->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            // Delete old tokens
            $user->tokens()->delete();

            // Create new token
            $token = $user->createToken('auth_token')->plainTextToken;

            // Load profile based on user type
            $profile = null;
            if ($user->user_type === 'student') {
                $profile = $user->student;
            } elseif ($user->user_type === 'tutor') {
                $profile = $user->tutor;
            }

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user,
                    'profile' => $profile,
                    'token' => $token,
                    'token_type' => 'Bearer'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logout successful'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Logout failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user profile
     */
    public function profile(Request $request)
    {
        try {
            $user = $request->user();
            
            // Load profile based on user type
            $profile = null;
            if ($user->user_type === 'student') {
                $profile = $user->student;
            } elseif ($user->user_type === 'tutor') {
                $profile = $user->tutor;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $user,
                    'profile' => $profile
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'date_of_birth' => 'sometimes|nullable|date',
            'gender' => 'sometimes|nullable|in:male,female,other',
            'address' => 'sometimes|nullable|string|max:500',
            'avatar' => 'sometimes|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Increased to 5MB
            'tutor_data' => 'sometimes|array',
            'student_data' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Handle avatar upload
            if ($request->hasFile('avatar')) {
                $avatarFile = $request->file('avatar');

                // Validate file
                if (!$avatarFile->isValid()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid avatar file uploaded',
                    ], 422);
                }

                // Check file size (5MB max)
                if ($avatarFile->getSize() > 5 * 1024 * 1024) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Avatar file size must be less than 5MB',
                    ], 422);
                }

                // Check MIME type
                $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!in_array($avatarFile->getMimeType(), $allowedMimes)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Avatar must be a valid image file (JPEG, PNG, GIF, or WebP)',
                    ], 422);
                }

                try {
                    // Delete old avatar if exists
                    if ($user->avatar) {
                        Storage::disk('public')->delete($user->avatar);
                    }

                    // Store new avatar
                    $avatarPath = $avatarFile->store('avatars', 'public');
                    $user->avatar = $avatarPath;
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to upload avatar: ' . $e->getMessage(),
                    ], 500);
                }
            }

            // Update basic user info
            $user->update($request->only(['name', 'phone', 'date_of_birth', 'gender', 'address']));

            // Update profile based on user type
            if ($user->user_type === 'tutor' && $request->has('tutor_data')) {
                $tutorData = $request->tutor_data;

                // Validate tutor-specific fields
                $validatedTutorData = [];
                $allowedTutorFields = [
                    'subjects', 'experience_years', 'education_level', 'hourly_rate',
                    'bio', 'qualifications', 'languages', 'availability', 'location',
                    'teaching_style', 'specializations'
                ];

                foreach ($allowedTutorFields as $field) {
                    if (isset($tutorData[$field])) {
                        $validatedTutorData[$field] = $tutorData[$field];
                    }
                }

                if ($user->tutor) {
                    $user->tutor->update($validatedTutorData);
                } else {
                    $validatedTutorData['user_id'] = $user->id;
                    \App\Models\Tutor::create($validatedTutorData);
                }
            } elseif ($user->user_type === 'student' && $request->has('student_data')) {
                $studentData = $request->student_data;

                // Validate student-specific fields
                $validatedStudentData = [];
                $allowedStudentFields = [
                    'grade_level', 'school', 'learning_preferences', 'subjects_of_interest',
                    'preferred_learning_style', 'goals', 'availability', 'budget_range'
                ];

                foreach ($allowedStudentFields as $field) {
                    if (isset($studentData[$field])) {
                        $validatedStudentData[$field] = $studentData[$field];
                    }
                }

                if ($user->student) {
                    $user->student->update($validatedStudentData);
                } else {
                    $validatedStudentData['user_id'] = $user->id;
                    \App\Models\Student::create($validatedStudentData);
                }
            }

            // Reload relationships
            $user->load(['tutor', 'student']);

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => $user,
                    'profile' => $user->user_type === 'tutor' ? $user->tutor : $user->student,
                    'avatar_url' => $user->avatar ? asset('storage/' . $user->avatar) : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Profile update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
