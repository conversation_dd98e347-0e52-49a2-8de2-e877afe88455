import 'package:json_annotation/json_annotation.dart';

part 'group.g.dart';

@JsonSerializable()
class Group {
  final int id;
  final String name;
  final String? description;
  final String? subject;
  @Json<PERSON>ey(name: 'group_type')
  final String groupType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'max_members')
  final int maxMembers;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'member_count')
  final int memberCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_full')
  final bool isFull;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_member')
  final bool? isMember;
  final String? image;
  final GroupCreator creator;
  final List<GroupMember>? members;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_message')
  final GroupLastMessage? lastMessage;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final DateTime? updatedAt;

  Group({
    required this.id,
    required this.name,
    this.description,
    this.subject,
    required this.groupType,
    required this.maxMembers,
    required this.memberCount,
    required this.isFull,
    this.isMember,
    this.image,
    required this.creator,
    this.members,
    this.lastMessage,
    required this.createdAt,
    this.updatedAt,
  });

  factory Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);
  Map<String, dynamic> toJson() => _$GroupToJson(this);

  Group copyWith({
    int? id,
    String? name,
    String? description,
    String? subject,
    String? groupType,
    int? maxMembers,
    int? memberCount,
    bool? isFull,
    bool? isMember,
    String? image,
    GroupCreator? creator,
    List<GroupMember>? members,
    GroupLastMessage? lastMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Group(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      subject: subject ?? this.subject,
      groupType: groupType ?? this.groupType,
      maxMembers: maxMembers ?? this.maxMembers,
      memberCount: memberCount ?? this.memberCount,
      isFull: isFull ?? this.isFull,
      isMember: isMember ?? this.isMember,
      image: image ?? this.image,
      creator: creator ?? this.creator,
      members: members ?? this.members,
      lastMessage: lastMessage ?? this.lastMessage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class GroupCreator {
  final int id;
  final String name;
  final String type;

  GroupCreator({
    required this.id,
    required this.name,
    required this.type,
  });

  factory GroupCreator.fromJson(Map<String, dynamic> json) => _$GroupCreatorFromJson(json);
  Map<String, dynamic> toJson() => _$GroupCreatorToJson(this);
}

@JsonSerializable()
class GroupMember {
  final int id;
  final String name;
  @JsonKey(name: 'user_type')
  final String userType;
  final String? avatar;
  final String role;
  @JsonKey(name: 'joined_at')
  final DateTime joinedAt;

  GroupMember({
    required this.id,
    required this.name,
    required this.userType,
    this.avatar,
    required this.role,
    required this.joinedAt,
  });

  factory GroupMember.fromJson(Map<String, dynamic> json) => _$GroupMemberFromJson(json);
  Map<String, dynamic> toJson() => _$GroupMemberToJson(this);
}

@JsonSerializable()
class GroupLastMessage {
  final int id;
  final String message;
  @JsonKey(name: 'sender_name')
  final String senderName;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  GroupLastMessage({
    required this.id,
    required this.message,
    required this.senderName,
    required this.createdAt,
  });

  factory GroupLastMessage.fromJson(Map<String, dynamic> json) => _$GroupLastMessageFromJson(json);
  Map<String, dynamic> toJson() => _$GroupLastMessageToJson(this);
}

@JsonSerializable()
class GroupMessage {
  final int id;
  @JsonKey(name: 'group_id')
  final int groupId;
  final GroupMessageSender sender;
  final String message;
  @JsonKey(name: 'message_type')
  final String messageType;
  @JsonKey(name: 'file_path')
  final String? filePath;
  @JsonKey(name: 'reply_to')
  final GroupMessageReply? replyTo;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  GroupMessage({
    required this.id,
    required this.groupId,
    required this.sender,
    required this.message,
    required this.messageType,
    this.filePath,
    this.replyTo,
    required this.createdAt,
  });

  factory GroupMessage.fromJson(Map<String, dynamic> json) => _$GroupMessageFromJson(json);
  Map<String, dynamic> toJson() => _$GroupMessageToJson(this);

  bool get isFromCurrentUser => false; // Will be set based on current user context
  bool get isText => messageType == 'text';
  bool get isImage => messageType == 'image';
  bool get isFile => messageType == 'file';
  bool get hasReply => replyTo != null;
}

@JsonSerializable()
class GroupMessageSender {
  final int id;
  final String name;
  final String type;
  final String? avatar;

  GroupMessageSender({
    required this.id,
    required this.name,
    required this.type,
    this.avatar,
  });

  factory GroupMessageSender.fromJson(Map<String, dynamic> json) => _$GroupMessageSenderFromJson(json);
  Map<String, dynamic> toJson() => _$GroupMessageSenderToJson(this);
}

@JsonSerializable()
class GroupMessageReply {
  final int id;
  final String message;
  @JsonKey(name: 'sender_name')
  final String senderName;

  GroupMessageReply({
    required this.id,
    required this.message,
    required this.senderName,
  });

  factory GroupMessageReply.fromJson(Map<String, dynamic> json) => _$GroupMessageReplyFromJson(json);
  Map<String, dynamic> toJson() => _$GroupMessageReplyToJson(this);
}
