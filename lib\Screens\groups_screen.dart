import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/group_provider.dart';
import '../providers/auth_provider.dart';
import '../models/group.dart';
import '../theme/app_theme.dart';
import 'group_chat_screen.dart';
import 'create_group_screen.dart';

class GroupsScreen extends StatefulWidget {
  const GroupsScreen({super.key});

  @override
  State<GroupsScreen> createState() => _GroupsScreenState();
}

class _GroupsScreenState extends State<GroupsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final groupProvider = Provider.of<GroupProvider>(context, listen: false);
      groupProvider.loadGroups();
      groupProvider.loadUserGroups();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Groups'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All Groups'),
            Tab(text: 'My Groups'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllGroupsTab(),
          _buildMyGroupsTab(),
        ],
      ),
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          // Only tutors can create groups
          if (authProvider.userType == 'tutor') {
            return FloatingActionButton(
              onPressed: () => _navigateToCreateGroup(),
              backgroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.add, color: Colors.white),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildAllGroupsTab() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        Expanded(
          child: Consumer<GroupProvider>(
            builder: (context, groupProvider, child) {
              if (groupProvider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (groupProvider.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'Failed to load groups',
                        style: AppTheme.headingMedium.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        groupProvider.error!,
                        style: AppTheme.bodyText.copyWith(color: Colors.grey[500]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => groupProvider.loadGroups(refresh: true),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              if (groupProvider.allGroups.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.group_outlined, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'No groups found',
                        style: AppTheme.headingMedium.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Try adjusting your search or filters',
                        style: AppTheme.bodyText.copyWith(color: Colors.grey[500]),
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: () => groupProvider.loadGroups(refresh: true),
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: groupProvider.allGroups.length,
                  itemBuilder: (context, index) {
                    final group = groupProvider.allGroups[index];
                    return _buildGroupCard(group, false);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMyGroupsTab() {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        if (groupProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (groupProvider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'Failed to load your groups',
                  style: AppTheme.headingMedium.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => groupProvider.loadUserGroups(refresh: true),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (groupProvider.userGroups.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.group_outlined, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No groups joined yet',
                  style: AppTheme.headingMedium.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'Join groups from the All Groups tab',
                  style: AppTheme.bodyText.copyWith(color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => groupProvider.loadUserGroups(refresh: true),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: groupProvider.userGroups.length,
            itemBuilder: (context, index) {
              final group = groupProvider.userGroups[index];
              return _buildGroupCard(group, true);
            },
          ),
        );
      },
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search groups...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _performSearch();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) => _performSearch(),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Consumer<GroupProvider>(
                  builder: (context, groupProvider, child) {
                    return DropdownButtonFormField<String>(
                      value: groupProvider.selectedSubject,
                      decoration: InputDecoration(
                        labelText: 'Subject',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('All Subjects'),
                        ),
                        ...['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English']
                            .map((subject) => DropdownMenuItem<String>(
                                  value: subject,
                                  child: Text(subject),
                                )),
                      ],
                      onChanged: (value) => _applyFilters(subject: value),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Consumer<GroupProvider>(
                  builder: (context, groupProvider, child) {
                    return DropdownButtonFormField<String>(
                      value: groupProvider.creatorTypeFilter,
                      decoration: InputDecoration(
                        labelText: 'Created by',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem<String>(
                          value: null,
                          child: Text('Anyone'),
                        ),
                        DropdownMenuItem<String>(
                          value: 'tutor',
                          child: Text('Tutors'),
                        ),
                        DropdownMenuItem<String>(
                          value: 'student',
                          child: Text('Students'),
                        ),
                      ],
                      onChanged: (value) => _applyFilters(creatorType: value),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGroupCard(Group group, bool isJoined) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _navigateToGroupChat(group),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
                    child: group.image != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(24),
                            child: Image.network(
                              group.image!,
                              width: 48,
                              height: 48,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(Icons.group, color: AppTheme.primaryColor),
                            ),
                          )
                        : const Icon(Icons.group, color: AppTheme.primaryColor),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          group.name,
                          style: AppTheme.headingSmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.person, size: 14, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text(
                              '${group.memberCount}/${group.maxMembers} members',
                              style: AppTheme.captionText.copyWith(color: Colors.grey[600]),
                            ),
                            if (group.subject != null) ...[
                              const SizedBox(width: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  group.subject!,
                                  style: AppTheme.captionText.copyWith(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (!isJoined && !(group.isMember ?? false))
                    _buildJoinButton(group)
                  else if (isJoined)
                    const Icon(Icons.check_circle, color: AppTheme.successColor),
                ],
              ),
              if (group.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  group.description!,
                  style: AppTheme.bodyText.copyWith(color: Colors.grey[700]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Created by ${group.creator.name}',
                    style: AppTheme.captionText.copyWith(color: Colors.grey[600]),
                  ),
                  const Spacer(),
                  if (group.lastMessage != null) ...[
                    Icon(Icons.message, size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      _formatLastMessageTime(group.lastMessage!.createdAt),
                      style: AppTheme.captionText.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJoinButton(Group group) {
    return Consumer<GroupProvider>(
      builder: (context, groupProvider, child) {
        return ElevatedButton(
          onPressed: group.isFull ? null : () => _joinGroup(group),
          style: ElevatedButton.styleFrom(
            backgroundColor: group.isFull ? Colors.grey : AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: Text(
            group.isFull ? 'Full' : 'Join',
            style: const TextStyle(fontSize: 12),
          ),
        );
      },
    );
  }

  void _performSearch() {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    groupProvider.applyFilters(
      subject: groupProvider.selectedSubject,
      creatorType: groupProvider.creatorTypeFilter,
      search: _searchController.text.isEmpty ? null : _searchController.text,
    );
  }

  void _applyFilters({String? subject, String? creatorType}) {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);
    groupProvider.applyFilters(
      subject: subject,
      creatorType: creatorType,
      search: _searchController.text.isEmpty ? null : _searchController.text,
    );
  }

  Future<void> _joinGroup(Group group) async {
    final groupProvider = Provider.of<GroupProvider>(context, listen: false);

    final success = await groupProvider.joinGroup(group.id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully joined ${group.name}'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(groupProvider.error ?? 'Failed to join group'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToGroupChat(Group group) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatScreen(group: group),
      ),
    );
  }

  void _navigateToCreateGroup() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateGroupScreen(),
      ),
    );
  }

  String _formatLastMessageTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
