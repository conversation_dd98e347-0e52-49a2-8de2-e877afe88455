import 'package:flutter/widgets.dart';
import '../models/api_response.dart';
import '../models/user.dart';
import '../services/tutor_service.dart';

enum TutorState {
  initial,
  loading,
  loaded,
  error,
}

class TutorProvider extends ChangeNotifier {
  final TutorService _tutorService = TutorService();
  
  TutorState _state = TutorState.initial;
  String? _errorMessage;
  List<Tutor> _tutors = [];
  List<Tutor> _featuredTutors = [];
  List<Tutor> _searchResults = [];
  PaginatedResponse<Tutor>? _paginatedTutors;
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  bool _isUpdating = false;

  // Getters
  TutorState get state => _state;
  String? get errorMessage => _errorMessage;
  List<Tutor> get tutors => _tutors;
  List<Tutor> get featuredTutors => _featuredTutors;
  List<Tutor> get searchResults => _searchResults;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  int get currentPage => _currentPage;
  int get totalTutors => _paginatedTutors?.total ?? 0;

  // Load featured tutors
  Future<void> loadFeaturedTutors() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _tutorService.getFeaturedTutors();

      if (response.isSuccess && response.data != null) {
        _featuredTutors = response.data!;
        _setState(TutorState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load featured tutors');
      }
    } catch (e) {
      _setError('Failed to load featured tutors: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load tutors with pagination
  Future<void> loadTutors({
    bool refresh = false,
    String? search,
    int? categoryId,
    String? location,
    bool? isOnline,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    String? sortBy,
    String? sortOrder,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _tutors.clear();
    }

    if (!_hasMore && !refresh) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _tutorService.getTutors(
        search: search,
        categoryId: categoryId,
        location: location,
        isOnline: isOnline,
        minPrice: minPrice,
        maxPrice: maxPrice,
        minRating: minRating,
        sortBy: sortBy,
        sortOrder: sortOrder,
        page: _currentPage,
        perPage: 15,
      );

      if (response.isSuccess && response.data != null) {
        _paginatedTutors = response.data!;
        
        if (refresh) {
          _tutors = response.data!.data;
        } else {
          _tutors.addAll(response.data!.data);
        }

        _hasMore = response.data!.hasNextPage;
        if (_hasMore) {
          _currentPage++;
        }

        _setState(TutorState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load tutors');
      }
    } catch (e) {
      _setError('Failed to load tutors: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search tutors
  Future<void> searchTutors({
    String? query,
    List<int>? categoryIds,
    String? location,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    bool? isOnline,
    String? sortBy,
    String? sortOrder,
    bool refresh = true,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _searchResults.clear();
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await _tutorService.searchTutors(
        query: query,
        categoryIds: categoryIds,
        location: location,
        minPrice: minPrice,
        maxPrice: maxPrice,
        minRating: minRating,
        isOnline: isOnline,
        sortBy: sortBy,
        sortOrder: sortOrder,
        page: _currentPage,
        perPage: 15,
      );

      if (response.isSuccess && response.data != null) {
        if (refresh) {
          _searchResults = response.data!.data;
        } else {
          _searchResults.addAll(response.data!.data);
        }

        _hasMore = response.data!.hasNextPage;
        if (_hasMore) {
          _currentPage++;
        }

        _setState(TutorState.loaded);
      } else {
        _setError(response.message ?? 'Failed to search tutors');
      }
    } catch (e) {
      _setError('Failed to search tutors: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get tutor by ID
  Future<Tutor?> getTutorById(int id) async {
    try {
      final response = await _tutorService.getTutorById(id);
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
    } catch (e) {
      print('Error getting tutor by ID: $e');
    }
    return null;
  }

  // Get tutors by category
  Future<void> loadTutorsByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _tutorService.getTutorsByCategory(categoryId);

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        if (data['tutors'] != null) {
          final tutorsData = data['tutors'] as Map<String, dynamic>;
          final paginatedResponse = PaginatedResponse<Tutor>.fromJson(
            tutorsData,
            (json) => Tutor.fromJson(json as Map<String, dynamic>),
          );
          _tutors = paginatedResponse.data;
          _paginatedTutors = paginatedResponse;
        }
        _setState(TutorState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load tutors by category');
      }
    } catch (e) {
      _setError('Failed to load tutors by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Clear search results
  void clearSearchResults() {
    _searchResults.clear();
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _clearError();
  }

  // Refresh all data
  Future<void> refresh() async {
    await Future.wait([
      loadFeaturedTutors(),
      loadTutors(refresh: true),
    ]);
  }

  // Private methods
  void _setState(TutorState newState) {
    if (_isUpdating) return;
    _isUpdating = true;
    _state = newState;
    notifyListeners();
    _isUpdating = false;
  }

  void _setLoading(bool loading) {
    if (_isUpdating) return;
    _isUpdating = true;
    _isLoading = loading;
    notifyListeners();
    _isUpdating = false;
  }

  void _setError(String error) {
    if (_isUpdating) return;
    _isUpdating = true;
    _errorMessage = error;
    _state = TutorState.error;
    notifyListeners();
    _isUpdating = false;
  }

  void _clearError() {
    if (_isUpdating) return;
    _isUpdating = true;
    _errorMessage = null;
    if (_state == TutorState.error) {
      _state = _tutors.isNotEmpty ? TutorState.loaded : TutorState.initial;
    }
    notifyListeners();
    _isUpdating = false;
  }

  // Filter methods
  List<Tutor> filterByRating(double minRating) {
    return TutorService.filterTutorsByRating(_tutors, minRating);
  }

  List<Tutor> filterByPrice(double? minPrice, double? maxPrice) {
    return TutorService.filterTutorsByPrice(_tutors, minPrice, maxPrice);
  }

  List<Tutor> filterByOnlineStatus(bool isOnline) {
    return TutorService.filterTutorsByOnlineStatus(_tutors, isOnline);
  }

  List<Tutor> filterByLocation(String location) {
    return TutorService.filterTutorsByLocation(_tutors, location);
  }

  List<Tutor> searchByName(String query) {
    return TutorService.searchTutorsByName(_tutors, query);
  }

  void sortTutors(String sortBy, {bool ascending = true}) {
    _tutors = TutorService.sortTutors(_tutors, sortBy, ascending: ascending);
    notifyListeners();
  }

  // Get statistics
  Map<String, dynamic> getTutorStats() {
    return TutorService.getTutorStats(_tutors);
  }
}
