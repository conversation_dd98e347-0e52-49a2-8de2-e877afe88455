<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'conversation_id',
        'sender_id',
        'receiver_id',
        'sender_type',
        'receiver_type',
        'message',
        'message_type',
        'file_path',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Get the sender (polymorphic).
     */
    public function sender(): MorphTo
    {
        return $this->morphTo('sender', 'sender_type', 'sender_id');
    }

    /**
     * Get the receiver (polymorphic).
     */
    public function receiver(): MorphTo
    {
        return $this->morphTo('receiver', 'receiver_type', 'receiver_id');
    }

    /**
     * Get the conversation this message belongs to.
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class);
    }

    /**
     * Mark message as read.
     */
    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    /**
     * Get conversation between two users.
     */
    public static function getConversation(int $userId1, string $userType1, int $userId2, string $userType2)
    {
        return self::where(function ($query) use ($userId1, $userType1, $userId2, $userType2) {
            $query->where('sender_id', $userId1)
                  ->where('sender_type', $userType1)
                  ->where('receiver_id', $userId2)
                  ->where('receiver_type', $userType2);
        })->orWhere(function ($query) use ($userId1, $userType1, $userId2, $userType2) {
            $query->where('sender_id', $userId2)
                  ->where('sender_type', $userType2)
                  ->where('receiver_id', $userId1)
                  ->where('receiver_type', $userType1);
        })->orderBy('created_at');
    }

    /**
     * Scope a query to only include unread messages.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope a query to filter by message type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('message_type', $type);
    }

    /**
     * Scope a query to get messages for a specific receiver.
     */
    public function scopeForReceiver($query, int $receiverId, string $receiverType)
    {
        return $query->where('receiver_id', $receiverId)
                    ->where('receiver_type', $receiverType);
    }

    /**
     * Scope a query to get messages from a specific sender.
     */
    public function scopeFromSender($query, int $senderId, string $senderType)
    {
        return $query->where('sender_id', $senderId)
                    ->where('sender_type', $senderType);
    }
}
