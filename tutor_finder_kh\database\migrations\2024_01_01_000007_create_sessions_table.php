<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tutor_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->datetime('session_date');
            $table->integer('duration_minutes')->default(60);
            $table->decimal('price', 8, 2);
            $table->enum('status', ['booked', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('booked');
            $table->text('notes')->nullable();
            $table->string('meeting_link')->nullable();
            $table->enum('session_type', ['online', 'in_person'])->default('online');
            $table->string('location')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->timestamps();
            
            $table->index(['tutor_id', 'session_date']);
            $table->index(['student_id', 'session_date']);
            $table->index(['status', 'session_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sessions');
    }
};
