// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Group _$GroupFromJson(Map<String, dynamic> json) => Group(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      subject: json['subject'] as String?,
      groupType: json['group_type'] as String,
      maxMembers: (json['max_members'] as num).toInt(),
      memberCount: (json['member_count'] as num).toInt(),
      isFull: json['is_full'] as bool,
      isMember: json['is_member'] as bool?,
      image: json['image'] as String?,
      creator: GroupCreator.fromJson(json['creator'] as Map<String, dynamic>),
      members: (json['members'] as List<dynamic>?)
          ?.map((e) => GroupMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastMessage: json['last_message'] == null
          ? null
          : GroupLastMessage.fromJson(
              json['last_message'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$GroupToJson(Group instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'subject': instance.subject,
      'group_type': instance.groupType,
      'max_members': instance.maxMembers,
      'member_count': instance.memberCount,
      'is_full': instance.isFull,
      'is_member': instance.isMember,
      'image': instance.image,
      'creator': instance.creator,
      'members': instance.members,
      'last_message': instance.lastMessage,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

GroupCreator _$GroupCreatorFromJson(Map<String, dynamic> json) => GroupCreator(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$GroupCreatorToJson(GroupCreator instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };

GroupMember _$GroupMemberFromJson(Map<String, dynamic> json) => GroupMember(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      userType: json['user_type'] as String,
      avatar: json['avatar'] as String?,
      role: json['role'] as String,
      joinedAt: DateTime.parse(json['joined_at'] as String),
    );

Map<String, dynamic> _$GroupMemberToJson(GroupMember instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'user_type': instance.userType,
      'avatar': instance.avatar,
      'role': instance.role,
      'joined_at': instance.joinedAt.toIso8601String(),
    };

GroupLastMessage _$GroupLastMessageFromJson(Map<String, dynamic> json) =>
    GroupLastMessage(
      id: (json['id'] as num).toInt(),
      message: json['message'] as String,
      senderName: json['sender_name'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$GroupLastMessageToJson(GroupLastMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'sender_name': instance.senderName,
      'created_at': instance.createdAt.toIso8601String(),
    };

GroupMessage _$GroupMessageFromJson(Map<String, dynamic> json) => GroupMessage(
      id: (json['id'] as num).toInt(),
      groupId: (json['group_id'] as num).toInt(),
      sender:
          GroupMessageSender.fromJson(json['sender'] as Map<String, dynamic>),
      message: json['message'] as String,
      messageType: json['message_type'] as String,
      filePath: json['file_path'] as String?,
      replyTo: json['reply_to'] == null
          ? null
          : GroupMessageReply.fromJson(
              json['reply_to'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$GroupMessageToJson(GroupMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'group_id': instance.groupId,
      'sender': instance.sender,
      'message': instance.message,
      'message_type': instance.messageType,
      'file_path': instance.filePath,
      'reply_to': instance.replyTo,
      'created_at': instance.createdAt.toIso8601String(),
    };

GroupMessageSender _$GroupMessageSenderFromJson(Map<String, dynamic> json) =>
    GroupMessageSender(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String,
      avatar: json['avatar'] as String?,
    );

Map<String, dynamic> _$GroupMessageSenderToJson(GroupMessageSender instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'avatar': instance.avatar,
    };

GroupMessageReply _$GroupMessageReplyFromJson(Map<String, dynamic> json) =>
    GroupMessageReply(
      id: (json['id'] as num).toInt(),
      message: json['message'] as String,
      senderName: json['sender_name'] as String,
    );

Map<String, dynamic> _$GroupMessageReplyToJson(GroupMessageReply instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'sender_name': instance.senderName,
    };
