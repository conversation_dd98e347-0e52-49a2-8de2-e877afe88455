import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/message.dart';
import 'api_client.dart';

class MessageService {
  final ApiClient _apiClient = ApiClient();

  // Get all conversations
  Future<ApiResponse<List<Conversation>>> getConversations() async {
    return await _apiClient.get<List<Conversation>>(
      ApiConfig.conversations,
      fromJson: (data) => (data as List)
          .map((item) => Conversation.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Get specific conversation with a user
  Future<ApiResponse<List<Message>>> getConversation(int userId, String userType) async {
    return await _apiClient.get<List<Message>>(
      ApiConfig.conversation(userId, userType),
      fromJson: (data) => (data as List)
          .map((item) => Message.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Send a message
  Future<ApiResponse<Message>> sendMessage(SendMessageRequest request) async {
    return await _apiClient.post<Message>(
      ApiConfig.sendMessage,
      data: request.toJson(),
      fromJson: (data) => Message.fromJson(data as Map<String, dynamic>),
    );
  }

  Future<ApiResponse<Map<String, dynamic>>> markAsRead(int messageId) async {
    return await _apiClient.put<Map<String, dynamic>>(
      ApiConfig.markMessageAsRead(messageId),
      fromJson: (data) => data as Map<String, dynamic>? ?? {},
    );
  }

  Future<ApiResponse<UnreadCount>> getUnreadCount() async {
    return await _apiClient.get<UnreadCount>(
      ApiConfig.unreadMessagesCount,
      fromJson: (data) => UnreadCount.fromJson(data as Map<String, dynamic>),
    );
  }

  // Get all messages (for debugging/admin purposes)
  Future<ApiResponse<List<Message>>> getAllMessages() async {
    return await _apiClient.get<List<Message>>(
      ApiConfig.messagesList,
      fromJson: (data) => (data as List)
          .map((item) => Message.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Helper methods for message operations
  static String formatMessageTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  static String formatChatTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final isToday = now.day == dateTime.day && 
                   now.month == dateTime.month && 
                   now.year == dateTime.year;
    
    if (isToday) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  static bool isMessageFromCurrentUser(Message message, int currentUserId, String currentUserType) {
    return message.senderId == currentUserId && message.senderType == currentUserType;
  }

  static String getOtherUserName(Conversation conversation) {
    return conversation.otherUserName;
  }

  static String getOtherUserType(Conversation conversation) {
    return conversation.otherUserType;
  }

  static String? getOtherUserAvatar(Conversation conversation) {
    return conversation.otherUserAvatar;
  }

  // Validate message before sending
  static bool isValidMessage(String message) {
    return message.trim().isNotEmpty && message.trim().length <= 1000;
  }

  // Get message type icon
  static String getMessageTypeIcon(String messageType) {
    switch (messageType) {
      case 'text':
        return '💬';
      case 'image':
        return '🖼️';
      case 'file':
        return '📎';
      default:
        return '💬';
    }
  }

  static String getUserTypeDisplayName(String userType) {
    switch (userType) {
      case 'student':
        return 'Student';
      case 'tutor':
        return 'Tutor';
      default:
        return userType;
    }
  }
}
