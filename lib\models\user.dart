import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

// Helper function to convert various types to bool
bool? _boolFromJson(dynamic value) {
  if (value == null) return null;
  if (value is bool) return value;
  if (value is int) return value == 1;
  if (value is String) return value.toLowerCase() == 'true' || value == '1';
  return null;
}

@JsonSerializable()
class Student {
  final int id;
  final String name;
  final String? phone;
  final String? email;
  final String? avatar;
  @Json<PERSON><PERSON>(name: 'date_of_birth')
  final String? dateOfBirth;
  final String? gender;
  final String? location;
  final String? bio;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  Student({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.avatar,
    this.dateOfBirth,
    this.gender,
    this.location,
    this.bio,
    this.createdAt,
    this.updatedAt,
  });

  factory Student.from<PERSON>son(Map<String, dynamic> json) => _$StudentFromJson(json);
  Map<String, dynamic> toJson() => _$StudentToJson(this);

  Student copyWith({
    int? id,
    String? name,
    String? phone,
    String? email,
    String? avatar,
    String? dateOfBirth,
    String? gender,
    String? location,
    String? bio,
    String? createdAt,
    String? updatedAt,
  }) {
    return Student(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      location: location ?? this.location,
      bio: bio ?? this.bio,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class Tutor {
  final int id;
  final String? name;
  final String? description;
  final String? phone;
  @JsonKey(name: 'is_online', fromJson: _boolFromJson)
  final bool? isOnline;
  final String? location;
  final String? totalStudents;
  final String? image;
  final String? status;
  @JsonKey(name: 'hourly_rate')
  final String? hourlyRate;
  @JsonKey(name: 'experience_years')
  final int? experienceYears;
  final String? qualifications;
  final List<String>? languages;
  final String? email;
  @JsonKey(name: 'average_rating')
  final double? averageRating;
  @JsonKey(name: 'review_count')
  final int? reviewCount;
  @JsonKey(name: 'completed_sessions_count')
  final int? completedSessionsCount;
  final List<TutorCategory>? categories;
  final List<TutorReview>? reviews;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  Tutor({
    required this.id,
    required this.name,
    this.description,
    this.phone,
    required this.isOnline,
    this.location,
    this.totalStudents,
    this.image,
    required this.status,
    this.hourlyRate,
    this.experienceYears,
    this.qualifications,
    this.languages,
    this.email,
    this.averageRating,
    this.reviewCount,
    this.completedSessionsCount,
    this.categories,
    this.reviews,
    this.createdAt,
    this.updatedAt,
  });

  factory Tutor.fromJson(Map<String, dynamic> json) => _$TutorFromJson(json);
  Map<String, dynamic> toJson() => _$TutorToJson(this);

  // Helper methods
  double get rating => averageRating ?? 0.0;
  int get totalReviews => reviewCount ?? 0;
  double get hourlyRateValue => double.tryParse(hourlyRate ?? '0') ?? 0.0;
  String get experienceText => experienceYears != null ? '$experienceYears years' : 'New tutor';
  bool get isApproved => status == 'approved';
  String get primaryCategory => categories?.isNotEmpty == true ? categories!.first.name : 'General';

  Tutor copyWith({
    int? id,
    String? name,
    String? description,
    String? phone,
    bool? isOnline,
    String? location,
    String? image,
    String? status,
    String? hourlyRate,
    int? experienceYears,
    String? qualifications,
    List<String>? languages,
    String? email,
    double? averageRating,
    int? reviewCount,
    int? completedSessionsCount,
    List<TutorCategory>? categories,
    List<TutorReview>? reviews,
    String? createdAt,
    String? updatedAt,
  }) {
    return Tutor(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      phone: phone ?? this.phone,
      isOnline: isOnline ?? this.isOnline,
      location: location ?? this.location,
      image: image ?? this.image,
      status: status ?? this.status,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      experienceYears: experienceYears ?? this.experienceYears,
      qualifications: qualifications ?? this.qualifications,
      languages: languages ?? this.languages,
      email: email ?? this.email,
      averageRating: averageRating ?? this.averageRating,
      reviewCount: reviewCount ?? this.reviewCount,
      completedSessionsCount: completedSessionsCount ?? this.completedSessionsCount,
      categories: categories ?? this.categories,
      reviews: reviews ?? this.reviews,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class TutorCategory {
  final int id;
  final String name;
  @JsonKey(name: 'tutor_count')
  final int? tutorCount;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  TutorCategory({
    required this.id,
    required this.name,
    this.tutorCount,
    this.createdAt,
    this.updatedAt,
  });

  factory TutorCategory.fromJson(Map<String, dynamic> json) => _$TutorCategoryFromJson(json);
  Map<String, dynamic> toJson() => _$TutorCategoryToJson(this);

  TutorCategory copyWith({
    int? id,
    String? name,
    int? tutorCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return TutorCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      tutorCount: tutorCount ?? this.tutorCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class TutorReview {
  final int id;
  @JsonKey(name: 'tutor_id')
  final int tutorId;
  @JsonKey(name: 'student_id')
  final int studentId;
  @JsonKey(name: 'session_id')
  final int? sessionId;
  final int rating;
  final String? comment;
  @JsonKey(name: 'is_anonymous')
  final bool isAnonymous;
  @JsonKey(name: 'is_approved')
  final bool isApproved;
  final Student? student;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  TutorReview({
    required this.id,
    required this.tutorId,
    required this.studentId,
    this.sessionId,
    required this.rating,
    this.comment,
    required this.isAnonymous,
    required this.isApproved,
    this.student,
    this.createdAt,
    this.updatedAt,
  });

  factory TutorReview.fromJson(Map<String, dynamic> json) => _$TutorReviewFromJson(json);
  Map<String, dynamic> toJson() => _$TutorReviewToJson(this);

  String get displayName => isAnonymous ? 'Anonymous' : (student?.name ?? 'Unknown');

  TutorReview copyWith({
    int? id,
    int? tutorId,
    int? studentId,
    int? sessionId,
    int? rating,
    String? comment,
    bool? isAnonymous,
    bool? isApproved,
    Student? student,
    String? createdAt,
    String? updatedAt,
  }) {
    return TutorReview(
      id: id ?? this.id,
      tutorId: tutorId ?? this.tutorId,
      studentId: studentId ?? this.studentId,
      sessionId: sessionId ?? this.sessionId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      isApproved: isApproved ?? this.isApproved,
      student: student ?? this.student,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class Admin {
  final int id;
  final String name;
  final String email;
  @JsonKey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  final String? phone;
  final String? avatar;
  final String role;
  final String status;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  Admin({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    this.phone,
    this.avatar,
    this.role = 'admin',
    this.status = 'active',
    this.createdAt,
    this.updatedAt,
  });

  factory Admin.fromJson(Map<String, dynamic> json) => _$AdminFromJson(json);
  Map<String, dynamic> toJson() => _$AdminToJson(this);

  // Helper methods
  bool get isActive => status == 'active';
  bool get isSuperAdmin => role == 'super_admin';
  bool get hasAvatar => avatar != null && avatar!.isNotEmpty;

  DateTime? get createdDateTime {
    try {
      return createdAt != null ? DateTime.parse(createdAt!) : null;
    } catch (e) {
      return null;
    }
  }

  DateTime? get updatedDateTime {
    try {
      return updatedAt != null ? DateTime.parse(updatedAt!) : null;
    } catch (e) {
      return null;
    }
  }

  Admin copyWith({
    int? id,
    String? name,
    String? email,
    String? emailVerifiedAt,
    String? phone,
    String? avatar,
    String? role,
    String? status,
    String? createdAt,
    String? updatedAt,
  }) {
    return Admin(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
