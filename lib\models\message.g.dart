// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
      id: (json['id'] as num).toInt(),
      senderId: (json['sender_id'] as num).toInt(),
      receiverId: (json['receiver_id'] as num).toInt(),
      senderType: json['sender_type'] as String,
      receiverType: json['receiver_type'] as String,
      message: json['message'] as String,
      messageType: json['message_type'] as String? ?? 'text',
      filePath: json['file_path'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      readAt: json['read_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
      'id': instance.id,
      'sender_id': instance.senderId,
      'receiver_id': instance.receiverId,
      'sender_type': instance.senderType,
      'receiver_type': instance.receiverType,
      'message': instance.message,
      'message_type': instance.messageType,
      'file_path': instance.filePath,
      'is_read': instance.isRead,
      'read_at': instance.readAt,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

Conversation _$ConversationFromJson(Map<String, dynamic> json) => Conversation(
      id: (json['id'] as num).toInt(),
      otherUserId: (json['other_user_id'] as num).toInt(),
      otherUserType: json['other_user_type'] as String,
      otherUserName: json['other_user_name'] as String,
      otherUserAvatar: json['other_user_avatar'] as String?,
      lastMessage: json['last_message'] as String?,
      lastMessageAt: json['last_message_at'] as String?,
      unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ConversationToJson(Conversation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'other_user_id': instance.otherUserId,
      'other_user_type': instance.otherUserType,
      'other_user_name': instance.otherUserName,
      'other_user_avatar': instance.otherUserAvatar,
      'last_message': instance.lastMessage,
      'last_message_at': instance.lastMessageAt,
      'unread_count': instance.unreadCount,
    };

SendMessageRequest _$SendMessageRequestFromJson(Map<String, dynamic> json) =>
    SendMessageRequest(
      receiverId: (json['receiver_id'] as num).toInt(),
      receiverType: json['receiver_type'] as String,
      message: json['message'] as String,
      senderType: json['sender_type'] as String?,
      senderId: (json['sender_id'] as num?)?.toInt(),
      messageType: json['message_type'] as String? ?? 'text',
    );

Map<String, dynamic> _$SendMessageRequestToJson(SendMessageRequest instance) =>
    <String, dynamic>{
      'receiver_id': instance.receiverId,
      'receiver_type': instance.receiverType,
      'sender_type': instance.senderType,
      'sender_id': instance.senderId,
      'message': instance.message,
      'message_type': instance.messageType,
    };

UnreadCount _$UnreadCountFromJson(Map<String, dynamic> json) => UnreadCount(
      unreadCount: (json['unread_count'] as num).toInt(),
    );

Map<String, dynamic> _$UnreadCountToJson(UnreadCount instance) =>
    <String, dynamic>{
      'unread_count': instance.unreadCount,
    };
