import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/profile_service.dart';
import '../models/profile_update_request.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/image_upload_helper.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _bioController = TextEditingController();
  final _locationController = TextEditingController();
  
  bool _isLoading = false;
  String _selectedGender = 'Not specified';
  DateTime? _selectedDate;
  Uint8List? _selectedImageBytes;
  String? _selectedImageName;
  final ImagePicker _imagePicker = ImagePicker();
  final ProfileService _profileService = ProfileService();

  // Additional fields for tutors/students
  final _experienceController = TextEditingController();
  final _educationController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  final _qualificationsController = TextEditingController();
  final _languagesController = TextEditingController();
  final _teachingStyleController = TextEditingController();
  final _specializationsController = TextEditingController();

  // Student fields
  final _gradeLevelController = TextEditingController();
  final _schoolController = TextEditingController();
  final _goalsController = TextEditingController();
  final _budgetRangeController = TextEditingController();

  String? _userType;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    if (!mounted) return;

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Wait a bit if the provider is still loading
      if (authProvider.isLoading) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (mounted && authProvider.isAuthenticated) {
        setState(() {
          _nameController.text = authProvider.userName ?? '';
          _emailController.text = authProvider.userEmail ?? '';
          _userType = authProvider.userType;
          // For other fields, we'll need to get them from the user profile
          // For now, keep the SharedPreferences fallback
        });

        // Load additional profile data from SharedPreferences as fallback
        final prefs = await SharedPreferences.getInstance();
        if (mounted) {
          setState(() {
            _phoneController.text = prefs.getString('phone') ?? '';
            _bioController.text = prefs.getString('bio') ?? '';
            _locationController.text = prefs.getString('location') ?? '';
            String? gender = prefs.getString('gender');
            _selectedGender = _capitalizeGender(gender) ?? 'Not specified';

            final dateString = prefs.getString('birthdate');
            if (dateString != null) {
              _selectedDate = DateTime.tryParse(dateString);
            }
          });
        }
      }
    } catch (e) {
      // Fallback to SharedPreferences if AuthProvider fails
      final prefs = await SharedPreferences.getInstance();
      if (mounted) {
        setState(() {
          _nameController.text = prefs.getString('fullname') ?? '';
          _emailController.text = prefs.getString('email') ?? '';
          _phoneController.text = prefs.getString('phone') ?? '';
          _bioController.text = prefs.getString('bio') ?? '';
          _locationController.text = prefs.getString('location') ?? '';
          String? gender = prefs.getString('gender');
          _selectedGender = _capitalizeGender(gender) ?? 'Not specified';

          final dateString = prefs.getString('birthdate');
          if (dateString != null) {
            _selectedDate = DateTime.tryParse(dateString);
          }
        });
      }
    }
  }

  Future<void> _showImageSourceDialog() async {
    final result = await ImageUploadHelper.pickAndValidateImage(
      context,
      _imagePicker,
    );

    if (result != null) {
      setState(() {
        _selectedImageBytes = result['bytes'] as Uint8List;
        _selectedImageName = result['name'] as String;
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Prepare tutor data if user is a tutor
      TutorUpdateData? tutorData;
      if (_userType == 'tutor') {
        tutorData = TutorUpdateData(
          experienceYears: _experienceController.text.isNotEmpty
              ? int.tryParse(_experienceController.text) : null,
          educationLevel: _educationController.text.isNotEmpty
              ? _educationController.text : null,
          hourlyRate: _hourlyRateController.text.isNotEmpty
              ? double.tryParse(_hourlyRateController.text) : null,
          bio: _bioController.text.isNotEmpty ? _bioController.text : null,
          qualifications: _qualificationsController.text.isNotEmpty
              ? _qualificationsController.text.split(',').map((e) => e.trim()).toList() : null,
          languages: _languagesController.text.isNotEmpty
              ? _languagesController.text.split(',').map((e) => e.trim()).toList() : null,
          teachingStyle: _teachingStyleController.text.isNotEmpty
              ? _teachingStyleController.text : null,
          specializations: _specializationsController.text.isNotEmpty
              ? _specializationsController.text.split(',').map((e) => e.trim()).toList() : null,
          location: _locationController.text.isNotEmpty ? _locationController.text : null,
        );
      }

      // Prepare student data if user is a student
      StudentUpdateData? studentData;
      if (_userType == 'student') {
        studentData = StudentUpdateData(
          gradeLevel: _gradeLevelController.text.isNotEmpty
              ? _gradeLevelController.text : null,
          school: _schoolController.text.isNotEmpty
              ? _schoolController.text : null,
          goals: _goalsController.text.isNotEmpty
              ? _goalsController.text : null,
          budgetRange: _budgetRangeController.text.isNotEmpty
              ? _budgetRangeController.text : null,
        );
      }

      // Use the ProfileService to update profile
      final response = await _profileService.updateProfile(
        name: _nameController.text.isNotEmpty ? _nameController.text : null,
        phone: _phoneController.text.isNotEmpty ? _phoneController.text : null,
        dateOfBirth: _selectedDate?.toIso8601String(),
        gender: _selectedGender != 'Not specified' ? _selectedGender.toLowerCase() : null,
        address: _locationController.text.isNotEmpty ? _locationController.text : null,
        avatarBytes: _selectedImageBytes,
        avatarFileName: _selectedImageName,
        tutorData: tutorData,
        studentData: studentData,
      );

      if (response.isSuccess) {
        // Also save to SharedPreferences for backward compatibility
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fullname', _nameController.text);
        await prefs.setString('email', _emailController.text);
        await prefs.setString('phone', _phoneController.text);
        await prefs.setString('bio', _bioController.text);
        await prefs.setString('location', _locationController.text);
        await prefs.setString('gender', _selectedGender);

        if (_selectedDate != null) {
          await prefs.setString('birthdate', _selectedDate!.toIso8601String());
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          String errorMessage = response.message ?? 'Failed to update profile';

          // Handle specific error cases
          if (response.errors != null) {
            final errors = response.errors as Map<String, dynamic>;
            if (errors.containsKey('avatar')) {
              errorMessage = 'Avatar upload failed: ${errors['avatar'][0]}';
            } else if (errors.containsKey('name')) {
              errorMessage = 'Name validation failed: ${errors['name'][0]}';
            } else if (errors.containsKey('phone')) {
              errorMessage = 'Phone validation failed: ${errors['phone'][0]}';
            }
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    _locationController.dispose();
    _experienceController.dispose();
    _educationController.dispose();
    _hourlyRateController.dispose();
    _qualificationsController.dispose();
    _languagesController.dispose();
    _teachingStyleController.dispose();
    _specializationsController.dispose();
    _gradeLevelController.dispose();
    _schoolController.dispose();
    _goalsController.dispose();
    _budgetRangeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: const Text(
              'Save',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildProfilePicture(),
            const SizedBox(height: 24),
            _buildPersonalInfoSection(),
            const SizedBox(height: 24),
            _buildContactInfoSection(),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(),
            const SizedBox(height: 24),
            if (_userType == 'tutor') _buildTutorSpecificSection(),
            if (_userType == 'student') _buildStudentSpecificSection(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePicture() {
    return Center(
      child: Stack(
        children: [
          CircleAvatar(
            radius: 60,
            backgroundColor: Colors.blue.shade100,
            backgroundImage: _selectedImageBytes != null
                ? MemoryImage(_selectedImageBytes!)
                : null,
            child: _selectedImageBytes == null
                ? Icon(
                    Icons.person,
                    size: 60,
                    color: Colors.blue.shade700,
                  )
                : null,
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blue.shade700,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: IconButton(
                icon: const Icon(Icons.camera_alt, color: Colors.white, size: 20),
                onPressed: _showImageSourceDialog,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return _buildSection(
      title: 'Personal Information',
      children: [
        _buildTextField(
          controller: _nameController,
          label: 'Full Name',
          icon: Icons.person_outline,
          validator: (value) {
            if (value?.isEmpty ?? true) return 'Name is required';
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildGenderDropdown(),
        const SizedBox(height: 16),
        _buildDatePicker(),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return _buildSection(
      title: 'Contact Information',
      children: [
        _buildTextField(
          controller: _emailController,
          label: 'Email',
          icon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value?.isEmpty ?? true) return 'Email is required';
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
              return 'Enter a valid email';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          icon: Icons.phone_outlined,
          keyboardType: TextInputType.phone,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _locationController,
          label: 'Location',
          icon: Icons.location_on_outlined,
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoSection() {
    return _buildSection(
      title: 'Additional Information',
      children: [
        _buildTextField(
          controller: _bioController,
          label: 'Bio',
          icon: Icons.description_outlined,
          maxLines: 3,
          hint: 'Tell us about yourself...',
        ),
      ],
    );
  }

  Widget _buildTutorSpecificSection() {
    return _buildSection(
      title: 'Tutor Information',
      children: [
        _buildTextField(
          controller: _experienceController,
          label: 'Years of Experience',
          icon: Icons.work_outline,
          keyboardType: TextInputType.number,
          hint: 'e.g., 5',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _educationController,
          label: 'Education Level',
          icon: Icons.school_outlined,
          hint: 'e.g., Bachelor\'s Degree in Mathematics',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _hourlyRateController,
          label: 'Hourly Rate (USD)',
          icon: Icons.attach_money,
          keyboardType: TextInputType.number,
          hint: 'e.g., 25',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _qualificationsController,
          label: 'Qualifications',
          icon: Icons.verified_outlined,
          maxLines: 2,
          hint: 'List your certifications and qualifications...',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _languagesController,
          label: 'Languages',
          icon: Icons.language_outlined,
          hint: 'e.g., English, Spanish, French',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _teachingStyleController,
          label: 'Teaching Style',
          icon: Icons.psychology_outlined,
          maxLines: 2,
          hint: 'Describe your teaching approach...',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _specializationsController,
          label: 'Specializations',
          icon: Icons.star_outline,
          hint: 'e.g., Algebra, Calculus, Statistics',
        ),
      ],
    );
  }

  Widget _buildStudentSpecificSection() {
    return _buildSection(
      title: 'Student Information',
      children: [
        _buildTextField(
          controller: _gradeLevelController,
          label: 'Grade Level',
          icon: Icons.grade_outlined,
          hint: 'e.g., Grade 10, University',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _schoolController,
          label: 'School/Institution',
          icon: Icons.school_outlined,
          hint: 'e.g., ABC High School',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _goalsController,
          label: 'Learning Goals',
          icon: Icons.flag_outlined,
          maxLines: 3,
          hint: 'What do you want to achieve?',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          controller: _budgetRangeController,
          label: 'Budget Range (USD/hour)',
          icon: Icons.attach_money,
          hint: 'e.g., 15-30',
        ),
      ],
    );
  }

  Widget _buildSection({required String title, required List<Widget> children}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.blue.shade700),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.blue.shade700, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: InputDecoration(
        labelText: 'Gender',
        prefixIcon: Icon(Icons.person_outline, color: Colors.blue.shade700),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.blue.shade700, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      items: ['Male', 'Female', 'Other', 'Not specified']
          .map((gender) => DropdownMenuItem(value: gender, child: Text(gender)))
          .toList(),
      onChanged: (value) => setState(() => _selectedGender = value!),
    );
  }

  Widget _buildDatePicker() {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _selectedDate ?? DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
          firstDate: DateTime(1950),
          lastDate: DateTime.now(),
        );
        if (date != null) {
          setState(() => _selectedDate = date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'Date of Birth',
          prefixIcon: Icon(Icons.calendar_today, color: Colors.blue.shade700),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.blue.shade700, width: 2),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
        ),
        child: Text(
          _selectedDate != null
              ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
              : 'Select date of birth',
          style: TextStyle(
            color: _selectedDate != null ? Colors.black87 : Colors.grey.shade600,
          ),
        ),
      ),
    );
  }

  // Helper method to convert gender from API format to dropdown format
  String? _capitalizeGender(String? gender) {
    if (gender == null) return null;
    switch (gender.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'other':
        return 'Other';
      default:
        return 'Not specified';
    }
  }
}
