import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'auth.g.dart';

@JsonSerializable()
class AuthResponse {
  final dynamic user; // Can be Student or Tutor
  final String token;
  @J<PERSON><PERSON>ey(name: 'token_type')
  final String? tokenType;

  AuthResponse({
    required this.user,
    required this.token,
    this.tokenType,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    // Handle the nested data structure from API
    final data = json['data'] as Map<String, dynamic>? ?? json;
    return AuthResponse(
      user: data['user'],
      token: data['token'] as String,
      tokenType: data['token_type'] as String?,
    );
  }

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);

  // Get user type from the user object
  String get userType {
    if (user is Map<String, dynamic>) {
      return (user as Map<String, dynamic>)['user_type'] as String? ?? 'student';
    }
    return 'student';
  }

  // Helper methods
  bool get isStudent => userType == 'student';
  bool get isTutor => userType == 'tutor';
  bool get isAdmin => userType == 'admin';

  Student? get studentUser => isStudent ? Student.fromJson(user as Map<String, dynamic>) : null;
  Tutor? get tutorUser => isTutor && (user as Map<String, dynamic>)['tutor'] != null
      ? Tutor.fromJson((user as Map<String, dynamic>)['tutor'] as Map<String, dynamic>)
      : null;
  Admin? get adminUser => isAdmin ? Admin.fromJson(user as Map<String, dynamic>) : null;

  String get userName {
    if (isStudent) {
      return studentUser?.name ?? 'Student';
    } else if (isTutor) {
      // For tutors, try tutor name first, then fall back to user name from the main user object
      final tutorName = tutorUser?.name;
      if (tutorName != null && tutorName.isNotEmpty) {
        return tutorName;
      }
      // Fall back to user name from the main user object
      final userData = user as Map<String, dynamic>;
      return userData['name'] as String? ?? 'Tutor';
    } else if (isAdmin) {
      return adminUser?.name ?? 'Admin';
    } else {
      return 'User';
    }
  }

  String? get userEmail {
    if (isStudent) {
      return studentUser?.email;
    } else if (isTutor) {
      // For tutors, fall back to user email from the main user object
      final userData = user as Map<String, dynamic>;
      return userData['email'] as String?;
    } else if (isAdmin) {
      return adminUser?.email;
    } else {
      return null;
    }
  }

  int get userId {
    if (isStudent) {
      return studentUser?.id ?? 0;
    } else if (isTutor) {
      // For tutors, return the user ID from the main user object (not the tutor profile ID)
      final userData = user as Map<String, dynamic>;
      return userData['id'] as int? ?? 0;
    } else if (isAdmin) {
      return adminUser?.id ?? 0;
    } else {
      return 0;
    }
  }
}

@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;
  @JsonKey(name: 'user_type')
  final String userType;

  LoginRequest({
    required this.email,
    required this.password,
    required this.userType,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class StudentRegisterRequest {
  final String name;
  final String email;
  final String password;
  @JsonKey(name: 'password_confirmation')
  final String passwordConfirmation;
  final String? phone;

  StudentRegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.passwordConfirmation,
    this.phone,
  });

  factory StudentRegisterRequest.fromJson(Map<String, dynamic> json) => _$StudentRegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$StudentRegisterRequestToJson(this);
}

@JsonSerializable()
class TutorRegisterRequest {
  final String name;
  final String email;
  final String password;
  @JsonKey(name: 'password_confirmation')
  final String passwordConfirmation;
  final String? phone;
  final String? description;
  final String? location;
  @JsonKey(name: 'hourly_rate')
  final double? hourlyRate;
  @JsonKey(name: 'experience_years')
  final int? experienceYears;
  final String? qualifications;
  final List<String>? languages;

  TutorRegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.passwordConfirmation,
    this.phone,
    this.description,
    this.location,
    this.hourlyRate,
    this.experienceYears,
    this.qualifications,
    this.languages,
  });

  factory TutorRegisterRequest.fromJson(Map<String, dynamic> json) => _$TutorRegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$TutorRegisterRequestToJson(this);
}

@JsonSerializable()
class UpdateProfileRequest {
  final String? name;
  final String? phone;
  @JsonKey(name: 'date_of_birth')
  final String? dateOfBirth;
  final String? gender;
  final String? location;
  final String? bio;
  final String? description;
  @JsonKey(name: 'hourly_rate')
  final double? hourlyRate;
  @JsonKey(name: 'experience_years')
  final int? experienceYears;
  final String? qualifications;
  final List<String>? languages;
  @JsonKey(name: 'is_online')
  final bool? isOnline;

  UpdateProfileRequest({
    this.name,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.location,
    this.bio,
    this.description,
    this.hourlyRate,
    this.experienceYears,
    this.qualifications,
    this.languages,
    this.isOnline,
  });

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) => _$UpdateProfileRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateProfileRequestToJson(this);
}
