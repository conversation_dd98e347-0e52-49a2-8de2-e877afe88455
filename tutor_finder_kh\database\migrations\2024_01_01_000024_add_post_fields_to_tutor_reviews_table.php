<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tutor_reviews', function (Blueprint $table) {
            // Add post_id foreign key
            $table->foreignId('post_id')->nullable()->after('session_id')->constrained('tutor_posts')->onDelete('cascade');
            
            // Add review_type to distinguish between different types of reviews
            $table->enum('review_type', ['tutor', 'session', 'post'])->default('tutor')->after('post_id');
            
            // Add indexes for better performance
            $table->index(['post_id', 'is_approved']);
            $table->index(['review_type', 'is_approved']);
            $table->index(['tutor_id', 'review_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tutor_reviews', function (Blueprint $table) {
            $table->dropForeign(['post_id']);
            $table->dropColumn(['post_id', 'review_type']);
        });
    }
};
