org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Kotlin options
kotlin.code.style=official
kotlin.incremental=false
kotlin.incremental.android=false

# Build optimization
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android build options
android.enableR8.fullMode=false
android.enableBuildCache=false
