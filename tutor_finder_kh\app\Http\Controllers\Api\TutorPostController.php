<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TutorPost;
use App\Models\PostComment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class TutorPostController extends Controller
{
    /**
     * Get all published posts with pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $query = TutorPost::with(['tutor.user', 'category'])
            ->published()
            ->orderBy('published_at', 'desc');

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by post type
        if ($request->has('post_type')) {
            $query->where('post_type', $request->post_type);
        }

        // Search functionality
        if ($request->has('search')) {
            $query->search($request->search);
        }

        // Filter by tutor
        if ($request->has('tutor_id')) {
            $query->where('tutor_id', $request->tutor_id);
        }

        $posts = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $posts,
        ]);
    }

    /**
     * Get featured posts.
     */
    public function featured(): JsonResponse
    {
        $posts = TutorPost::with(['tutor.user', 'category'])
            ->published()
            ->featured()
            ->orderBy('published_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $posts,
        ]);
    }

    /**
     * Get a specific post by slug.
     */
    public function show(string $slug): JsonResponse
    {
        $post = TutorPost::with(['tutor.user', 'category', 'reviews'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Increment views count
        $post->incrementViews();

        return response()->json([
            'success' => true,
            'data' => $post,
        ]);
    }

    /**
     * Create a new post (tutor only).
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'post_type' => 'required|in:article,tutorial,announcement,resource,tip',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|array',
            'featured_image' => 'nullable|image|max:2048',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        if (!$user || $user->user_type !== 'tutor') {
            return response()->json([
                'success' => false,
                'message' => 'Only tutors can create posts',
            ], 403);
        }

        // Check if user has a tutor profile
        $tutor = $user->tutor;
        if (!$tutor) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor profile not found. Please complete your tutor registration.',
            ], 404);
        }

        $data = $request->all();
        $data['tutor_id'] = $tutor->id;
        $data['slug'] = Str::slug($request->title);

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('posts/images', 'public');
        }

        // Handle attachments upload
        if ($request->hasFile('attachments')) {
            $attachments = [];
            foreach ($request->file('attachments') as $file) {
                $attachments[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $file->store('posts/attachments', 'public'),
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                ];
            }
            $data['attachments'] = $attachments;
        }

        // Set published_at if publishing
        if ($data['is_published'] ?? false) {
            $data['published_at'] = now();
        }

        $post = TutorPost::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Post created successfully',
            'data' => $post->load(['tutor.user', 'category']),
        ], 201);
    }

    /**
     * Update a post (tutor only - own posts).
     */
    public function update(Request $request, TutorPost $post): JsonResponse
    {
        $user = Auth::user();
        if (!$user || $user->user_type !== 'tutor') {
            return response()->json([
                'success' => false,
                'message' => 'Only tutors can update posts',
            ], 403);
        }

        $tutor = $user->tutor;
        if (!$tutor || $post->tutor_id !== $tutor->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this post',
            ], 403);
        }

        $request->validate([
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'excerpt' => 'nullable|string|max:500',
            'post_type' => 'sometimes|required|in:article,tutorial,announcement,resource,tip',
            'category_id' => 'nullable|exists:categories,id',
            'tags' => 'nullable|array',
            'featured_image' => 'nullable|image|max:2048',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        $data = $request->all();

        // Update slug if title changed
        if ($request->has('title')) {
            $data['slug'] = Str::slug($request->title);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($post->featured_image) {
                Storage::disk('public')->delete($post->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('posts/images', 'public');
        }

        // Set published_at if publishing for the first time
        if (($data['is_published'] ?? false) && !$post->published_at) {
            $data['published_at'] = now();
        }

        $post->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Post updated successfully',
            'data' => $post->load(['tutor.user', 'category']),
        ]);
    }

    /**
     * Delete a post (tutor only - own posts).
     */
    public function destroy(TutorPost $post): JsonResponse
    {
        $user = Auth::user();
        if (!$user || $user->user_type !== 'tutor') {
            return response()->json([
                'success' => false,
                'message' => 'Only tutors can delete posts',
            ], 403);
        }

        $tutor = $user->tutor;
        if (!$tutor || $post->tutor_id !== $tutor->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this post',
            ], 403);
        }

        // Delete associated files
        if ($post->featured_image) {
            Storage::disk('public')->delete($post->featured_image);
        }

        if ($post->attachments) {
            foreach ($post->attachments as $attachment) {
                Storage::disk('public')->delete($attachment['path']);
            }
        }

        $post->delete();

        return response()->json([
            'success' => true,
            'message' => 'Post deleted successfully',
        ]);
    }

    /**
     * Get posts by a specific tutor.
     */
    public function tutorPosts(Request $request, int $tutorId): JsonResponse
    {
        $query = TutorPost::with(['tutor.user', 'category'])
            ->where('tutor_id', $tutorId)
            ->published()
            ->orderBy('published_at', 'desc');

        $posts = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $posts,
        ]);
    }

    /**
     * Like/unlike a post.
     */
    public function toggleLike(TutorPost $post): JsonResponse
    {
        $user = Auth::user();
        
        if ($post->isLikedBy($user)) {
            $post->likedBy()->detach($user->id);
            $post->decrementLikes();
            $liked = false;
        } else {
            $post->likedBy()->attach($user->id);
            $post->incrementLikes();
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $post->fresh()->likes_count,
        ]);
    }

    /**
     * Bookmark/unbookmark a post.
     */
    public function toggleBookmark(TutorPost $post): JsonResponse
    {
        $user = Auth::user();
        
        if ($post->isBookmarkedBy($user)) {
            $post->bookmarkedBy()->detach($user->id);
            $bookmarked = false;
        } else {
            $post->bookmarkedBy()->attach($user->id);
            $bookmarked = true;
        }

        return response()->json([
            'success' => true,
            'bookmarked' => $bookmarked,
        ]);
    }
}
