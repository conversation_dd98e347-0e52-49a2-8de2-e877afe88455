import 'package:flutter/foundation.dart';
import '../models/group.dart';
import '../services/group_service.dart';

class GroupProvider with ChangeNotifier {
  final GroupService _groupService = GroupService();

  List<Group> _allGroups = [];
  List<Group> _userGroups = [];
  Group? _currentGroup;
  List<GroupMessage> _currentGroupMessages = [];
  
  bool _isLoading = false;
  bool _isLoadingMessages = false;
  String? _error;

  // Getters
  List<Group> get allGroups => _allGroups;
  List<Group> get userGroups => _userGroups;
  Group? get currentGroup => _currentGroup;
  List<GroupMessage> get currentGroupMessages => _currentGroupMessages;
  bool get isLoading => _isLoading;
  bool get isLoadingMessages => _isLoadingMessages;
  String? get error => _error;

  // Filters
  String? _selectedSubject;
  String? _searchQuery;
  String? _creatorTypeFilter;

  String? get selectedSubject => _selectedSubject;
  String? get searchQuery => _searchQuery;
  String? get creatorTypeFilter => _creatorTypeFilter;

  /// Load all available groups with filters
  Future<void> loadGroups({
    String? subject,
    String? creatorType,
    String? search,
    bool refresh = false,
  }) async {
    if (_isLoading && !refresh) return;

    _isLoading = true;
    _error = null;
    
    // Update filters
    _selectedSubject = subject;
    _creatorTypeFilter = creatorType;
    _searchQuery = search;
    
    notifyListeners();

    try {
      final groups = await _groupService.getGroups(
        subject: subject,
        creatorType: creatorType,
        search: search,
      );
      
      _allGroups = groups;
      _error = null;
    } catch (e) {
      _error = e.toString();
      _allGroups = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load user's joined groups
  Future<void> loadUserGroups({bool refresh = false}) async {
    if (_isLoading && !refresh) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final groups = await _groupService.getUserGroups();
      _userGroups = groups;
      _error = null;
    } catch (e) {
      _error = e.toString();
      _userGroups = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load a specific group
  Future<void> loadGroup(int groupId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final group = await _groupService.getGroup(groupId);
      _currentGroup = group;
      _error = null;
    } catch (e) {
      _error = e.toString();
      _currentGroup = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Create a new group
  Future<Group?> createGroup({
    required String name,
    String? description,
    String? subject,
    int maxMembers = 50,
    String groupType = 'study',
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final group = await _groupService.createGroup(
        name: name,
        description: description,
        subject: subject,
        maxMembers: maxMembers,
        groupType: groupType,
      );
      
      // Add to user groups
      _userGroups.insert(0, group);
      _error = null;
      notifyListeners();
      return group;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Join a group
  Future<bool> joinGroup(int groupId) async {
    try {
      final success = await _groupService.joinGroup(groupId);
      if (success) {
        // Refresh user groups and current group
        await loadUserGroups(refresh: true);
        if (_currentGroup?.id == groupId) {
          await loadGroup(groupId);
        }
      }
      return success;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Leave a group
  Future<bool> leaveGroup(int groupId) async {
    try {
      final success = await _groupService.leaveGroup(groupId);
      if (success) {
        // Remove from user groups
        _userGroups.removeWhere((group) => group.id == groupId);
        if (_currentGroup?.id == groupId) {
          _currentGroup = null;
          _currentGroupMessages = [];
        }
        notifyListeners();
      }
      return success;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Load group messages
  Future<void> loadGroupMessages(int groupId, {bool refresh = false}) async {
    if (_isLoadingMessages && !refresh) return;

    _isLoadingMessages = true;
    if (refresh) {
      _currentGroupMessages = [];
    }
    notifyListeners();

    try {
      final messages = await _groupService.getGroupMessages(groupId);
      _currentGroupMessages = messages;
      _error = null;
    } catch (e) {
      _error = e.toString();
      if (refresh) {
        _currentGroupMessages = [];
      }
    } finally {
      _isLoadingMessages = false;
      notifyListeners();
    }
  }

  /// Send a message to the current group
  Future<bool> sendMessage({
    required int groupId,
    required String message,
    String messageType = 'text',
    int? replyToId,
  }) async {
    try {
      final newMessage = await _groupService.sendGroupMessage(
        groupId: groupId,
        message: message,
        messageType: messageType,
        replyToId: replyToId,
      );
      
      // Add message to current messages
      _currentGroupMessages.add(newMessage);
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Clear current group
  void clearCurrentGroup() {
    _currentGroup = null;
    _currentGroupMessages = [];
    notifyListeners();
  }

  /// Apply filters
  void applyFilters({
    String? subject,
    String? creatorType,
    String? search,
  }) {
    loadGroups(
      subject: subject,
      creatorType: creatorType,
      search: search,
      refresh: true,
    );
  }

  /// Clear filters
  void clearFilters() {
    _selectedSubject = null;
    _creatorTypeFilter = null;
    _searchQuery = null;
    loadGroups(refresh: true);
  }
}
