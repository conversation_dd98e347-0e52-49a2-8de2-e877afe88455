-- =====================================================
-- TUTOR FINDER KH - RELATIONSHIPS AND ADDITIONAL DATA
-- =====================================================

USE tutor_finder_kh_app;

-- =====================================================
-- TUTOR CATEGORIES RELATIONSHIPS
-- =====================================================

INSERT INTO `tutor_categories` (`tutor_id`, `category_id`, `created_at`, `updated_at`) VALUES
-- Dr. <PERSON> - Mathematics
(1, 1, NOW(), NOW()),
(1, 25, NOW(), NOW()),
(1, 26, NOW(), NOW()),

-- Prof<PERSON> <PERSON> - <PERSON>, Mathematics
(2, 2, NOW(), NOW()),
(2, 1, NOW(), NOW()),
(2, 36, NOW(), NOW()),

-- Ms. Emily Davis - English
(3, 5, NOW(), NOW()),
(3, 19, NOW(), NOW()),
(3, 20, NOW(), NOW()),

-- Dr. <PERSON> <PERSON> - Chemistry, Biology
(4, 3, NOW(), NOW()),
(4, 4, NOW(), NOW()),

-- Ms. Lisa Park - Biology
(5, 4, NOW(), NOW()),
(5, 30, NOW(), NOW()),

-- Dr. Robert Kim - Computer Science, Programming
(6, 6, NOW(), NOW()),
(6, 7, NOW(), NOW()),
(6, 8, NOW(), NOW()),

-- Ms. Anna Martinez - Languages
(7, 11, NOW(), NOW()),
(7, 12, NOW(), NOW()),
(7, 21, NOW(), NOW()),

-- Dr. David Thompson - History, Geography
(8, 9, NOW(), NOW()),
(8, 10, NOW(), NOW()),
(8, 18, NOW(), NOW());

-- =====================================================
-- SESSIONS DATA
-- =====================================================

INSERT INTO `sessions` (`id`, `tutor_id`, `student_id`, `session_date`, `duration_minutes`, `price`, `status`, `notes`, `meeting_link`, `session_type`, `location`, `started_at`, `ended_at`, `cancellation_reason`, `created_at`, `updated_at`) VALUES
(1, 1, 1, DATE_ADD(NOW(), INTERVAL 1 DAY), 60, 25.00, 'booked', 'Focus on calculus derivatives and applications', 'https://meet.google.com/abc-defg-hij', 'online', NULL, NULL, NULL, NULL, NOW(), NOW()),

(2, 2, 1, DATE_SUB(NOW(), INTERVAL 2 DAY), 90, 45.00, 'completed', 'Physics mechanics - Newton\'s laws review', 'https://meet.google.com/xyz-uvwx-yz', 'online', NULL, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -90 MINUTE), NULL, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),

(3, 3, 2, DATE_ADD(NOW(), INTERVAL 3 DAY), 60, 20.00, 'confirmed', 'Essay writing techniques and structure', NULL, 'in_person', 'Battambang Public Library', NULL, NULL, NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(4, 4, 3, DATE_SUB(NOW(), INTERVAL 1 DAY), 75, 35.00, 'completed', 'Organic chemistry reactions and mechanisms', 'https://meet.google.com/chem-lab-session', 'online', NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 DAY), INTERVAL -75 MINUTE), NULL, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(5, 5, 6, DATE_ADD(NOW(), INTERVAL 2 DAY), 60, 22.00, 'booked', 'Ecosystem dynamics and biodiversity', 'https://meet.google.com/bio-eco-study', 'online', NULL, NULL, NULL, NULL, NOW(), NOW()),

(6, 6, 1, DATE_SUB(NOW(), INTERVAL 5 DAY), 120, 52.00, 'completed', 'JavaScript fundamentals and DOM manipulation', 'https://meet.google.com/code-session-js', 'online', NULL, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -120 MINUTE), NULL, DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

-- =====================================================
-- TUTOR REVIEWS DATA
-- =====================================================

INSERT INTO `tutor_reviews` (`id`, `tutor_id`, `student_id`, `session_id`, `rating`, `comment`, `is_anonymous`, `is_approved`, `review_type`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 2, 5, 'Prof. Chen is an excellent physics tutor! He explains complex concepts clearly and provides great examples.', 0, 1, 'tutor', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),

(2, 4, 3, 4, 4, 'Dr. Wilson made organic chemistry much easier to understand. Great use of visual aids!', 0, 1, 'tutor', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(3, 6, 1, 6, 5, 'Amazing programming tutor! Robert helped me understand JavaScript concepts that I was struggling with.', 0, 1, 'tutor', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY)),

(4, 1, 2, NULL, 5, 'Dr. Johnson is patient and thorough. Her teaching style really helps with understanding calculus.', 1, 1, 'tutor', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),

(5, 3, 4, NULL, 4, 'Ms. Davis helped me improve my essay writing significantly. Very encouraging teacher!', 0, 1, 'tutor', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

-- =====================================================
-- FAVORITES DATA
-- =====================================================

INSERT INTO `favorites` (`id`, `student_id`, `tutor_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY)),
(2, 1, 2, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, 2, 3, DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY)),
(4, 3, 4, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),
(5, 4, 7, DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY)),
(6, 6, 5, DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

-- =====================================================
-- CHATS DATA
-- =====================================================

INSERT INTO `chats` (`id`, `session_id`, `sender_id`, `sender_type`, `message`, `message_type`, `is_read`, `read_at`, `created_at`, `updated_at`) VALUES
(1, 1, 2, 'tutor', 'Hi John! Ready for our calculus session tomorrow?', 'text', 1, DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR)),

(2, 1, 10, 'student', 'Yes, Dr. Johnson! I\'ve been practicing the problems you sent.', 'text', 1, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

(3, 1, 2, 'tutor', 'Great! We\'ll focus on the chain rule and its applications.', 'text', 0, NULL, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

(4, 2, 3, 'tutor', 'Excellent work today on Newton\'s laws! Your understanding has improved significantly.', 'text', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -1 HOUR), DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -30 MINUTE), DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -1 HOUR)),

(5, 2, 10, 'student', 'Thank you Prof. Chen! The examples really helped clarify the concepts.', 'text', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -2 HOUR), DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -1 HOUR), DATE_SUB(DATE_SUB(NOW(), INTERVAL 2 DAY), INTERVAL -2 HOUR));

-- =====================================================
-- NOTIFICATIONS DATA
-- =====================================================

INSERT INTO `notifications` (`id`, `user_id`, `user_type`, `title`, `message`, `type`, `data`, `is_read`, `read_at`, `created_at`, `updated_at`) VALUES
(1, 10, 'student', 'Session Confirmed', 'Your calculus session with Dr. Sarah Johnson has been confirmed for tomorrow at 2:00 PM.', 'booking', '{"session_id":1,"tutor_name":"Dr. Sarah Johnson"}', 0, NULL, DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR)),

(2, 2, 'tutor', 'New Session Booking', 'John Smith has booked a session with you for tomorrow at 2:00 PM.', 'booking', '{"session_id":1,"student_name":"John Smith"}', 1, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

(3, 3, 'tutor', 'New Review Received', 'You received a 5-star review from John Smith for your physics session.', 'review', '{"review_id":1,"rating":5,"student_name":"John Smith"}', 0, NULL, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),

(4, 11, 'student', 'Session Reminder', 'Your English session with Ms. Emily Davis is scheduled for tomorrow at 10:00 AM.', 'booking', '{"session_id":3,"tutor_name":"Ms. Emily Davis"}', 0, NULL, DATE_SUB(NOW(), INTERVAL 12 HOUR), DATE_SUB(NOW(), INTERVAL 12 HOUR)),

(5, 12, 'student', 'Session Completed', 'Your chemistry session with Dr. James Wilson has been completed. Please leave a review!', 'system', '{"session_id":4,"tutor_name":"Dr. James Wilson"}', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 DAY), INTERVAL -2 HOUR), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 1 DAY), INTERVAL -2 HOUR));
