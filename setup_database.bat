@echo off
echo ========================================
echo    Database Setup for Tutor Finder
echo ========================================
echo.

echo Step 1: Create MySQL Database
echo Please run this SQL command in your MySQL client:
echo.
echo CREATE DATABASE tutor_finder_kh;
echo.
echo Press any key after creating the database...
pause

echo.
echo Step 2: Navigate to Laravel directory
cd tutor_finder_kh

echo Step 3: Clear config cache
php artisan config:clear

echo Step 4: Run migrations
php artisan migrate

echo Step 5: Seed database with sample data (optional)
set /p choice="Do you want to seed the database with sample data? (y/n): "
if /i "%choice%"=="y" (
    php artisan db:seed
)

echo.
echo ========================================
echo    Database Setup Complete!
echo ========================================
echo.
echo You can now start the Laravel server with:
echo php artisan serve
echo.
pause
