# TutorFinder API

A comprehensive Laravel-based REST API for the TutorFinder mobile application, providing endpoints for tutor discovery, student-tutor interactions, session management, and more.

## 🚀 Features

- **User Authentication**: Separate authentication for students, tutors, and admins
- **Tutor Management**: Browse, search, and filter tutors by various criteria
- **Session Booking**: Complete session lifecycle management
- **Favorites System**: Students can favorite tutors
- **Reviews & Ratings**: Comprehensive review system
- **Real-time Messaging**: In-app messaging between students and tutors
- **Notifications**: Push notifications for important events
- **Admin Panel**: Administrative functions for managing the platform

## 📋 Requirements

- PHP 8.1+
- <PERSON>vel 10.x
- MySQL 8.0+
- Composer

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd tutorfinder-api
```

2. **Install dependencies**
```bash
composer install
```

3. **Environment setup**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Configure database**
Update your `.env` file with database credentials:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tutorfinder
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

5. **Run migrations and seeders**
```bash
php artisan migrate
php artisan db:seed
```

6. **Install Sanctum**
```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

7. **Start the server**
```bash
php artisan serve
```

## 🔐 Authentication

The API uses Laravel Sanctum for authentication. All protected endpoints require a Bearer token.

### Getting a Token

**Student Registration:**
```http
POST /api/v1/auth/register/student
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "phone": "+**********"
}
```

**Tutor Registration:**
```http
POST /api/v1/auth/register/tutor
Content-Type: application/json

{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password",
    "phone": "+**********",
    "description": "Experienced math tutor",
    "hourly_rate": 25.00,
    "experience_years": 5
}
```

**Login:**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "user_type": "student"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>"
        },
        "token": "1|abc123...",
        "user_type": "student"
    }
}
```

### Using the Token

Include the token in the Authorization header:
```http
Authorization: Bearer 1|abc123...
```

## 📚 API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/auth/register/student` | Register a new student | No |
| POST | `/api/v1/auth/register/tutor` | Register a new tutor | No |
| POST | `/api/v1/auth/login` | Login user | No |
| POST | `/api/v1/auth/logout` | Logout user | Yes |
| GET | `/api/v1/auth/profile` | Get user profile | Yes |
| PUT | `/api/v1/auth/profile` | Update user profile | Yes |

### Tutor Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/tutors` | Get all tutors with filters | No |
| GET | `/api/v1/tutors/featured` | Get featured tutors | No |
| GET | `/api/v1/tutors/search` | Search tutors | No |
| GET | `/api/v1/tutors/{id}` | Get specific tutor | No |
| GET | `/api/v1/tutors/{id}/availability` | Get tutor availability | No |
| GET | `/api/v1/tutors/category/{categoryId}` | Get tutors by category | No |

### Category Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/categories` | Get all categories | No |
| GET | `/api/v1/categories/popular` | Get popular categories | No |
| GET | `/api/v1/categories/{id}` | Get specific category | No |

### Favorites Endpoints (Student Only)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/favorites` | Get user's favorites | Yes (Student) |
| POST | `/api/v1/favorites` | Add tutor to favorites | Yes (Student) |
| DELETE | `/api/v1/favorites/{tutorId}` | Remove from favorites | Yes (Student) |
| GET | `/api/v1/favorites/check/{tutorId}` | Check if favorited | Yes (Student) |
| GET | `/api/v1/favorites/count` | Get favorites count | Yes (Student) |

### Session Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/sessions` | Get user's sessions | Yes (Student) |
| POST | `/api/v1/sessions` | Book a session | Yes (Student) |
| GET | `/api/v1/sessions/{id}` | Get specific session | Yes |
| PUT | `/api/v1/sessions/{id}/cancel` | Cancel session | Yes |
| GET | `/api/v1/sessions/upcoming` | Get upcoming sessions | Yes (Student) |
| GET | `/api/v1/sessions/past` | Get past sessions | Yes (Student) |

### Tutor Session Endpoints (Tutor Only)

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/tutor/sessions` | Get tutor's sessions | Yes (Tutor) |
| PUT | `/api/v1/tutor/sessions/{id}/confirm` | Confirm session | Yes (Tutor) |
| PUT | `/api/v1/tutor/sessions/{id}/start` | Start session | Yes (Tutor) |
| PUT | `/api/v1/tutor/sessions/{id}/complete` | Complete session | Yes (Tutor) |

### Review Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/reviews/tutor/{tutorId}` | Get tutor reviews | No |
| POST | `/api/v1/reviews` | Create review | Yes (Student) |
| GET | `/api/v1/reviews/my-reviews` | Get user's reviews | Yes (Student) |
| PUT | `/api/v1/reviews/{id}` | Update review | Yes (Student) |
| DELETE | `/api/v1/reviews/{id}` | Delete review | Yes (Student) |

### Message Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/messages` | Get user's messages | Yes |
| POST | `/api/v1/messages` | Send message | Yes |
| GET | `/api/v1/messages/conversations` | Get conversations | Yes |
| GET | `/api/v1/messages/conversation/{userId}/{userType}` | Get specific conversation | Yes |
| PUT | `/api/v1/messages/{id}/read` | Mark message as read | Yes |
| GET | `/api/v1/messages/unread-count` | Get unread count | Yes |

### Notification Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/notifications` | Get user's notifications | Yes |
| PUT | `/api/v1/notifications/{id}/read` | Mark as read | Yes |
| PUT | `/api/v1/notifications/mark-all-read` | Mark all as read | Yes |
| DELETE | `/api/v1/notifications/{id}` | Delete notification | Yes |
| GET | `/api/v1/notifications/unread-count` | Get unread count | Yes |

## 🔍 Query Parameters

### Tutor Search & Filtering

**GET `/api/v1/tutors`** supports the following query parameters:

- `search` - Search by name or description
- `category_id` - Filter by category ID
- `location` - Filter by location
- `is_online` - Filter by online availability (true/false)
- `min_price` - Minimum hourly rate
- `max_price` - Maximum hourly rate
- `min_rating` - Minimum average rating
- `sort_by` - Sort by: rating, price, experience, created_at
- `sort_order` - Sort order: asc, desc
- `per_page` - Results per page (default: 15, max: 50)

**Example:**
```http
GET /api/v1/tutors?search=math&category_id=1&min_rating=4&sort_by=rating&sort_order=desc&per_page=10
```

## 📝 Response Format

All API responses follow this format:

**Success Response:**
```json
{
    "success": true,
    "message": "Operation successful",
    "data": {
        // Response data
    }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Error message",
    "errors": {
        // Validation errors (if applicable)
    }
}
```

## 🚦 HTTP Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity (Validation Error)
- `500` - Internal Server Error

## 🧪 Testing

Run the test suite:
```bash
php artisan test
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.
