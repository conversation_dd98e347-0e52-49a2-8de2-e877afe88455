<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdminUser;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin
        AdminUser::create([
            'username' => 'superadmin',
            'email' => '<EMAIL>',
            'name' => 'Super Administrator',
            'password' => Hash::make('password'),
            'role' => 'super_admin',
            'is_active' => true,
        ]);

        // Create regular admin
        AdminUser::create([
            'username' => 'admin',
            'email' => '<EMAIL>',
            'name' => 'Administrator',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create moderator
        AdminUser::create([
            'username' => 'moderator',
            'email' => '<EMAIL>',
            'name' => 'Moderator',
            'password' => Hash::make('password'),
            'role' => 'moderator',
            'is_active' => true,
        ]);
    }
}
