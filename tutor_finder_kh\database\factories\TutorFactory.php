<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tutor>
 */
class TutorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'phone' => fake()->phoneNumber(),
            'description' => fake()->paragraph(3),
            'is_online' => fake()->boolean(80), // 80% chance of being online
            'location' => fake()->city() . ', ' . fake()->state(),
            'hourly_rate' => fake()->randomFloat(2, 15, 100),
            'experience_years' => fake()->numberBetween(1, 20),
            'qualifications' => fake()->sentence(10),
            'languages' => fake()->randomElements(['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese'], fake()->numberBetween(1, 3)),
            'status' => fake()->randomElement(['pending', 'approved', 'rejected']),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the tutor is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
        ]);
    }

    /**
     * Indicate that the tutor is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the tutor is online.
     */
    public function online(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_online' => true,
        ]);
    }

    /**
     * Indicate that the tutor is offline.
     */
    public function offline(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_online' => false,
        ]);
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
