<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('creator_id');
            $table->enum('creator_type', ['student', 'tutor', 'admin'])->default('tutor');
            $table->string('subject')->nullable();
            $table->integer('max_members')->default(50);
            $table->boolean('is_active')->default(true);
            $table->enum('group_type', ['study', 'discussion', 'announcement'])->default('study');
            $table->string('image')->nullable();
            $table->timestamps();

            $table->index(['creator_id', 'creator_type']);
            $table->index(['subject', 'is_active']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('groups');
    }
};
