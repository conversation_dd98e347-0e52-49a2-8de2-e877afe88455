<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Favorite;
use App\Models\Tutor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FavoriteController extends Controller
{
    /**
     * Get user's favorite tutors
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            
            $query = Favorite::with(['tutor.user'])
                ->where('user_id', $user->id);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $favorites = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $favorites->items(),
                'pagination' => [
                    'current_page' => $favorites->currentPage(),
                    'last_page' => $favorites->lastPage(),
                    'per_page' => $favorites->perPage(),
                    'total' => $favorites->total(),
                    'from' => $favorites->firstItem(),
                    'to' => $favorites->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch favorites',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add tutor to favorites
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tutor_id' => 'required|integer|exists:tutors,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $tutorId = $request->tutor_id;

            // Check if already favorited
            $existingFavorite = Favorite::where('user_id', $user->id)
                ->where('tutor_id', $tutorId)
                ->first();

            if ($existingFavorite) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tutor is already in favorites'
                ], 409);
            }

            // Check if tutor exists
            $tutor = Tutor::findOrFail($tutorId);

            // Create favorite
            $favorite = Favorite::create([
                'user_id' => $user->id,
                'tutor_id' => $tutorId,
            ]);

            // Load the favorite with tutor data
            $favorite->load('tutor.user');

            return response()->json([
                'success' => true,
                'message' => 'Tutor added to favorites successfully',
                'data' => $favorite
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add to favorites',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove tutor from favorites
     */
    public function destroy(Request $request, $tutorId)
    {
        try {
            $user = $request->user();

            $favorite = Favorite::where('user_id', $user->id)
                ->where('tutor_id', $tutorId)
                ->first();

            if (!$favorite) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tutor not found in favorites'
                ], 404);
            }

            $favorite->delete();

            return response()->json([
                'success' => true,
                'message' => 'Tutor removed from favorites successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove from favorites',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if tutor is favorited by user
     */
    public function check(Request $request, $tutorId)
    {
        try {
            $user = $request->user();

            $isFavorited = Favorite::where('user_id', $user->id)
                ->where('tutor_id', $tutorId)
                ->exists();

            return response()->json([
                'success' => true,
                'data' => [
                    'is_favorited' => $isFavorited,
                    'tutor_id' => (int) $tutorId
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check favorite status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get favorites count for user
     */
    public function count(Request $request)
    {
        try {
            $user = $request->user();

            $count = Favorite::where('user_id', $user->id)->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'count' => $count
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get favorites count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle favorite status
     */
    public function toggle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tutor_id' => 'required|integer|exists:tutors,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $tutorId = $request->tutor_id;

            $favorite = Favorite::where('user_id', $user->id)
                ->where('tutor_id', $tutorId)
                ->first();

            if ($favorite) {
                // Remove from favorites
                $favorite->delete();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Tutor removed from favorites',
                    'data' => [
                        'action' => 'removed',
                        'is_favorited' => false,
                        'tutor_id' => $tutorId
                    ]
                ]);
            } else {
                // Add to favorites
                $favorite = Favorite::create([
                    'user_id' => $user->id,
                    'tutor_id' => $tutorId,
                ]);

                $favorite->load('tutor.user');

                return response()->json([
                    'success' => true,
                    'message' => 'Tutor added to favorites',
                    'data' => [
                        'action' => 'added',
                        'is_favorited' => true,
                        'tutor_id' => $tutorId,
                        'favorite' => $favorite
                    ]
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle favorite',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all favorites for user
     */
    public function clear(Request $request)
    {
        try {
            $user = $request->user();

            $deletedCount = Favorite::where('user_id', $user->id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'All favorites cleared successfully',
                'data' => [
                    'deleted_count' => $deletedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear favorites',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get favorite tutors by subject
     */
    public function bySubject(Request $request)
    {
        $request->validate([
            'subject' => 'required|string',
        ]);

        try {
            $user = $request->user();
            $subject = $request->subject;

            $favorites = Favorite::with(['tutor.user'])
                ->where('user_id', $user->id)
                ->whereHas('tutor', function ($query) use ($subject) {
                    $query->where('subjects', 'LIKE', '%' . $subject . '%');
                })
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $favorites
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch favorites by subject',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
