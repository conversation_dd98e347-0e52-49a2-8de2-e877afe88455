<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            if (!Schema::hasColumn('tutors', 'experience_years')) {
                $table->integer('experience_years')->nullable()->after('education_level');
            }
            if (!Schema::hasColumn('tutors', 'hourly_rate')) {
                $table->decimal('hourly_rate', 8, 2)->nullable()->after('experience_years');
            }
            if (!Schema::hasColumn('tutors', 'qualifications')) {
                $table->json('qualifications')->nullable()->after('hourly_rate');
            }
            if (!Schema::hasColumn('tutors', 'languages')) {
                $table->json('languages')->nullable()->after('qualifications');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            $table->dropColumn(['experience_years', 'hourly_rate', 'qualifications', 'languages']);
        });
    }
};
