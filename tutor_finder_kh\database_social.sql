-- =====================================================
-- TUTOR FINDER KH - SOCIAL FEATURES DATA
-- =====================================================

USE tutor_finder_kh_app;

-- =====================================================
-- GROUPS DATA
-- =====================================================

INSERT INTO `groups` (`id`, `name`, `description`, `creator_id`, `creator_type`, `subject`, `max_members`, `is_active`, `group_type`, `image`, `created_at`, `updated_at`) VALUES
(1, 'Mathematics Study Group', 'A group for students learning calculus, algebra, and advanced mathematics topics.', 2, 'tutor', 'Mathematics', 25, 1, 'study', 'groups/math_study_group.jpg', DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(2, 'Physics Discussion Forum', 'Discuss physics concepts, share resources, and collaborate on problem-solving.', 3, 'tutor', 'Physics', 30, 1, 'discussion', 'groups/physics_discussion.jpg', DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),

(3, 'Programming Bootcamp', 'Learn programming fundamentals, share code, and work on projects together.', 7, 'tutor', 'Programming', 20, 1, 'study', 'groups/programming_bootcamp.jpg', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 6 HOUR)),

(4, 'English Writing Workshop', 'Improve your writing skills, share essays, and get feedback from peers.', 4, 'tutor', 'English', 15, 1, 'study', 'groups/writing_workshop.jpg', DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY));

-- =====================================================
-- GROUP MEMBERS DATA
-- =====================================================

INSERT INTO `group_members` (`id`, `group_id`, `user_id`, `role`, `joined_at`, `is_active`, `created_at`, `updated_at`) VALUES
-- Mathematics Study Group members
(1, 1, 10, 'member', DATE_SUB(NOW(), INTERVAL 10 DAY), 1, NOW(), NOW()),
(2, 1, 14, 'member', DATE_SUB(NOW(), INTERVAL 8 DAY), 1, NOW(), NOW()),
(3, 1, 12, 'member', DATE_SUB(NOW(), INTERVAL 5 DAY), 1, NOW(), NOW()),

-- Physics Discussion Forum members
(4, 2, 10, 'member', DATE_SUB(NOW(), INTERVAL 15 DAY), 1, NOW(), NOW()),
(5, 2, 12, 'member', DATE_SUB(NOW(), INTERVAL 12 DAY), 1, NOW(), NOW()),

-- Programming Bootcamp members
(6, 3, 10, 'member', DATE_SUB(NOW(), INTERVAL 7 DAY), 1, NOW(), NOW()),
(7, 3, 14, 'member', DATE_SUB(NOW(), INTERVAL 6 DAY), 1, NOW(), NOW()),

-- English Writing Workshop members
(8, 4, 11, 'member', DATE_SUB(NOW(), INTERVAL 9 DAY), 1, NOW(), NOW()),
(9, 4, 13, 'member', DATE_SUB(NOW(), INTERVAL 7 DAY), 1, NOW(), NOW()),
(10, 4, 15, 'member', DATE_SUB(NOW(), INTERVAL 4 DAY), 1, NOW(), NOW());

-- =====================================================
-- GROUP MESSAGES DATA
-- =====================================================

INSERT INTO `group_messages` (`id`, `group_id`, `sender_id`, `sender_type`, `message`, `message_type`, `created_at`, `updated_at`) VALUES
-- Mathematics Study Group messages
(1, 1, 2, 'tutor', 'Welcome to the Mathematics Study Group! Feel free to ask questions and share resources.', 'text', DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY)),

(2, 1, 10, 'student', 'Thank you Dr. Johnson! I have a question about derivatives.', 'text', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY)),

(3, 1, 12, 'student', 'Can someone help me with integration by parts?', 'text', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY)),

-- Programming Bootcamp messages
(4, 3, 7, 'tutor', 'Today we\'ll be covering JavaScript fundamentals. Please review the materials I shared.', 'text', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),

(5, 3, 10, 'student', 'I\'m having trouble with async/await. Could you explain it again?', 'text', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),

-- English Writing Workshop messages
(6, 4, 4, 'tutor', 'Remember to focus on your thesis statement - it should be clear and arguable.', 'text', DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 8 DAY)),

(7, 4, 11, 'student', 'I\'ve uploaded my essay draft. Could everyone please provide feedback?', 'text', DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

-- =====================================================
-- CONVERSATIONS DATA
-- =====================================================

INSERT INTO `conversations` (`id`, `user1_id`, `user2_id`, `last_message_at`, `created_at`, `updated_at`) VALUES
(1, 10, 2, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

(2, 11, 4, DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 6 HOUR)),

(3, 12, 5, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),

(4, 10, 7, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 6 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY));

-- =====================================================
-- MESSAGES DATA
-- =====================================================

INSERT INTO `messages` (`id`, `sender_id`, `receiver_id`, `sender_type`, `receiver_type`, `message`, `message_type`, `is_read`, `read_at`, `created_at`, `updated_at`) VALUES
-- Conversation 1: John Smith & Sarah Johnson
(1, 10, 2, 'student', 'tutor', 'Hi Dr. Johnson, I need help with calculus derivatives.', 'text', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -1 HOUR), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -1 HOUR)),

(2, 2, 10, 'tutor', 'student', 'Hello John! I\'d be happy to help you with derivatives. When would you like to schedule a session?', 'text', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -2 HOUR), DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -1 HOUR), DATE_SUB(DATE_SUB(NOW(), INTERVAL 5 DAY), INTERVAL -2 HOUR)),

(3, 10, 2, 'student', 'tutor', 'How about tomorrow at 2 PM? I\'m available then.', 'text', 0, NULL, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)),

-- Conversation 2: Emma Johnson & Emily Davis
(4, 11, 4, 'student', 'tutor', 'Ms. Davis, could you help me with essay structure for my English assignment?', 'text', 1, DATE_SUB(DATE_SUB(NOW(), INTERVAL 8 DAY), INTERVAL -2 HOUR), DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(DATE_SUB(NOW(), INTERVAL 8 DAY), INTERVAL -2 HOUR)),

(5, 4, 11, 'tutor', 'student', 'Of course, Emma! Let\'s work on creating a strong thesis statement and organizing your ideas.', 'text', 1, DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 6 HOUR), DATE_SUB(NOW(), INTERVAL 6 HOUR));

-- =====================================================
-- SUMMARY QUERY TO VERIFY DATA
-- =====================================================

-- Uncomment the following lines to verify the data after insertion:
/*
SELECT 'Users' as Table_Name, COUNT(*) as Count FROM users
UNION ALL
SELECT 'Categories', COUNT(*) FROM categories
UNION ALL
SELECT 'Tutors', COUNT(*) FROM tutors
UNION ALL
SELECT 'Students', COUNT(*) FROM students
UNION ALL
SELECT 'Sessions', COUNT(*) FROM sessions
UNION ALL
SELECT 'Reviews', COUNT(*) FROM tutor_reviews
UNION ALL
SELECT 'Favorites', COUNT(*) FROM favorites
UNION ALL
SELECT 'Groups', COUNT(*) FROM groups
UNION ALL
SELECT 'Group Members', COUNT(*) FROM group_members
UNION ALL
SELECT 'Messages', COUNT(*) FROM messages
UNION ALL
SELECT 'Notifications', COUNT(*) FROM notifications;
*/
