<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            // Add user_id foreign key if it doesn't exist
            if (!Schema::hasColumn('tutors', 'user_id')) {
                $table->foreignId('user_id')->after('id')->constrained('users')->onDelete('cascade');
            }
            
            // Add new fields
            if (!Schema::hasColumn('tutors', 'subjects')) {
                $table->json('subjects')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('tutors', 'education_level')) {
                $table->string('education_level')->nullable()->after('subjects');
            }
            if (!Schema::hasColumn('tutors', 'bio')) {
                $table->text('bio')->nullable()->after('education_level');
            }
            if (!Schema::hasColumn('tutors', 'availability')) {
                $table->json('availability')->nullable()->after('bio');
            }
            if (!Schema::hasColumn('tutors', 'latitude')) {
                $table->decimal('latitude', 10, 8)->nullable()->after('location');
            }
            if (!Schema::hasColumn('tutors', 'longitude')) {
                $table->decimal('longitude', 11, 8)->nullable()->after('latitude');
            }
            if (!Schema::hasColumn('tutors', 'is_verified')) {
                $table->boolean('is_verified')->default(false)->after('longitude');
            }
            if (!Schema::hasColumn('tutors', 'is_available')) {
                $table->boolean('is_available')->default(true)->after('is_verified');
            }
            if (!Schema::hasColumn('tutors', 'rating')) {
                $table->decimal('rating', 3, 2)->default(0)->after('is_available');
            }
            if (!Schema::hasColumn('tutors', 'reviews_count')) {
                $table->integer('reviews_count')->default(0)->after('rating');
            }
            if (!Schema::hasColumn('tutors', 'total_sessions')) {
                $table->integer('total_sessions')->default(0)->after('reviews_count');
            }
            if (!Schema::hasColumn('tutors', 'response_time')) {
                $table->integer('response_time')->nullable()->comment('Response time in minutes')->after('total_sessions');
            }
            if (!Schema::hasColumn('tutors', 'teaching_style')) {
                $table->string('teaching_style')->nullable()->after('response_time');
            }
            if (!Schema::hasColumn('tutors', 'specializations')) {
                $table->json('specializations')->nullable()->after('teaching_style');
            }
            
            // Update existing columns if they exist
            if (Schema::hasColumn('tutors', 'qualifications')) {
                $table->json('qualifications')->nullable()->change();
            }
            if (Schema::hasColumn('tutors', 'languages')) {
                $table->json('languages')->nullable()->change();
            }
            
            // Add indexes for better performance
            $table->index('user_id');
            $table->index('is_verified');
            $table->index('is_available');
            $table->index('rating');
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            $table->dropColumn([
                'subjects',
                'education_level',
                'bio',
                'availability',
                'latitude',
                'longitude',
                'is_verified',
                'is_available',
                'rating',
                'reviews_count',
                'total_sessions',
                'response_time',
                'teaching_style',
                'specializations'
            ]);
            
            if (Schema::hasColumn('tutors', 'user_id')) {
                $table->dropForeign(['user_id']);
                $table->dropColumn('user_id');
            }
        });
    }
};
