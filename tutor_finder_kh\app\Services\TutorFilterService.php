<?php

namespace App\Services;

use App\Models\Tutor;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;

class TutorFilterService
{
    /**
     * Apply all filters to the tutor query
     */
    public static function applyFilters(Builder $query, Request $request): Builder
    {
        // Basic filters
        self::applyBasicFilters($query, $request);
        
        // Subject and category filters
        self::applySubjectFilters($query, $request);
        
        // Location filters
        self::applyLocationFilters($query, $request);
        
        // Teaching mode filters (online/offline)
        self::applyTeachingModeFilters($query, $request);
        
        // Grade level filters
        self::applyGradeLevelFilters($query, $request);
        
        // Price and rating filters
        self::applyPriceAndRatingFilters($query, $request);
        
        // Experience and qualification filters
        self::applyExperienceFilters($query, $request);
        
        // Availability filters
        self::applyAvailabilityFilters($query, $request);
        
        // Search filters
        self::applySearchFilters($query, $request);
        
        return $query;
    }

    /**
     * Apply basic filters
     */
    private static function applyBasicFilters(Builder $query, Request $request): void
    {
        // Only active tutors
        $query->whereHas('user', function ($q) {
            $q->where('user_type', 'tutor')
              ->where('is_active', true);
        });

        // Verification status
        if ($request->has('is_verified') && $request->is_verified !== null) {
            $query->where('is_verified', $request->boolean('is_verified'));
        }

        // Availability status
        if ($request->has('is_available') && $request->is_available !== null) {
            $query->where('is_available', $request->boolean('is_available'));
        }
    }

    /**
     * Apply subject and category filters
     */
    private static function applySubjectFilters(Builder $query, Request $request): void
    {
        // Subject filters
        if ($request->has('subject') && $request->subject) {
            $subjects = is_array($request->subject) ? $request->subject : [$request->subject];
            $query->where(function ($q) use ($subjects) {
                foreach ($subjects as $subject) {
                    $q->orWhereJsonContains('subjects', $subject)
                      ->orWhere('subjects', 'LIKE', '%' . $subject . '%');
                }
            });
        }

        // Category filters
        if ($request->has('category_id') && $request->category_id) {
            $categoryIds = is_array($request->category_id) ? $request->category_id : [$request->category_id];
            $query->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }
    }

    /**
     * Apply location-based filters
     */
    private static function applyLocationFilters(Builder $query, Request $request): void
    {
        // Text-based location search
        if ($request->has('location') && $request->location) {
            $query->where('location', 'LIKE', '%' . $request->location . '%');
        }

        if ($request->has('city') && $request->city) {
            $query->where('location', 'LIKE', '%' . $request->city . '%');
        }

        // Distance-based location filter
        if ($request->has('latitude') && $request->has('longitude') && $request->has('radius')) {
            $lat = $request->latitude;
            $lng = $request->longitude;
            $radius = $request->radius; // in kilometers
            
            $query->whereRaw("
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * 
                cos(radians(longitude) - radians(?)) + sin(radians(?)) * 
                sin(radians(latitude)))) <= ?
            ", [$lat, $lng, $lat, $radius]);
        }
    }

    /**
     * Apply teaching mode filters (online/offline)
     */
    private static function applyTeachingModeFilters(Builder $query, Request $request): void
    {
        if ($request->has('teaching_mode')) {
            $mode = $request->teaching_mode;
            
            switch ($mode) {
                case 'online':
                    $query->where(function ($q) {
                        $q->whereJsonContains('availability', ['online' => true])
                          ->orWhere('teaching_style', 'LIKE', '%online%');
                    });
                    break;
                    
                case 'offline':
                case 'in_person':
                    $query->where(function ($q) {
                        $q->whereJsonContains('availability', ['in_person' => true])
                          ->orWhere('teaching_style', 'LIKE', '%in-person%')
                          ->orWhere('teaching_style', 'LIKE', '%offline%');
                    });
                    break;
                    
                case 'both':
                    $query->where(function ($q) {
                        $q->whereJsonContains('availability', ['online' => true])
                          ->whereJsonContains('availability', ['in_person' => true]);
                    });
                    break;
            }
        }
    }

    /**
     * Apply grade level filters
     */
    private static function applyGradeLevelFilters(Builder $query, Request $request): void
    {
        if ($request->has('grade_level') && $request->grade_level) {
            $grades = is_array($request->grade_level) ? $request->grade_level : [$request->grade_level];
            $query->where(function ($q) use ($grades) {
                foreach ($grades as $grade) {
                    $q->orWhereJsonContains('specializations', $grade)
                      ->orWhere('education_level', 'LIKE', '%' . $grade . '%')
                      ->orWhere('bio', 'LIKE', '%' . $grade . '%');
                }
            });
        }
    }

    /**
     * Apply price and rating filters
     */
    private static function applyPriceAndRatingFilters(Builder $query, Request $request): void
    {
        // Price range filters
        if ($request->has('min_rate') && $request->min_rate) {
            $query->where('hourly_rate', '>=', $request->min_rate);
        }

        if ($request->has('max_rate') && $request->max_rate) {
            $query->where('hourly_rate', '<=', $request->max_rate);
        }

        // Rating filters
        if ($request->has('min_rating') && $request->min_rating) {
            $query->where('rating', '>=', $request->min_rating);
        }
    }

    /**
     * Apply experience and qualification filters
     */
    private static function applyExperienceFilters(Builder $query, Request $request): void
    {
        // Experience filters
        if ($request->has('min_experience') && $request->min_experience) {
            $query->where('experience_years', '>=', $request->min_experience);
        }

        if ($request->has('max_experience') && $request->max_experience) {
            $query->where('experience_years', '<=', $request->max_experience);
        }

        // Language filters
        if ($request->has('language') && $request->language) {
            $languages = is_array($request->language) ? $request->language : [$request->language];
            $query->where(function ($q) use ($languages) {
                foreach ($languages as $language) {
                    $q->orWhereJsonContains('languages', $language);
                }
            });
        }
    }

    /**
     * Apply availability filters
     */
    private static function applyAvailabilityFilters(Builder $query, Request $request): void
    {
        // Day of week availability
        if ($request->has('available_day') && $request->available_day) {
            $day = strtolower($request->available_day);
            $query->whereJsonContains('availability', [$day => true]);
        }

        // Time slot availability
        if ($request->has('available_time') && $request->available_time) {
            $timeSlot = $request->available_time;
            $query->whereJsonContains('availability', ['time_slots' => $timeSlot]);
        }
    }

    /**
     * Apply search filters
     */
    private static function applySearchFilters(Builder $query, Request $request): void
    {
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('user', function ($userQuery) use ($searchTerm) {
                    $userQuery->where('name', 'LIKE', '%' . $searchTerm . '%');
                })
                ->orWhere('bio', 'LIKE', '%' . $searchTerm . '%')
                ->orWhereJsonContains('qualifications', $searchTerm)
                ->orWhereJsonContains('subjects', $searchTerm)
                ->orWhereJsonContains('specializations', $searchTerm);
            });
        }
    }

    /**
     * Apply sorting to the query
     */
    public static function applySorting(Builder $query, Request $request): Builder
    {
        $sortBy = $request->get('sort_by', 'rating');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'name':
                $query->join('users', 'tutors.user_id', '=', 'users.id')
                      ->orderBy('users.name', $sortOrder)
                      ->select('tutors.*');
                break;
            case 'rating':
                $query->orderBy('rating', $sortOrder);
                break;
            case 'price':
            case 'hourly_rate':
                $query->orderBy('hourly_rate', $sortOrder);
                break;
            case 'experience':
                $query->orderBy('experience_years', $sortOrder);
                break;
            case 'popularity':
                $query->orderBy('total_sessions', $sortOrder);
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'response_time':
                $query->orderBy('response_time', 'asc');
                break;
            default:
                $query->orderBy('rating', 'desc');
                break;
        }
        
        return $query;
    }

    /**
     * Get available filter options
     */
    public static function getFilterOptions(): array
    {
        return [
            'teaching_modes' => ['online', 'offline', 'both'],
            'grade_levels' => [
                'elementary', 'middle_school', 'high_school', 'college', 'university',
                'grade_1', 'grade_2', 'grade_3', 'grade_4', 'grade_5',
                'grade_6', 'grade_7', 'grade_8', 'grade_9', 'grade_10',
                'grade_11', 'grade_12'
            ],
            'sort_options' => [
                'rating', 'price', 'experience', 'popularity', 'newest', 'response_time', 'name'
            ],
            'price_ranges' => [
                ['min' => 0, 'max' => 10, 'label' => 'Under $10'],
                ['min' => 10, 'max' => 25, 'label' => '$10 - $25'],
                ['min' => 25, 'max' => 50, 'label' => '$25 - $50'],
                ['min' => 50, 'max' => 100, 'label' => '$50 - $100'],
                ['min' => 100, 'max' => null, 'label' => 'Over $100'],
            ],
            'experience_levels' => [
                ['min' => 0, 'max' => 1, 'label' => 'New (0-1 years)'],
                ['min' => 1, 'max' => 3, 'label' => 'Beginner (1-3 years)'],
                ['min' => 3, 'max' => 5, 'label' => 'Intermediate (3-5 years)'],
                ['min' => 5, 'max' => 10, 'label' => 'Experienced (5-10 years)'],
                ['min' => 10, 'max' => null, 'label' => 'Expert (10+ years)'],
            ]
        ];
    }
}
