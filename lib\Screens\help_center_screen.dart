import 'package:flutter/material.dart';
import '../config/ui_constants.dart';
import '../widgets/enhanced_components.dart';

class HelpCenterScreen extends StatefulWidget {
  const HelpCenterScreen({super.key});

  @override
  State<HelpCenterScreen> createState() => _HelpCenterScreenState();
}

class _HelpCenterScreenState extends State<HelpCenterScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final List<Map<String, dynamic>> _faqItems = [
    {
      'category': 'General',
      'question': 'How do I find a tutor?',
      'answer': 'You can find tutors by browsing the home screen, using the search function, or filtering by subject, location, and availability. Simply tap on a tutor\'s profile to view their details and book a session.',
    },
    {
      'category': 'General',
      'question': 'How do I book a tutoring session?',
      'answer': 'To book a session, select a tutor, choose your preferred time slot, and confirm your booking. You\'ll receive a confirmation email with session details.',
    },
    {
      'category': 'Payment',
      'question': 'What payment methods are accepted?',
      'answer': 'We accept major credit cards (Visa, Mastercard, American Express), PayPal, and bank transfers. You can manage your payment methods in the app settings.',
    },
    {
      'category': 'Payment',
      'question': 'How do refunds work?',
      'answer': 'Refunds are processed according to our cancellation policy. If you cancel 24 hours before the session, you\'ll receive a full refund. Cancellations within 24 hours may incur a fee.',
    },
    {
      'category': 'Sessions',
      'question': 'Can I reschedule a session?',
      'answer': 'Yes, you can reschedule sessions up to 2 hours before the scheduled time. Go to "My Sessions" and select the session you want to reschedule.',
    },
    {
      'category': 'Sessions',
      'question': 'What if my tutor doesn\'t show up?',
      'answer': 'If your tutor doesn\'t show up within 10 minutes of the scheduled time, please contact our support team. You\'ll receive a full refund and help finding an alternative tutor.',
    },
    {
      'category': 'Technical',
      'question': 'How do I join an online session?',
      'answer': 'For online sessions, you\'ll receive a link in your confirmation email. You can also access the session through the "My Sessions" section in the app.',
    },
    {
      'category': 'Technical',
      'question': 'What if I\'m having technical issues?',
      'answer': 'For technical issues, try restarting the app first. If problems persist, contact our support team through the app <NAME_EMAIL>.',
    },
  ];

  List<Map<String, dynamic>> get _filteredFAQs {
    if (_searchQuery.isEmpty) return _faqItems;
    return _faqItems.where((item) =>
      item['question'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
      item['answer'].toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UIConstants.neutralGray50,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: UIConstants.primaryBlue,
              foregroundColor: Colors.white,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Help Center',
                  style: UIConstants.headingSm.copyWith(color: Colors.white),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: UIConstants.primaryGradient,
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        right: -50,
                        top: -50,
                        child: Container(
                          width: 200,
                          height: 200,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withValues(alpha: 0.1),
                          ),
                        ),
                      ),
                      Positioned(
                        left: -30,
                        bottom: -30,
                        child: Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withValues(alpha: 0.05),
                          ),
                        ),
                      ),
                      const Positioned(
                        right: 30,
                        bottom: 30,
                        child: Icon(
                          Icons.help_outline,
                          size: 80,
                          color: Colors.white24,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(60),
                child: Container(
                  color: UIConstants.primaryBlue,
                  child: TabBar(
                    controller: _tabController,
                    indicatorColor: Colors.white,
                    indicatorWeight: 3,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.white70,
                    labelStyle: UIConstants.labelMd.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: UIConstants.labelMd,
                    tabs: const [
                      Tab(
                        icon: Icon(Icons.quiz_outlined),
                        text: 'FAQ',
                      ),
                      Tab(
                        icon: Icon(Icons.contact_support_outlined),
                        text: 'Contact',
                      ),
                      Tab(
                        icon: Icon(Icons.menu_book_outlined),
                        text: 'Guides',
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildEnhancedFAQTab(),
            _buildEnhancedContactTab(),
            _buildEnhancedGuidesTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedFAQTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(UIConstants.spacingLg),
          child: EnhancedSearchBar(
            hintText: 'Search frequently asked questions...',
            controller: _searchController,
            onChanged: (value) => setState(() => _searchQuery = value),
            onClear: () => setState(() => _searchQuery = ''),
            showFilter: true,
            onFilterTap: () => _showFilterDialog(),
          ),
        ),
        Expanded(
          child: _filteredFAQs.isEmpty
              ? const EnhancedEmptyState(
                  icon: Icons.search_off,
                  title: 'No results found',
                  subtitle: 'Try searching with different keywords or browse all FAQs',
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(
                    horizontal: UIConstants.spacingLg,
                    vertical: UIConstants.spacingSm,
                  ),
                  itemCount: _filteredFAQs.length,
                  itemBuilder: (context, index) {
                    final faq = _filteredFAQs[index];
                    return _buildEnhancedFAQItem(faq, index);
                  },
                ),
        ),
      ],
    );
  }





  Widget _buildEnhancedFAQItem(Map<String, dynamic> faq, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: EnhancedCard(
              margin: const EdgeInsets.only(bottom: UIConstants.spacingMd),
              child: Theme(
                data: Theme.of(context).copyWith(
                  dividerColor: Colors.transparent,
                ),
                child: ExpansionTile(
                  tilePadding: EdgeInsets.zero,
                  childrenPadding: const EdgeInsets.only(
                    bottom: UIConstants.spacingLg,
                  ),
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: _getCategoryGradient(faq['category']),
                      borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                    ),
                    child: Icon(
                      _getCategoryIcon(faq['category']),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    faq['question'],
                    style: UIConstants.bodyMd.copyWith(
                      fontWeight: FontWeight.w600,
                      color: UIConstants.neutralGray800,
                    ),
                  ),
                  subtitle: Container(
                    margin: const EdgeInsets.only(top: UIConstants.spacingSm),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: UIConstants.spacingSm,
                            vertical: UIConstants.spacing2xs,
                          ),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(faq['category']),
                            borderRadius: BorderRadius.circular(UIConstants.radiusFull),
                          ),
                          child: Text(
                            faq['category'],
                            style: UIConstants.labelSm.copyWith(
                              color: _getCategoryTextColor(faq['category']),
                            ),
                          ),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.help_outline,
                          size: 16,
                          color: UIConstants.neutralGray400,
                        ),
                      ],
                    ),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: UIConstants.spacingLg,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            height: 1,
                            color: UIConstants.neutralGray200,
                            margin: const EdgeInsets.only(
                              bottom: UIConstants.spacingLg,
                            ),
                          ),
                          Text(
                            faq['answer'],
                            style: UIConstants.bodyMd.copyWith(
                              color: UIConstants.neutralGray600,
                              height: 1.6,
                            ),
                          ),
                          const SizedBox(height: UIConstants.spacingLg),
                          Row(
                            children: [
                              Text(
                                'Was this helpful?',
                                style: UIConstants.bodySm.copyWith(
                                  color: UIConstants.neutralGray500,
                                ),
                              ),
                              const SizedBox(width: UIConstants.spacingMd),
                              IconButton(
                                onPressed: () => _markHelpful(index, true),
                                icon: const Icon(Icons.thumb_up_outlined),
                                iconSize: 18,
                                style: IconButton.styleFrom(
                                  foregroundColor: UIConstants.successGreen,
                                ),
                              ),
                              IconButton(
                                onPressed: () => _markHelpful(index, false),
                                icon: const Icon(Icons.thumb_down_outlined),
                                iconSize: 18,
                                style: IconButton.styleFrom(
                                  foregroundColor: UIConstants.errorRed,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  Widget _buildEnhancedContactTab() {
    return ListView(
      padding: const EdgeInsets.all(UIConstants.spacingLg),
      children: [
        // Quick stats section
        EnhancedCard(
          child: Column(
            children: [
              Text(
                'We\'re here to help!',
                style: UIConstants.headingSm.copyWith(
                  color: UIConstants.neutralGray800,
                ),
              ),
              const SizedBox(height: UIConstants.spacingSm),
              Text(
                'Our support team typically responds within 2 hours',
                style: UIConstants.bodyMd.copyWith(
                  color: UIConstants.neutralGray600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: UIConstants.spacingLg),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStatItem('2hrs', 'Avg Response'),
                  _buildStatItem('24/7', 'Availability'),
                  _buildStatItem('98%', 'Satisfaction'),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: UIConstants.spacing2xl),
        _buildEnhancedContactOption(
          icon: Icons.email_outlined,
          title: 'Email Support',
          subtitle: 'Get help via email',
          description: '<EMAIL>',
          gradient: UIConstants.primaryGradient,
          onTap: () => _showComingSoon('Email support'),
        ),
        const SizedBox(height: UIConstants.spacingLg),
        _buildEnhancedContactOption(
          icon: Icons.chat_outlined,
          title: 'Live Chat',
          subtitle: 'Chat with our support team',
          description: 'Available 24/7',
          gradient: UIConstants.secondaryGradient,
          onTap: () => _showComingSoon('Live chat'),
        ),
        const SizedBox(height: UIConstants.spacingLg),
        _buildEnhancedContactOption(
          icon: Icons.phone_outlined,
          title: 'Phone Support',
          subtitle: 'Call our support hotline',
          description: '+****************',
          gradient: UIConstants.accentGradient,
          onTap: () => _showComingSoon('Phone support'),
        ),
        const SizedBox(height: UIConstants.spacingLg),
        _buildEnhancedContactOption(
          icon: Icons.bug_report_outlined,
          title: 'Report a Bug',
          subtitle: 'Help us improve the app',
          description: 'Report technical issues',
          gradient: const LinearGradient(
            colors: [UIConstants.errorRed, Color(0xFFDC2626)],
          ),
          onTap: () => _showBugReportDialog(),
        ),
      ],
    );
  }





  Widget _buildEnhancedGuidesTab() {
    final guides = [
      {
        'title': 'Getting Started',
        'description': 'Learn the basics of using TutorFinder and set up your profile',
        'icon': Icons.play_circle_outline,
        'duration': '5 min read',
        'category': 'Beginner',
        'color': UIConstants.primaryBlue,
        'progress': 0.0,
      },
      {
        'title': 'Finding the Right Tutor',
        'description': 'Tips and strategies for choosing the perfect tutor for your learning goals',
        'icon': Icons.search,
        'duration': '8 min read',
        'category': 'Student',
        'color': UIConstants.secondaryPurple,
        'progress': 0.0,
      },
      {
        'title': 'Becoming a Tutor',
        'description': 'Complete step-by-step guide to start your tutoring journey on our platform',
        'icon': Icons.school,
        'duration': '12 min read',
        'category': 'Tutor',
        'color': UIConstants.accentOrange,
        'progress': 0.0,
      },
      {
        'title': 'Payment & Billing',
        'description': 'Everything you need to know about payments, refunds, and billing cycles',
        'icon': Icons.payment,
        'duration': '6 min read',
        'category': 'General',
        'color': UIConstants.successGreen,
        'progress': 0.0,
      },
      {
        'title': 'Safety Guidelines',
        'description': 'Essential safety tips and best practices for secure online learning',
        'icon': Icons.security,
        'duration': '10 min read',
        'category': 'Important',
        'color': UIConstants.errorRed,
        'progress': 0.0,
      },
    ];

    return Column(
      children: [
        // Progress overview
        Padding(
          padding: const EdgeInsets.all(UIConstants.spacingLg),
          child: EnhancedCard(
            backgroundColor: UIConstants.primaryBlue,
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                      ),
                      child: const Icon(
                        Icons.menu_book,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: UIConstants.spacingLg),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Learning Progress',
                            style: UIConstants.bodyLg.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: UIConstants.spacing2xs),
                          Text(
                            '0 of ${guides.length} guides completed',
                            style: UIConstants.bodyMd.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: UIConstants.spacingLg),
                LinearProgressIndicator(
                  value: 0.0,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  borderRadius: BorderRadius.circular(UIConstants.radiusFull),
                ),
              ],
            ),
          ),
        ),
        // Guides list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(
              horizontal: UIConstants.spacingLg,
              vertical: UIConstants.spacingSm,
            ),
            itemCount: guides.length,
            itemBuilder: (context, index) {
              final guide = guides[index];
              return _buildEnhancedGuideItem(guide, index);
            },
          ),
        ),
      ],
    );
  }





  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$feature coming soon!')),
    );
  }

  void _showBugReportDialog() {
    final TextEditingController bugController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report a Bug'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please describe the issue you encountered:'),
            const SizedBox(height: 16),
            TextField(
              controller: bugController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'Describe the bug...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Bug report submitted. Thank you!')),
              );
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  // Helper methods for enhanced UI
  LinearGradient _getCategoryGradient(String category) {
    switch (category.toLowerCase()) {
      case 'general':
        return UIConstants.primaryGradient;
      case 'payment':
        return UIConstants.accentGradient;
      case 'sessions':
        return UIConstants.secondaryGradient;
      case 'technical':
        return const LinearGradient(
          colors: [UIConstants.successGreen, Color(0xFF059669)],
        );
      default:
        return UIConstants.primaryGradient;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'general':
        return Icons.info_outline;
      case 'payment':
        return Icons.payment;
      case 'sessions':
        return Icons.schedule;
      case 'technical':
        return Icons.settings;
      default:
        return Icons.help_outline;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'general':
        return UIConstants.primaryBlue.withValues(alpha: 0.1);
      case 'payment':
        return UIConstants.accentOrange.withValues(alpha: 0.1);
      case 'sessions':
        return UIConstants.secondaryPurple.withValues(alpha: 0.1);
      case 'technical':
        return UIConstants.successGreen.withValues(alpha: 0.1);
      default:
        return UIConstants.primaryBlue.withValues(alpha: 0.1);
    }
  }

  Color _getCategoryTextColor(String category) {
    switch (category.toLowerCase()) {
      case 'general':
        return UIConstants.primaryBlue;
      case 'payment':
        return UIConstants.accentOrange;
      case 'sessions':
        return UIConstants.secondaryPurple;
      case 'technical':
        return UIConstants.successGreen;
      default:
        return UIConstants.primaryBlue;
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(UIConstants.radius2xl),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: UIConstants.spacingMd),
              decoration: BoxDecoration(
                color: UIConstants.neutralGray300,
                borderRadius: BorderRadius.circular(UIConstants.radiusFull),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(UIConstants.spacingLg),
              child: Text(
                'Filter FAQs',
                style: UIConstants.headingSm,
              ),
            ),
            // Add filter options here
            Expanded(
              child: Center(
                child: Text(
                  'Filter options coming soon!',
                  style: UIConstants.bodyMd.copyWith(
                    color: UIConstants.neutralGray500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _markHelpful(int index, bool isHelpful) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isHelpful ? 'Thanks for your feedback!' : 'We\'ll improve this answer',
        ),
        backgroundColor: isHelpful ? UIConstants.successGreen : UIConstants.warningYellow,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusLg),
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: UIConstants.headingSm.copyWith(
            color: UIConstants.primaryBlue,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: UIConstants.spacing2xs),
        Text(
          label,
          style: UIConstants.bodySm.copyWith(
            color: UIConstants.neutralGray500,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required String description,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return EnhancedCard(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(UIConstants.radiusLg),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: UIConstants.spacingLg),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: UIConstants.bodyLg.copyWith(
                    fontWeight: FontWeight.w600,
                    color: UIConstants.neutralGray800,
                  ),
                ),
                const SizedBox(height: UIConstants.spacing2xs),
                Text(
                  subtitle,
                  style: UIConstants.bodyMd.copyWith(
                    color: UIConstants.neutralGray600,
                  ),
                ),
                const SizedBox(height: UIConstants.spacing2xs),
                Text(
                  description,
                  style: UIConstants.bodySm.copyWith(
                    color: UIConstants.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: UIConstants.neutralGray400,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedGuideItem(Map<String, dynamic> guide, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: EnhancedCard(
              margin: const EdgeInsets.only(bottom: UIConstants.spacingMd),
              onTap: () => _showGuideDetail(guide),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: (guide['color'] as Color).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                        ),
                        child: Icon(
                          guide['icon'] as IconData,
                          color: guide['color'] as Color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: UIConstants.spacingLg),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guide['title'],
                              style: UIConstants.bodyLg.copyWith(
                                fontWeight: FontWeight.w600,
                                color: UIConstants.neutralGray800,
                              ),
                            ),
                            const SizedBox(height: UIConstants.spacing2xs),
                            Text(
                              guide['description'],
                              style: UIConstants.bodyMd.copyWith(
                                color: UIConstants.neutralGray600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: UIConstants.spacingMd),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: UIConstants.spacingSm,
                          vertical: UIConstants.spacing2xs,
                        ),
                        decoration: BoxDecoration(
                          color: (guide['color'] as Color).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(UIConstants.radiusFull),
                        ),
                        child: Text(
                          guide['category'],
                          style: UIConstants.labelSm.copyWith(
                            color: guide['color'] as Color,
                          ),
                        ),
                      ),
                      const SizedBox(width: UIConstants.spacingMd),
                      const Icon(
                        Icons.schedule,
                        size: 16,
                        color: UIConstants.neutralGray400,
                      ),
                      const SizedBox(width: UIConstants.spacing2xs),
                      Text(
                        guide['duration'],
                        style: UIConstants.bodySm.copyWith(
                          color: UIConstants.neutralGray500,
                        ),
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: UIConstants.neutralGray400,
                      ),
                    ],
                  ),
                  if (guide['progress'] > 0) ...[
                    const SizedBox(height: UIConstants.spacingMd),
                    LinearProgressIndicator(
                      value: guide['progress'],
                      backgroundColor: UIConstants.neutralGray200,
                      valueColor: AlwaysStoppedAnimation<Color>(guide['color'] as Color),
                      borderRadius: BorderRadius.circular(UIConstants.radiusFull),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showGuideDetail(Map<String, dynamic> guide) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(UIConstants.radius2xl),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: UIConstants.spacingMd),
              decoration: BoxDecoration(
                color: UIConstants.neutralGray300,
                borderRadius: BorderRadius.circular(UIConstants.radiusFull),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(UIConstants.spacingLg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: (guide['color'] as Color).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                          ),
                          child: Icon(
                            guide['icon'] as IconData,
                            color: guide['color'] as Color,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: UIConstants.spacingLg),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                guide['title'],
                                style: UIConstants.headingSm,
                              ),
                              const SizedBox(height: UIConstants.spacing2xs),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: UIConstants.spacingSm,
                                      vertical: UIConstants.spacing2xs,
                                    ),
                                    decoration: BoxDecoration(
                                      color: (guide['color'] as Color).withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(UIConstants.radiusFull),
                                    ),
                                    child: Text(
                                      guide['category'],
                                      style: UIConstants.labelSm.copyWith(
                                        color: guide['color'] as Color,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: UIConstants.spacingMd),
                                  Text(
                                    guide['duration'],
                                    style: UIConstants.bodySm.copyWith(
                                      color: UIConstants.neutralGray500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: UIConstants.spacing2xl),
                    Text(
                      guide['description'],
                      style: UIConstants.bodyLg.copyWith(
                        color: UIConstants.neutralGray700,
                        height: 1.6,
                      ),
                    ),
                    const SizedBox(height: UIConstants.spacing2xl),
                    EnhancedButton(
                      text: 'Start Reading',
                      onPressed: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Guide content coming soon!'),
                          ),
                        );
                      },
                      icon: const Icon(Icons.play_arrow),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}
