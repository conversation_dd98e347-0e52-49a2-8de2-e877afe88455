import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/category_service.dart';

enum CategoryState {
  initial,
  loading,
  loaded,
  error,
}

class CategoryProvider extends ChangeNotifier {
  final CategoryService _categoryService = CategoryService();
  
  CategoryState _state = CategoryState.initial;
  String? _errorMessage;
  List<TutorCategory> _categories = [];
  List<TutorCategory> _popularCategories = [];
  bool _isLoading = false;

  // Getters
  CategoryState get state => _state;
  String? get errorMessage => _errorMessage;
  List<TutorCategory> get categories => _categories;
  List<TutorCategory> get popularCategories => _popularCategories;
  bool get isLoading => _isLoading;

  // Load all categories
  Future<void> loadCategories() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _categoryService.getCategories();

      if (response.isSuccess && response.data != null) {
        _categories = response.data!;
        _setState(CategoryState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load categories');
      }
    } catch (e) {
      _setError('Failed to load categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load popular categories
  Future<void> loadPopularCategories() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _categoryService.getPopularCategories();

      if (response.isSuccess && response.data != null) {
        _popularCategories = response.data!;
        _setState(CategoryState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load popular categories');
      }
    } catch (e) {
      _setError('Failed to load popular categories: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get category by ID
  Future<Map<String, dynamic>?> getCategoryById(int id) async {
    try {
      final response = await _categoryService.getCategoryById(id);
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
    } catch (e) {
      print('Error getting category by ID: $e');
    }
    return null;
  }

  // Search categories
  List<TutorCategory> searchCategories(String query) {
    return _categoryService.searchCategories(_categories, query);
  }

  // Get categories with most tutors
  List<TutorCategory> getCategoriesWithMostTutors() {
    return _categoryService.getCategoriesWithMostTutors(_categories);
  }

  // Get recommended categories
  List<TutorCategory> getRecommendedCategories() {
    return _categoryService.getRecommendedCategories(_categories);
  }

  // Filter categories by minimum tutor count
  List<TutorCategory> filterCategoriesByTutorCount(int minCount) {
    return _categoryService.filterCategoriesByTutorCount(_categories, minCount);
  }

  // Group categories alphabetically
  Map<String, List<TutorCategory>> groupCategoriesAlphabetically() {
    return _categoryService.groupCategoriesAlphabetically(_categories);
  }

  // Get category by name
  TutorCategory? getCategoryByName(String name) {
    try {
      return _categories.firstWhere(
        (category) => category.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  // Get categories by names
  List<TutorCategory> getCategoriesByNames(List<String> names) {
    return _categories.where((category) {
      return names.any((name) =>
        category.name.toLowerCase().contains(name.toLowerCase()));
    }).toList();
  }

  // Clear error
  void clearError() {
    _clearError();
  }

  // Refresh all data
  Future<void> refresh() async {
    await Future.wait([
      loadCategories(),
      loadPopularCategories(),
    ]);
  }

  // Initialize - load both categories and popular categories
  Future<void> initialize() async {
    await refresh();
  }

  // Private methods
  void _setState(CategoryState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = CategoryState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == CategoryState.error) {
      _state = _categories.isNotEmpty ? CategoryState.loaded : CategoryState.initial;
    }
    notifyListeners();
  }

  // Get statistics
  Map<String, dynamic> getCategoryStats() {
    return _categoryService.getCategoryStats(_categories);
  }

  // Helper methods for UI
  List<TutorCategory> getTopCategories({int limit = 8}) {
    final categoriesWithTutors = _categories
        .where((cat) => (cat.tutorCount ?? 0) > 0)
        .toList();

    categoriesWithTutors.sort((a, b) =>
        (b.tutorCount ?? 0).compareTo(a.tutorCount ?? 0));

    return categoriesWithTutors.take(limit).toList();
  }

  List<TutorCategory> getRandomCategories({int limit = 6}) {
    final shuffled = List<TutorCategory>.from(_categories);
    shuffled.shuffle();
    return shuffled.take(limit).toList();
  }

  bool hasCategories() {
    return _categories.isNotEmpty;
  }

  bool hasPopularCategories() {
    return _popularCategories.isNotEmpty;
  }
}
