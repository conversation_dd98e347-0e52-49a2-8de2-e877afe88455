<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'grade_level',
        'school',
        'learning_preferences',
        'subjects_of_interest',
        'preferred_learning_style',
        'goals',
        'availability',
        'budget_range',
    ];

    protected $casts = [
        'subjects_of_interest' => 'array',
        'learning_preferences' => 'array',
        'availability' => 'array',
    ];

    /**
     * Get the user that owns the student profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the favorites for this student through the user.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(Favorite::class, 'user_id', 'user_id');
    }

    /**
     * Get the favorite tutors for this student.
     */
    public function favoriteTutors(): BelongsToMany
    {
        return $this->belongsToMany(Tutor::class, 'favorites', 'user_id', 'tutor_id', 'user_id');
    }

    /**
     * Get the reviews written by this student.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(TutorReview::class, 'student_id');
    }

    /**
     * Get the sessions for this student.
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(Session::class, 'student_id');
    }

    /**
     * Get the sent messages by this student through user.
     */
    public function sentMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'sender_id', 'user_id');
    }

    /**
     * Get the received messages by this student through user.
     */
    public function receivedMessages(): HasMany
    {
        return $this->hasMany(Message::class, 'receiver_id', 'user_id');
    }

    /**
     * Get the notifications for this student through user.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'user_id', 'user_id');
    }

    /**
     * Get the total number of completed sessions for this student.
     */
    public function getCompletedSessionsCountAttribute(): int
    {
        return $this->sessions()->where('status', 'completed')->count();
    }

    /**
     * Get the total hours of tutoring received by this student.
     */
    public function getTotalHoursAttribute(): int
    {
        return $this->sessions()
                    ->where('status', 'completed')
                    ->sum('duration_minutes') / 60;
    }

    /**
     * Check if student has favorited a specific tutor.
     */
    public function hasFavorited(int $tutorId): bool
    {
        return $this->favorites()->where('tutor_id', $tutorId)->exists();
    }

    /**
     * Add a tutor to favorites.
     */
    public function addToFavorites(int $tutorId): bool
    {
        if (!$this->hasFavorited($tutorId)) {
            $this->favorites()->create(['tutor_id' => $tutorId]);
            return true;
        }
        return false;
    }

    /**
     * Remove a tutor from favorites.
     */
    public function removeFromFavorites(int $tutorId): bool
    {
        return $this->favorites()->where('tutor_id', $tutorId)->delete() > 0;
    }
}
