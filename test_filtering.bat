@echo off
echo ========================================
echo    Testing Student Filtering System
echo ========================================
echo.

echo [1/6] Testing basic tutor listing...
curl -s "http://127.0.0.1:8000/api/v1/tutors" | head -20
echo.
echo.

echo [2/6] Testing filter options...
curl -s "http://127.0.0.1:8000/api/v1/tutors/filter-options"
echo.
echo.

echo [3/6] Testing subject filtering...
curl -s "http://127.0.0.1:8000/api/v1/tutors?subject=math"
echo.
echo.

echo [4/6] Testing online tutors...
curl -s "http://127.0.0.1:8000/api/v1/tutors/online"
echo.
echo.

echo [5/6] Testing price range filtering...
curl -s "http://127.0.0.1:8000/api/v1/tutors?min_rate=10&max_rate=50"
echo.
echo.

echo [6/6] Testing combined filters...
curl -s "http://127.0.0.1:8000/api/v1/tutors?subject=math&teaching_mode=online&min_rating=4.0&sort_by=rating"
echo.
echo.

echo ========================================
echo    Filtering Test Complete
echo ========================================
