<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tutor_posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tutor_id')->constrained('tutors')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('set null');
            $table->string('title');
            $table->longText('content');
            $table->text('excerpt')->nullable();
            $table->enum('post_type', ['article', 'tutorial', 'announcement', 'resource', 'tip'])->default('article');
            $table->json('tags')->nullable();
            $table->string('featured_image')->nullable();
            $table->json('attachments')->nullable();
            $table->boolean('is_published')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->integer('views_count')->default(0);
            $table->integer('likes_count')->default(0);
            $table->integer('comments_count')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('slug')->unique();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['tutor_id', 'is_published']);
            $table->index(['category_id', 'is_published']);
            $table->index(['post_type', 'is_published']);
            $table->index(['is_featured', 'is_published']);
            $table->index('published_at');
            $table->index('views_count');
            $table->index('likes_count');
            // Note: SQLite doesn't support fulltext indexes
            // $table->fullText(['title', 'content', 'excerpt']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutor_posts');
    }
};
