import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/tutor_provider.dart';
import '../providers/categories.dart';
import '../providers/favorite_provider.dart';
import '../providers/post_provider.dart';
import '../models/user.dart';
import '../models/tutor_post.dart';
import '../routes/routes_screen.dart';
import '../config/ui_constants.dart';
import '../widgets/enhanced_components.dart';
import 'notifications_screen.dart';
import 'chat_screen.dart';

import 'create_post_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});


  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String userName = 'Guest!';
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    // Use post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUser();
      _loadData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }



  Future<void> _loadUser() async {
    if (!mounted) return;

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Wait a bit if the provider is still loading
      if (authProvider.isLoading) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (mounted) {
        setState(() {
          userName = authProvider.userName ?? 'Guest';
        });
      }
    } catch (e) {
      // Handle error gracefully
      if (mounted) {
        setState(() {
          userName = 'Guest';
        });
      }
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    try {
      final tutorProvider = Provider.of<TutorProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      final postProvider = Provider.of<PostProvider>(context, listen: false);

      await Future.wait([
        tutorProvider.loadFeaturedTutors(),
        categoryProvider.loadPopularCategories(),
        postProvider.loadFeaturedPosts(),
      ]);
    } catch (e) {
      // Handle error gracefully - providers will handle their own error states
      debugPrint('Error loading data: $e');
    }
  }
  

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isAdmin) {
          return _buildAdminHomeScreen();
        } else if (authProvider.isTutor) {
          return _buildTutorHomeScreen();
        } else {
          return _buildStudentHomeScreen();
        }
      },
    );
  }

  Widget _buildStudentHomeScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildStudentAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: ListView(
          padding: const EdgeInsets.all(UIConstants.spacingLg),
          children: [
            // Enhanced search bar
            EnhancedSearchBar(
              hintText: 'Search tutors, subjects, or posts...',
              showFilter: true,
              onFilterTap: () => _showFilterDialog(),
              onChanged: (value) {
                // TODO: Implement search functionality
              },
            ),
            const SizedBox(height: UIConstants.spacingLg),
            _categoryFilter,
            const SizedBox(height: UIConstants.spacingLg),
            _banner,
            const SizedBox(height: UIConstants.spacing2xl),
            _sectionHeader('Featured Tutors', onTap: () => _showAllTutors()),
            const SizedBox(height: UIConstants.spacingMd),
            _tutorHorizontalList(),
            const SizedBox(height: UIConstants.spacing3xl),
            _sectionHeader('Featured Posts', onTap: () => _showAllPosts()),
            const SizedBox(height: UIConstants.spacingMd),
            _featuredPostsList(),
            const SizedBox(height: UIConstants.spacing3xl),
            _sectionHeader('Popular Subjects', onTap: () => _showAllSubjects()),
            const SizedBox(height: 12),
            _subjectGrid(),
            const SizedBox(height: 32),
            _sectionHeader('Recent Reviews', onTap: () {}),
            const SizedBox(height: 12),
            _reviewsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildTutorHomeScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildTutorAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildTutorDashboard(),
            const SizedBox(height: 20),
            _buildQuickActions(),
            const SizedBox(height: 24),
            _sectionHeader('My Students', onTap: () {}),
            const SizedBox(height: 12),
            _buildStudentsList(),
            const SizedBox(height: 32),
            _sectionHeader('Recent Sessions', onTap: () {}),
            const SizedBox(height: 12),
            _buildRecentSessions(),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminHomeScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAdminAppBar(),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildAdminDashboard(),
            const SizedBox(height: 20),
            _buildAdminQuickActions(),
            const SizedBox(height: 24),
            _sectionHeader('Pending Approvals', onTap: () {}),
            const SizedBox(height: 12),
            _buildPendingApprovals(),
            const SizedBox(height: 32),
            _sectionHeader('System Stats', onTap: () {}),
            const SizedBox(height: 12),
            _buildSystemStats(),
          ],
        ),
      ),
    );
  }

  // App Bars for different user types
  PreferredSizeWidget _buildStudentAppBar() {
    return AppBar(
      backgroundColor: UIConstants.primaryBlue,
      elevation: 0,
      toolbarHeight: 80,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: UIConstants.primaryGradient,
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: UIConstants.spacingLg,
              vertical: UIConstants.spacingSm,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Hello, $userName! 👋',
                        style: UIConstants.bodyLg.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: UIConstants.spacing2xs),
                      Text(
                        'Ready to learn something new?',
                        style: UIConstants.bodyMd.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.notifications_outlined, color: Colors.white),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const NotiScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: UIConstants.spacingSm),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(UIConstants.radiusLg),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.person_outline, color: Colors.white),
                        onPressed: () {
                          // TODO: Navigate to profile
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildTutorAppBar() {
    return AppBar(
      backgroundColor: Colors.green.shade700,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome, $userName! 🎓',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const Text(
            'Manage your tutoring',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add_circle_outline, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const CreatePostScreen()),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NotiScreen(),
              ),
            );
          },
        ),
      ],
    );
  }

  PreferredSizeWidget _buildAdminAppBar() {
    return AppBar(
      backgroundColor: Colors.purple.shade700,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Admin Panel - $userName 👑',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const Text(
            'System Management',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add_box_outlined, color: Colors.white),
          onPressed: () {
            // TODO: Add new category
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Add category feature coming soon!')),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () {
            // TODO: Admin settings
          },
        ),
      ],
    );
  }

  Future<void> _refreshData() async {
    final tutorProvider = Provider.of<TutorProvider>(context, listen: false);
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

    await Future.wait([
      tutorProvider.refresh(),
      categoryProvider.refresh(),
    ]);

    _loadUser();
  }

  // Tutor-specific widgets
  Widget _buildTutorDashboard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade600, Colors.green.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Your Dashboard',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard('Students', '12', Icons.people),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Sessions', '45', Icons.schedule),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Rating', '4.8', Icons.star),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Create Post',
                Icons.add_circle,
                Colors.blue,
                () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Create post feature coming soon!')),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'My Schedule',
                Icons.calendar_today,
                Colors.orange,
                () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Schedule feature coming soon!')),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStudentsList() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 5,
        itemBuilder: (context, index) {
          return Container(
            width: 100,
            margin: const EdgeInsets.only(right: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: Colors.blue.shade100,
                  child: Text(
                    'S${index + 1}',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Student ${index + 1}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecentSessions() {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.green.shade100,
                child: Icon(Icons.schedule, color: Colors.green.shade700),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mathematics Session ${index + 1}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Student ${index + 1} • 1 hour ago',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Completed',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  // Admin-specific widgets
  Widget _buildAdminDashboard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade600, Colors.purple.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'System Overview',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard('Tutors', '156', Icons.school),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Students', '1.2K', Icons.people),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard('Pending', '8', Icons.pending),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdminQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Admin Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Add Category',
                Icons.add_box,
                Colors.purple,
                () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Add category feature coming soon!')),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'Approve Posts',
                Icons.check_circle,
                Colors.green,
                () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Approve posts feature coming soon!')),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPendingApprovals() {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.orange.shade100,
                child: Icon(Icons.pending, color: Colors.orange.shade700),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tutor Application ${index + 1}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'John Doe • Mathematics',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: Icon(Icons.check, color: Colors.green.shade600),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Approved!')),
                      );
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: Colors.red.shade600),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Rejected!')),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildSystemStats() {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(Icons.trending_up, color: Colors.green.shade600, size: 32),
                const SizedBox(height: 8),
                const Text(
                  'Active Sessions',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text('24', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(Icons.attach_money, color: Colors.blue.shade600, size: 32),
                const SizedBox(height: 8),
                const Text(
                  'Revenue',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Text('\$2.4K', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Helper widgets
  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showAllTutors() {
    // TODO: Navigate to all tutors screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All tutors screen coming soon!')),
    );
  }

  void _showAllSubjects() {
    // TODO: Navigate to all subjects screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All subjects screen coming soon!')),
    );
  }

  void _showAllPosts() {
    // TODO: Navigate to all posts screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All posts screen coming soon!')),
    );
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(UIConstants.radius2xl),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: UIConstants.spacingMd),
              decoration: BoxDecoration(
                color: UIConstants.neutralGray300,
                borderRadius: BorderRadius.circular(UIConstants.radiusFull),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(UIConstants.spacingLg),
              child: Row(
                children: [
                  const Text(
                    'Filter & Sort',
                    style: UIConstants.headingSm,
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Clear All',
                      style: UIConstants.labelMd.copyWith(
                        color: UIConstants.errorRed,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: UIConstants.spacingLg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Subject Categories',
                      style: UIConstants.bodyLg.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: UIConstants.spacingMd),
                    // Add filter chips here
                    Wrap(
                      spacing: UIConstants.spacingSm,
                      runSpacing: UIConstants.spacingSm,
                      children: [
                        'Mathematics',
                        'Science',
                        'Languages',
                        'Arts',
                        'Technology',
                      ].map((category) => FilterChip(
                        label: Text(category),
                        selected: false,
                        onSelected: (selected) {
                          // TODO: Implement filter logic
                        },
                      )).toList(),
                    ),
                    const SizedBox(height: UIConstants.spacing2xl),
                    Text(
                      'Price Range',
                      style: UIConstants.bodyLg.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: UIConstants.spacingMd),
                    // Add price range slider here
                    Text(
                      'Price range filter coming soon!',
                      style: UIConstants.bodyMd.copyWith(
                        color: UIConstants.neutralGray500,
                      ),
                    ),
                    const SizedBox(height: UIConstants.spacing2xl),
                    EnhancedButton(
                      text: 'Apply Filters',
                      onPressed: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Filters applied successfully!'),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPostDetail(TutorPost post) {
    // Show post detail in a modal bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Post type badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        post.postType.toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Title
                    Text(
                      post.title,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Meta info
                    Row(
                      children: [
                        Icon(Icons.visibility, size: 16, color: Colors.grey.shade500),
                        const SizedBox(width: 4),
                        Text(
                          '${post.viewsCount} views',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.favorite, size: 16, color: Colors.grey.shade500),
                        const SizedBox(width: 4),
                        Text(
                          '${post.likesCount} likes',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.schedule, size: 16, color: Colors.grey.shade500),
                        const SizedBox(width: 4),
                        Text(
                          '${post.readingTime} min read',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Content
                    Text(
                      post.displayExcerpt,
                      style: const TextStyle(
                        fontSize: 16,
                        height: 1.6,
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Toggle like
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Like feature coming soon!')),
                              );
                            },
                            icon: const Icon(Icons.favorite_border),
                            label: const Text('Like'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade50,
                              foregroundColor: Colors.blue.shade700,
                              elevation: 0,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // TODO: Toggle bookmark
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Bookmark feature coming soon!')),
                              );
                            },
                            icon: const Icon(Icons.bookmark_border),
                            label: const Text('Save'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey.shade50,
                              foregroundColor: Colors.grey.shade700,
                              elevation: 0,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  void _showTutorProfile(Tutor tutor) {
    // Navigate to tutor profile or show tutor details
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tutor Header
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundImage: tutor.image != null
                            ? NetworkImage(tutor.image!)
                            : null,
                          child: tutor.image == null
                            ? Text((tutor.name?.isNotEmpty ?? false) ? tutor.name![0].toUpperCase() : 'T')
                            : null,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                tutor.name ?? 'Tutor',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(Icons.star, color: Colors.amber, size: 16),
                                  const SizedBox(width: 4),
                                  Text('${tutor.rating}'),
                                  const SizedBox(width: 8),
                                  Text('(${tutor.totalReviews} reviews)'),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '\$${tutor.hourlyRateValue}/hour',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Bio
                    if (tutor.description != null) ...[
                      const Text(
                        'About',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(tutor.description!),
                      const SizedBox(height: 20),
                    ],

                    // Subjects
                    if (tutor.categories != null && tutor.categories!.isNotEmpty) ...[
                      const Text(
                        'Subjects',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: tutor.categories!.map((category) => Chip(
                          label: Text(category.name),
                          backgroundColor: Colors.blue.shade50,
                        )).toList(),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(
                                context,
                                '/chat',
                                arguments: {
                                  'otherUserId': tutor.id,
                                  'otherUserType': 'tutor',
                                  'otherUserName': tutor.name ?? 'Tutor',
                                  'otherUserAvatar': tutor.image,
                                },
                              );
                            },
                            icon: const Icon(Icons.chat),
                            label: const Text('Chat'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              // Add to favorites
                              final favoriteProvider = Provider.of<FavoriteProvider>(context, listen: false);
                              favoriteProvider.toggleFavorite(tutor.id);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('${tutor.name ?? 'Tutor'} added to favorites!')),
                              );
                            },
                            icon: const Icon(Icons.favorite_border),
                            label: const Text('Favorite'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  void _navigateToTutorSearchWithSubject(String subjectName) {
    // Navigate to tutor search with subject pre-selected
    Navigator.pushNamed(
      context,
      AppRoute.tutorSearchScreen,
      arguments: {
        'selectedSubject': _mapSubjectName(subjectName),
        'autoApplyFilter': true,
      },
    );
  }

  // Map display names to filter names
  String _mapSubjectName(String displayName) {
    switch (displayName) {
      case 'Math':
        return 'Mathematics';
      case 'IT':
        return 'IT/Computer Science';
      case 'Khmer':
        return 'Khmer Literature';
      default:
        return displayName;
    }
  }

  void _openChatWithTutor(Tutor tutor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          otherUserId: tutor.id,
          otherUserType: 'tutor',
          otherUserName: tutor.name ?? 'Tutor',
          otherUserAvatar: tutor.image,
        ),
      ),
    );
  }

  Future<void> _filterTutorsByCategory(String category) async {
    if (!mounted) return;

    try {
      final tutorProvider = Provider.of<TutorProvider>(context, listen: false);

      if (category == 'All') {
        await tutorProvider.loadTutors(refresh: true);
      } else {
        final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
        final categoryObj = categoryProvider.getCategoryByName(category);
        if (categoryObj != null) {
          await tutorProvider.loadTutorsByCategory(categoryObj.id);
        }
      }
    } catch (e) {
      // Handle error gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error filtering tutors: $e')),
        );
      }
    }
  }



  Widget get _categoryFilter => Consumer<CategoryProvider>(
        builder: (context, categoryProvider, child) {
          final categories = ['All', ...categoryProvider.popularCategories.map((c) => c.name)];

          return SizedBox(
            height: 40,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              separatorBuilder: (_, __) => const SizedBox(width: 12),
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = category == _selectedCategory;
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (category != 'All') {
                      // Navigate to tutor search with category pre-selected
                      _navigateToTutorSearchWithSubject(category);
                    } else {
                      // Filter tutors by category for 'All'
                      WidgetsBinding.instance.addPostFrameCallback((_) async {
                        if (mounted) {
                          setState(() {
                            _selectedCategory = category;
                          });
                          await _filterTutorsByCategory(category);
                        }
                      });
                    }
                  },
                  backgroundColor: Colors.white,
                  selectedColor: Colors.blue.shade700,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                    side: BorderSide(
                      color: isSelected ? Colors.blue.shade700 : Colors.grey.shade300,
                    ),
                  ),
                );
              },
            ),
          );
        },
      );

  Widget get _banner => Container(
        height: 160,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade700,
              Colors.blue.shade500,
              Colors.lightBlue.shade400,
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.shade700.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              top: -20,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),
            Positioned(
              right: 20,
              bottom: -10,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.05),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Find the Best Tutors',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 4,
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'For Every Subject',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, AppRoute.tutorSearchScreen);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.blue.shade700,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    ),
                    child: const Text(
                      'Browse Tutors',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Widget _sectionHeader(String title, {VoidCallback? onTap}) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          GestureDetector(
            onTap: onTap,
            child: Text(
              'See All',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      );

  Widget _tutorHorizontalList() {
    return Consumer<TutorProvider>(
      builder: (context, tutorProvider, child) {
        if (tutorProvider.isLoading && tutorProvider.featuredTutors.isEmpty) {
          return const SizedBox(
            height: 180,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (tutorProvider.featuredTutors.isEmpty) {
          return const SizedBox(
            height: 180,
            child: Center(
              child: Text(
                'No featured tutors available',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          );
        }

        return SizedBox(
          height: 180,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: tutorProvider.featuredTutors.length,
            separatorBuilder: (_, __) => const SizedBox(width: 16),
            itemBuilder: (context, i) {
              final tutor = tutorProvider.featuredTutors[i];
              return Container(
                width: 140,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: const [
                    BoxShadow(color: Colors.black12, blurRadius: 6, offset: Offset(0, 2))
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: 36,
                      backgroundImage: tutor.image != null
                          ? NetworkImage(tutor.image!)
                          : null,
                      backgroundColor: Colors.blue.shade100,
                      child: tutor.image == null
                          ? Text(
                              (tutor.name?.isNotEmpty ?? false) ? tutor.name![0].toUpperCase() : 'T',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      tutor.name ?? 'Tutor',
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      tutor.primaryCategory,
                      style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        Text(
                          tutor.rating.toStringAsFixed(1),
                          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade700,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                              padding: const EdgeInsets.symmetric(vertical: 4),
                            ),
                            onPressed: () {
                              _showTutorProfile(tutor);
                            },
                            child: const Text(
                              'Profile',
                              style: TextStyle(fontSize: 10, color: Colors.white),
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green.shade600,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                              padding: const EdgeInsets.symmetric(vertical: 4),
                            ),
                            onPressed: () {
                              _openChatWithTutor(tutor);
                            },
                            child: const Text(
                              'Chat',
                              style: TextStyle(fontSize: 10, color: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _subjectGrid() {
    final subjects = [
      {'name': 'Math', 'icon': Icons.calculate, 'tutors': '120+'},
      {'name': 'English', 'icon': Icons.language, 'tutors': '95+'},
      {'name': 'Physics', 'icon': Icons.science, 'tutors': '80+'},
      {'name': 'Chemistry', 'icon': Icons.biotech, 'tutors': '65+'},
      {'name': 'Biology', 'icon': Icons.eco, 'tutors': '70+'},
      {'name': 'History', 'icon': Icons.history_edu, 'tutors': '45+'},
      {'name': 'IT', 'icon': Icons.computer, 'tutors': '110+'},
      {'name': 'Khmer', 'icon': Icons.translate, 'tutors': '55+'},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: subjects.length,
      itemBuilder: (context, index) {
        final subject = subjects[index];
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () {
                _navigateToTutorSearchWithSubject(subject['name'] as String);
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        subject['icon'] as IconData,
                        color: Colors.blue.shade700,
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      subject['name'] as String,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${subject['tutors']} tutors',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _featuredPostsList() {
    return Consumer<PostProvider>(
      builder: (context, postProvider, child) {
        if (postProvider.isLoading && postProvider.featuredPosts.isEmpty) {
          return const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (postProvider.featuredPosts.isEmpty) {
          return Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.article_outlined, size: 48, color: Colors.grey.shade400),
                  const SizedBox(height: 12),
                  Text(
                    'No featured posts available',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Check back later for new content',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: postProvider.featuredPosts.length,
            itemBuilder: (context, index) {
              final post = postProvider.featuredPosts[index];
              return Container(
                width: 300,
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: InkWell(
                  onTap: () => _showPostDetail(post),
                  borderRadius: BorderRadius.circular(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Post image or placeholder
                      Container(
                        height: 100,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.blue.shade400,
                              Colors.blue.shade600,
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            if (post.featuredImage != null && post.featuredImage!.isNotEmpty)
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                                child: Image.network(
                                  post.featuredImageUrl,
                                  width: double.infinity,
                                  height: 100,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) => _buildPostPlaceholder(),
                                ),
                              )
                            else
                              _buildPostPlaceholder(),
                            // Post type badge
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  post.postType.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Post content
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                post.title,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                post.displayExcerpt,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              Row(
                                children: [
                                  Icon(Icons.visibility, size: 14, color: Colors.grey.shade500),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${post.viewsCount}',
                                    style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                                  ),
                                  const SizedBox(width: 12),
                                  Icon(Icons.favorite, size: 14, color: Colors.grey.shade500),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${post.likesCount}',
                                    style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                                  ),
                                  const Spacer(),
                                  Text(
                                    '${post.readingTime} min read',
                                    style: TextStyle(fontSize: 10, color: Colors.grey.shade500),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildPostPlaceholder() {
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade400,
            Colors.blue.shade600,
          ],
        ),
      ),
      child: const Icon(
        Icons.article,
        size: 40,
        color: Colors.white,
      ),
    );
  }

  Widget _reviewsList() {
    final reviews = [
      {
        'student': 'Sarah Kim',
        'tutor': 'John Smith',
        'subject': 'Math',
        'rating': 5,
        'comment': 'Excellent teacher! Very patient and explains concepts clearly.',
        'avatar': 'assets/images/avatar1.png',
      },
      {
        'student': 'David Chen',
        'tutor': 'Emily Johnson',
        'subject': 'English',
        'rating': 4,
        'comment': 'Great help with essay writing and grammar.',
        'avatar': 'assets/images/avatar2.png',
      },
      {
        'student': 'Lisa Wong',
        'tutor': 'Michael Brown',
        'subject': 'Physics',
        'rating': 5,
        'comment': 'Made physics fun and easy to understand!',
        'avatar': 'assets/images/avatar3.png',
      },
    ];

    return SizedBox(
      height: 200,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: reviews.length,
        separatorBuilder: (_, __) => const SizedBox(width: 16),
        itemBuilder: (context, index) {
          final review = reviews[index];
          return Container(
            width: 280,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: AssetImage(review['avatar'] as String),
                        backgroundColor: Colors.blue.shade100,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              review['student'] as String,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              '${review['subject']} with ${review['tutor']}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: List.generate(5, (i) => Icon(
                          i < (review['rating'] as int) ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 16,
                        )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: Text(
                      review['comment'] as String,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 13,
                        height: 1.4,
                      ),
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
