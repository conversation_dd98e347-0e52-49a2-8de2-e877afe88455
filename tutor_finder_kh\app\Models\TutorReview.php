<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TutorReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'tutor_id',
        'student_id',
        'session_id',
        'post_id',
        'rating',
        'comment',
        'is_anonymous',
        'is_approved',
        'review_type',
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'is_approved' => 'boolean',
    ];

    /**
     * Get the tutor that owns this review.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class);
    }

    /**
     * Get the student that wrote this review.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the session this review is for.
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(Session::class);
    }

    /**
     * Get the tutor post this review is for.
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(TutorPost::class, 'post_id');
    }

    /**
     * Get the student name for display (considering anonymity).
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }
        
        return $this->student->name ?? 'Unknown';
    }

    /**
     * Scope a query to only include approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include pending reviews.
     */
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    /**
     * Scope a query to filter by rating.
     */
    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope a query to filter by minimum rating.
     */
    public function scopeMinRating($query, int $minRating)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Scope a query to filter by review type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('review_type', $type);
    }

    /**
     * Scope a query to only include session reviews.
     */
    public function scopeSessionReviews($query)
    {
        return $query->where('review_type', 'session')
                    ->whereNotNull('session_id');
    }

    /**
     * Scope a query to only include post reviews.
     */
    public function scopePostReviews($query)
    {
        return $query->where('review_type', 'post')
                    ->whereNotNull('post_id');
    }

    /**
     * Scope a query to only include tutor reviews (general).
     */
    public function scopeTutorReviews($query)
    {
        return $query->where('review_type', 'tutor')
                    ->whereNull('session_id')
                    ->whereNull('post_id');
    }

    /**
     * Check if this is a session review.
     */
    public function isSessionReview(): bool
    {
        return $this->review_type === 'session' && !is_null($this->session_id);
    }

    /**
     * Check if this is a post review.
     */
    public function isPostReview(): bool
    {
        return $this->review_type === 'post' && !is_null($this->post_id);
    }

    /**
     * Check if this is a general tutor review.
     */
    public function isTutorReview(): bool
    {
        return $this->review_type === 'tutor' && is_null($this->session_id) && is_null($this->post_id);
    }

    /**
     * Get the review subject (what is being reviewed).
     */
    public function getReviewSubjectAttribute(): string
    {
        if ($this->isSessionReview()) {
            return 'Session with ' . $this->tutor->name;
        } elseif ($this->isPostReview()) {
            return 'Post: ' . ($this->post->title ?? 'Unknown Post');
        } else {
            return 'Tutor: ' . $this->tutor->name;
        }
    }
}
