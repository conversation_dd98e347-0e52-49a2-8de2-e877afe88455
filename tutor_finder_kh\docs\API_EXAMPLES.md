# TutorFinder API - Request/Response Examples

## Authentication Examples

### 1. Student Registration

**Request:**
```http
POST /api/v1/auth/register/student
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "******-0123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Student registered successfully",
    "data": {
        "user": {
            "id": 1,
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "******-0123",
            "created_at": "2024-01-15T10:30:00.000000Z"
        },
        "token": "1|abc123def456...",
        "user_type": "student"
    }
}
```

### 2. Tutor Registration

**Request:**
```http
POST /api/v1/auth/register/tutor
Content-Type: application/json

{
    "name": "<PERSON><PERSON> <PERSON>",
    "email": "micha<PERSON>@example.com",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "******-0456",
    "description": "PhD in Mathematics with 10 years of teaching experience",
    "location": "New York, NY",
    "hourly_rate": 45.00,
    "experience_years": 10,
    "qualifications": "PhD in Mathematics, MIT",
    "languages": ["English", "Chinese"]
}
```

**Response:**
```json
{
    "success": true,
    "message": "Tutor registered successfully. Account pending approval.",
    "data": {
        "user": {
            "id": 1,
            "name": "Dr. Michael Chen",
            "email": "<EMAIL>",
            "phone": "******-0456",
            "description": "PhD in Mathematics with 10 years of teaching experience",
            "location": "New York, NY",
            "hourly_rate": "45.00",
            "experience_years": 10,
            "status": "pending",
            "created_at": "2024-01-15T10:30:00.000000Z"
        },
        "token": "2|def456ghi789...",
        "user_type": "tutor"
    }
}
```

### 3. Login

**Request:**
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123",
    "user_type": "student"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "Sarah Johnson",
            "email": "<EMAIL>",
            "phone": "******-0123"
        },
        "token": "3|ghi789jkl012...",
        "user_type": "student"
    }
}
```

## Tutor Examples

### 1. Get All Tutors with Filters

**Request:**
```http
GET /api/v1/tutors?search=math&category_id=1&min_rating=4&sort_by=rating&per_page=5
```

**Response:**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "name": "Dr. Michael Chen",
                "description": "PhD in Mathematics with 10 years of teaching experience",
                "location": "New York, NY",
                "hourly_rate": "45.00",
                "experience_years": 10,
                "is_online": true,
                "status": "approved",
                "average_rating": 4.8,
                "review_count": 25,
                "categories": [
                    {
                        "id": 1,
                        "name": "Mathematics"
                    }
                ]
            }
        ],
        "per_page": 5,
        "total": 1
    }
}
```

### 2. Get Specific Tutor

**Request:**
```http
GET /api/v1/tutors/1
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Dr. Michael Chen",
        "description": "PhD in Mathematics with 10 years of teaching experience",
        "location": "New York, NY",
        "hourly_rate": "45.00",
        "experience_years": 10,
        "qualifications": "PhD in Mathematics, MIT",
        "languages": ["English", "Chinese"],
        "is_online": true,
        "status": "approved",
        "average_rating": 4.8,
        "review_count": 25,
        "categories": [
            {
                "id": 1,
                "name": "Mathematics"
            }
        ],
        "reviews": [
            {
                "id": 1,
                "rating": 5,
                "comment": "Excellent teacher! Very patient and explains concepts clearly.",
                "created_at": "2024-01-10T15:30:00.000000Z",
                "student": {
                    "id": 1,
                    "name": "Sarah Johnson"
                }
            }
        ]
    }
}
```

## Favorites Examples

### 1. Add to Favorites

**Request:**
```http
POST /api/v1/favorites
Authorization: Bearer 3|ghi789jkl012...
Content-Type: application/json

{
    "tutor_id": 1
}
```

**Response:**
```json
{
    "success": true,
    "message": "Tutor added to favorites",
    "data": {
        "id": 1,
        "student_id": 1,
        "tutor_id": 1,
        "created_at": "2024-01-15T11:00:00.000000Z",
        "tutor": {
            "id": 1,
            "name": "Dr. Michael Chen",
            "hourly_rate": "45.00",
            "average_rating": 4.8
        }
    }
}
```

### 2. Get Favorites

**Request:**
```http
GET /api/v1/favorites
Authorization: Bearer 3|ghi789jkl012...
```

**Response:**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "created_at": "2024-01-15T11:00:00.000000Z",
                "tutor": {
                    "id": 1,
                    "name": "Dr. Michael Chen",
                    "description": "PhD in Mathematics with 10 years of teaching experience",
                    "hourly_rate": "45.00",
                    "average_rating": 4.8,
                    "review_count": 25,
                    "categories": [
                        {
                            "id": 1,
                            "name": "Mathematics"
                        }
                    ]
                }
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

## Session Examples

### 1. Book a Session

**Request:**
```http
POST /api/v1/sessions
Authorization: Bearer 3|ghi789jkl012...
Content-Type: application/json

{
    "tutor_id": 1,
    "session_date": "2024-01-20T14:00:00Z",
    "duration_minutes": 60,
    "session_type": "online",
    "notes": "Need help with calculus homework"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Session booked successfully",
    "data": {
        "id": 1,
        "tutor_id": 1,
        "student_id": 1,
        "session_date": "2024-01-20T14:00:00.000000Z",
        "duration_minutes": 60,
        "price": "45.00",
        "status": "booked",
        "session_type": "online",
        "notes": "Need help with calculus homework",
        "created_at": "2024-01-15T11:30:00.000000Z",
        "tutor": {
            "id": 1,
            "name": "Dr. Michael Chen"
        }
    }
}
```

### 2. Get User Sessions

**Request:**
```http
GET /api/v1/sessions
Authorization: Bearer 3|ghi789jkl012...
```

**Response:**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "session_date": "2024-01-20T14:00:00.000000Z",
                "duration_minutes": 60,
                "price": "45.00",
                "status": "booked",
                "session_type": "online",
                "notes": "Need help with calculus homework",
                "tutor": {
                    "id": 1,
                    "name": "Dr. Michael Chen",
                    "phone": "******-0456"
                }
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

## Review Examples

### 1. Create Review

**Request:**
```http
POST /api/v1/reviews
Authorization: Bearer 3|ghi789jkl012...
Content-Type: application/json

{
    "tutor_id": 1,
    "session_id": 1,
    "rating": 5,
    "comment": "Excellent teacher! Very patient and explains concepts clearly.",
    "is_anonymous": false
}
```

**Response:**
```json
{
    "success": true,
    "message": "Review created successfully",
    "data": {
        "id": 1,
        "tutor_id": 1,
        "student_id": 1,
        "session_id": 1,
        "rating": 5,
        "comment": "Excellent teacher! Very patient and explains concepts clearly.",
        "is_anonymous": false,
        "is_approved": true,
        "created_at": "2024-01-15T12:00:00.000000Z"
    }
}
```

## Error Examples

### 1. Validation Error

**Request:**
```http
POST /api/v1/auth/register/student
Content-Type: application/json

{
    "name": "",
    "email": "invalid-email",
    "password": "123"
}
```

**Response:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "name": ["The name field is required."],
        "email": ["The email field must be a valid email address."],
        "password": ["The password field must be at least 6 characters.", "The password field confirmation does not match."]
    }
}
```

### 2. Authentication Error

**Request:**
```http
GET /api/v1/favorites
```

**Response:**
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### 3. Authorization Error

**Request:**
```http
POST /api/v1/favorites
Authorization: Bearer tutor_token_here...
Content-Type: application/json

{
    "tutor_id": 1
}
```

**Response:**
```json
{
    "success": false,
    "message": "Access denied. This endpoint is only available for students."
}
```

### 4. Resource Not Found

**Request:**
```http
GET /api/v1/tutors/999
```

**Response:**
```json
{
    "success": false,
    "message": "Tutor not found"
}
```
