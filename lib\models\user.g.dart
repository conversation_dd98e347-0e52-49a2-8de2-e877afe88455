// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Student _$StudentFromJson(Map<String, dynamic> json) => Student(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      avatar: json['avatar'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      gender: json['gender'] as String?,
      location: json['location'] as String?,
      bio: json['bio'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$StudentToJson(Student instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
      'avatar': instance.avatar,
      'date_of_birth': instance.dateOfBirth,
      'gender': instance.gender,
      'location': instance.location,
      'bio': instance.bio,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

Tutor _$TutorFromJson(Map<String, dynamic> json) => Tutor(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      phone: json['phone'] as String?,
      isOnline: _boolFromJson(json['is_online']),
      location: json['location'] as String?,
      totalStudents: json['totalStudents'] as String?,
      image: json['image'] as String?,
      status: json['status'] as String?,
      hourlyRate: json['hourly_rate'] as String?,
      experienceYears: (json['experience_years'] as num?)?.toInt(),
      qualifications: json['qualifications'] as String?,
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      email: json['email'] as String?,
      averageRating: (json['average_rating'] as num?)?.toDouble(),
      reviewCount: (json['review_count'] as num?)?.toInt(),
      completedSessionsCount:
          (json['completed_sessions_count'] as num?)?.toInt(),
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => TutorCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
      reviews: (json['reviews'] as List<dynamic>?)
          ?.map((e) => TutorReview.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$TutorToJson(Tutor instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'phone': instance.phone,
      'is_online': instance.isOnline,
      'location': instance.location,
      'totalStudents': instance.totalStudents,
      'image': instance.image,
      'status': instance.status,
      'hourly_rate': instance.hourlyRate,
      'experience_years': instance.experienceYears,
      'qualifications': instance.qualifications,
      'languages': instance.languages,
      'email': instance.email,
      'average_rating': instance.averageRating,
      'review_count': instance.reviewCount,
      'completed_sessions_count': instance.completedSessionsCount,
      'categories': instance.categories,
      'reviews': instance.reviews,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

TutorCategory _$TutorCategoryFromJson(Map<String, dynamic> json) =>
    TutorCategory(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      tutorCount: (json['tutor_count'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$TutorCategoryToJson(TutorCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'tutor_count': instance.tutorCount,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

TutorReview _$TutorReviewFromJson(Map<String, dynamic> json) => TutorReview(
      id: (json['id'] as num).toInt(),
      tutorId: (json['tutor_id'] as num).toInt(),
      studentId: (json['student_id'] as num).toInt(),
      sessionId: (json['session_id'] as num?)?.toInt(),
      rating: (json['rating'] as num).toInt(),
      comment: json['comment'] as String?,
      isAnonymous: json['is_anonymous'] as bool,
      isApproved: json['is_approved'] as bool,
      student: json['student'] == null
          ? null
          : Student.fromJson(json['student'] as Map<String, dynamic>),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$TutorReviewToJson(TutorReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'tutor_id': instance.tutorId,
      'student_id': instance.studentId,
      'session_id': instance.sessionId,
      'rating': instance.rating,
      'comment': instance.comment,
      'is_anonymous': instance.isAnonymous,
      'is_approved': instance.isApproved,
      'student': instance.student,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

Admin _$AdminFromJson(Map<String, dynamic> json) => Admin(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      emailVerifiedAt: json['email_verified_at'] as String?,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      role: json['role'] as String? ?? 'admin',
      status: json['status'] as String? ?? 'active',
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$AdminToJson(Admin instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'email_verified_at': instance.emailVerifiedAt,
      'phone': instance.phone,
      'avatar': instance.avatar,
      'role': instance.role,
      'status': instance.status,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
