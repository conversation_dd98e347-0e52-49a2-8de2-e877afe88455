import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'dart:convert';
import '../models/api_response.dart';
import '../models/profile_update_request.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class ProfileService {
  static final ProfileService _instance = ProfileService._internal();
  factory ProfileService() => _instance;
  ProfileService._internal();

  final ApiClient _apiClient = ApiClient();

  /// Update user profile with optional image upload
  Future<ApiResponse<ProfileUpdateResponse>> updateProfile({
    String? name,
    String? phone,
    String? dateOfBirth,
    String? gender,
    String? address,
    Uint8List? avatarBytes,
    String? avatarFileName,
    TutorUpdateData? tutorData,
    StudentUpdateData? studentData,
  }) async {
    try {
      final token = await _apiClient.getToken();
      if (token == null) {
        return ApiResponse<ProfileUpdateResponse>(
          success: false,
          message: 'Authentication token not found',
        );
      }

      final uri = Uri.parse('${ApiConfig.baseUrl}/auth/profile');
      final request = http.MultipartRequest('POST', uri);

      // Add method override for PUT since multipart doesn't support PUT directly
      request.fields['_method'] = 'PUT';

      // Add headers
      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'application/json',
      });

      // Add text fields
      if (name != null) request.fields['name'] = name;
      if (phone != null) request.fields['phone'] = phone;
      if (dateOfBirth != null) request.fields['date_of_birth'] = dateOfBirth;
      if (gender != null) request.fields['gender'] = gender;
      if (address != null) request.fields['address'] = address;

      // Add tutor data if provided
      if (tutorData != null) {
        final tutorJson = tutorData.toJson();
        for (final entry in tutorJson.entries) {
          if (entry.value != null) {
            if (entry.value is List) {
              request.fields['tutor_data[${entry.key}]'] = jsonEncode(entry.value);
            } else if (entry.value is Map) {
              request.fields['tutor_data[${entry.key}]'] = jsonEncode(entry.value);
            } else {
              request.fields['tutor_data[${entry.key}]'] = entry.value.toString();
            }
          }
        }
      }

      // Add student data if provided
      if (studentData != null) {
        final studentJson = studentData.toJson();
        for (final entry in studentJson.entries) {
          if (entry.value != null) {
            if (entry.value is List) {
              request.fields['student_data[${entry.key}]'] = jsonEncode(entry.value);
            } else if (entry.value is Map) {
              request.fields['student_data[${entry.key}]'] = jsonEncode(entry.value);
            } else {
              request.fields['student_data[${entry.key}]'] = entry.value.toString();
            }
          }
        }
      }

      // Add avatar file if provided
      if (avatarBytes != null && avatarFileName != null) {
        // Determine MIME type from file extension
        String? mimeType;
        final extension = avatarFileName.toLowerCase().split('.').last;
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'gif':
            mimeType = 'image/gif';
            break;
          case 'webp':
            mimeType = 'image/webp';
            break;
          default:
            mimeType = 'image/jpeg'; // Default fallback
        }

        final multipartFile = http.MultipartFile.fromBytes(
          'avatar',
          avatarBytes,
          filename: avatarFileName,
          contentType: MediaType.parse(mimeType),
        );
        request.files.add(multipartFile);
      }

      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('🚀 PROFILE UPDATE REQUEST: ${request.url}');
      print('📤 FIELDS: ${request.fields}');
      print('📁 FILES: ${request.files.map((f) => f.field).toList()}');
      print('✅ RESPONSE CODE: ${response.statusCode}');
      print('📥 RESPONSE BODY: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final profileResponse = ProfileUpdateResponse.fromJson(jsonData);
        
        return ApiResponse<ProfileUpdateResponse>(
          success: true,
          message: profileResponse.message,
          data: profileResponse,
        );
      } else {
        final jsonData = jsonDecode(response.body);
        return ApiResponse<ProfileUpdateResponse>(
          success: false,
          message: jsonData['message'] ?? 'Profile update failed',
          errors: jsonData['errors'],
        );
      }
    } catch (e) {
      print('❌ PROFILE UPDATE ERROR: $e');
      return ApiResponse<ProfileUpdateResponse>(
        success: false,
        message: 'Network error: $e',
      );
    }
  }

  /// Get current user profile
  Future<ApiResponse<dynamic>> getProfile() async {
    try {
      final token = await _apiClient.getToken();
      if (token == null) {
        return ApiResponse<dynamic>(
          success: false,
          message: 'Authentication token not found',
        );
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/auth/profile'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      print('🚀 GET PROFILE REQUEST: ${ApiConfig.baseUrl}/auth/profile');
      print('✅ RESPONSE CODE: ${response.statusCode}');
      print('📥 RESPONSE BODY: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return ApiResponse<dynamic>(
          success: true,
          message: jsonData['message'] ?? 'Profile retrieved successfully',
          data: jsonData['data'],
        );
      } else {
        final jsonData = jsonDecode(response.body);
        return ApiResponse<dynamic>(
          success: false,
          message: jsonData['message'] ?? 'Failed to get profile',
        );
      }
    } catch (e) {
      print('❌ GET PROFILE ERROR: $e');
      return ApiResponse<dynamic>(
        success: false,
        message: 'Network error: $e',
      );
    }
  }
}
