import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/tutor_provider.dart';
import '../models/user.dart';
import 'chat_screen.dart';

class SubjectDetailScreen extends StatefulWidget {
  final String subjectName;
  final int? subjectId;

  const SubjectDetailScreen({
    super.key,
    required this.subjectName,
    this.subjectId,
  });

  @override
  State<SubjectDetailScreen> createState() => _SubjectDetailScreenState();
}

class _SubjectDetailScreenState extends State<SubjectDetailScreen> {
  List<Tutor> _subjectTutors = [];

  @override
  void initState() {
    super.initState();
    // Use post-frame callback to avoid calling setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTutors();
    });
  }

  void _loadTutors() async {
    if (!mounted) return;

    try {
      final tutorProvider = Provider.of<TutorProvider>(context, listen: false);
      if (tutorProvider.tutors.isEmpty) {
        await tutorProvider.loadTutors();
      }
    } catch (e) {
      // Handle error gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading tutors: $e')),
        );
      }
    }
  }

  Future<void> _refreshTutors() async {
    final tutorProvider = Provider.of<TutorProvider>(context, listen: false);
    await tutorProvider.loadTutors();
  }

  void _openChatWithTutor(Tutor tutor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          otherUserId: tutor.id,
          otherUserType: 'tutor',
          otherUserName: tutor.name ?? 'Tutor',
          otherUserAvatar: tutor.image,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.subjectName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              '${_subjectTutors.length} tutors available',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () {
              _showFilterOptions();
            },
          ),
        ],
      ),
      body: Consumer<TutorProvider>(
        builder: (context, tutorProvider, child) {
          // Filter tutors by subject
          final filteredTutors = tutorProvider.tutors.where((tutor) {
            return tutor.categories?.any((category) =>
              category.name.toLowerCase().contains(widget.subjectName.toLowerCase())
            ) ?? false;
          }).toList();

          if (tutorProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (tutorProvider.errorMessage != null) {
            return _buildErrorState(tutorProvider.errorMessage!);
          }

          if (filteredTutors.isEmpty) {
            return _buildEmptyState();
          }

          return _buildTutorsList(filteredTutors);
        },
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refreshTutors,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No tutors found for ${widget.subjectName}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try browsing other subjects or check back later',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTutorsList(List<Tutor> tutors) {
    _subjectTutors = tutors; // Update the local list for filtering
    return RefreshIndicator(
      onRefresh: _refreshTutors,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSubjectHeader(),
          const SizedBox(height: 20),
          _buildTutorsGrid(),
        ],
      ),
    );
  }

  Widget _buildSubjectHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.subject,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.subjectName,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Text(
                      'Find the perfect tutor for your learning needs',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildStatCard('Tutors', '${_subjectTutors.length}', Icons.people),
              const SizedBox(width: 12),
              _buildStatCard('Avg Rating', '4.8', Icons.star),
              const SizedBox(width: 12),
              _buildStatCard('Online', '${_subjectTutors.where((t) => t.isOnline ?? false).length}', Icons.circle),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 10,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTutorsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Available Tutors',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            TextButton.icon(
              onPressed: _showSortOptions,
              icon: Icon(Icons.sort, size: 16, color: Colors.blue.shade700),
              label: Text(
                'Sort',
                style: TextStyle(color: Colors.blue.shade700),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...(_subjectTutors.map((tutor) => _buildTutorCard(tutor)).toList()),
      ],
    );
  }

  Widget _buildTutorCard(Tutor tutor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundImage: tutor.image != null
                          ? NetworkImage(tutor.image!)
                          : null,
                      backgroundColor: Colors.blue.shade100,
                      child: tutor.image == null
                          ? Text(
                              (tutor.name?.isNotEmpty ?? false) ? tutor.name![0].toUpperCase() : 'T',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            )
                          : null,
                    ),
                    if (tutor.isOnline ?? false)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tutor.name ?? 'Tutor',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (tutor.isOnline ?? false)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'Online',
                                style: TextStyle(
                                  color: Colors.green.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            tutor.rating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          Text(
                            ' (${tutor.totalReviews} reviews)',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const Spacer(),
                          if (tutor.hourlyRate != null)
                            Text(
                              '\$${tutor.hourlyRateValue.toStringAsFixed(0)}/hr',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        tutor.experienceText,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (tutor.description != null) ...[
              const SizedBox(height: 12),
              Text(
                tutor.description!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _showTutorProfile(tutor);
                    },
                    icon: const Icon(Icons.person, size: 16),
                    label: const Text('View Profile'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade700,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _openChatWithTutor(tutor);
                    },
                    icon: const Icon(Icons.chat, size: 16),
                    label: const Text('Chat'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTutorProfile(Tutor tutor) {
    // TODO: Navigate to tutor profile screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${tutor.name} profile coming soon!')),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Tutors',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.circle, color: Colors.green),
              title: const Text('Online Only'),
              onTap: () {
                Navigator.pop(context);
                _filterOnlineTutors();
              },
            ),
            ListTile(
              leading: const Icon(Icons.star, color: Colors.amber),
              title: const Text('Highly Rated (4.5+)'),
              onTap: () {
                Navigator.pop(context);
                _filterHighRatedTutors();
              },
            ),
            ListTile(
              leading: const Icon(Icons.attach_money, color: Colors.blue),
              title: const Text('Budget Friendly'),
              onTap: () {
                Navigator.pop(context);
                _filterBudgetFriendly();
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh, color: Colors.grey),
              title: const Text('Clear Filters'),
              onTap: () {
                Navigator.pop(context);
                _refreshTutors();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Sort Tutors',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.star, color: Colors.amber),
              title: const Text('Highest Rated'),
              onTap: () {
                Navigator.pop(context);
                _sortByRating();
              },
            ),
            ListTile(
              leading: const Icon(Icons.attach_money, color: Colors.green),
              title: const Text('Lowest Price'),
              onTap: () {
                Navigator.pop(context);
                _sortByPrice();
              },
            ),
            ListTile(
              leading: const Icon(Icons.school, color: Colors.blue),
              title: const Text('Most Experienced'),
              onTap: () {
                Navigator.pop(context);
                _sortByExperience();
              },
            ),
            ListTile(
              leading: const Icon(Icons.circle, color: Colors.green),
              title: const Text('Online First'),
              onTap: () {
                Navigator.pop(context);
                _sortByOnlineStatus();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _filterOnlineTutors() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors = _subjectTutors.where((tutor) => tutor.isOnline ?? false).toList();
        });
      }
    });
  }

  void _filterHighRatedTutors() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors = _subjectTutors.where((tutor) => tutor.rating >= 4.5).toList();
        });
      }
    });
  }

  void _filterBudgetFriendly() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors = _subjectTutors.where((tutor) => tutor.hourlyRateValue <= 30).toList();
        });
      }
    });
  }

  void _sortByRating() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors.sort((a, b) => b.rating.compareTo(a.rating));
        });
      }
    });
  }

  void _sortByPrice() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors.sort((a, b) => a.hourlyRateValue.compareTo(b.hourlyRateValue));
        });
      }
    });
  }

  void _sortByExperience() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors.sort((a, b) => (b.experienceYears ?? 0).compareTo(a.experienceYears ?? 0));
        });
      }
    });
  }

  void _sortByOnlineStatus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _subjectTutors.sort((a, b) => (b.isOnline ?? false) ? 1 : -1);
        });
      }
    });
  }
}
