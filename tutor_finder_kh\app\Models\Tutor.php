<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Tutor extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subjects',
        'experience_years',
        'education_level',
        'hourly_rate',
        'bio',
        'qualifications',
        'languages',
        'availability',
        'location',
        'latitude',
        'longitude',
        'is_verified',
        'is_available',
        'rating',
        'reviews_count',
        'total_sessions',
        'response_time',
        'teaching_style',
        'specializations',
    ];

    protected $casts = [
        'subjects' => 'array',
        'languages' => 'array',
        'availability' => 'array',
        'qualifications' => 'array',
        'specializations' => 'array',
        'hourly_rate' => 'decimal:2',
        'rating' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_verified' => 'boolean',
        'is_available' => 'boolean',
        'reviews_count' => 'integer',
        'total_sessions' => 'integer',
        'response_time' => 'integer',
    ];

    /**
     * Get the user that owns the tutor profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the categories that belong to this tutor.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'tutor_categories');
    }

    /**
     * Get the favorites for this tutor.
     */
    public function favorites(): HasMany
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * Get the reviews for this tutor.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(TutorReview::class);
    }

    /**
     * Get the posts created by this tutor.
     */
    public function posts(): HasMany
    {
        return $this->hasMany(TutorPost::class);
    }

    /**
     * Get the sessions for this tutor.
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(Session::class);
    }

    /**
     * Get the documents for this tutor.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Get the reports against this tutor.
     */
    public function reports(): HasMany
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Get the average rating for this tutor.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()->where('is_approved', true)->avg('rating') ?? 0;
    }

    /**
     * Get the total number of reviews for this tutor.
     */
    public function getReviewCountAttribute(): int
    {
        return $this->reviews()->where('is_approved', true)->count();
    }

    /**
     * Get the total number of completed sessions for this tutor.
     */
    public function getCompletedSessionsCountAttribute(): int
    {
        return $this->sessions()->where('status', 'completed')->count();
    }

    /**
     * Check if tutor is favorited by a specific student.
     */
    public function isFavoritedBy(int $studentId): bool
    {
        return $this->favorites()->where('student_id', $studentId)->exists();
    }

    /**
     * Scope a query to only include approved tutors.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include online tutors.
     */
    public function scopeOnline($query)
    {
        return $query->where('is_online', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->whereHas('categories', function ($q) use ($categoryId) {
            $q->where('category_id', $categoryId);
        });
    }

    /**
     * Scope a query to filter by minimum rating.
     */
    public function scopeWithMinRating($query, $minRating)
    {
        return $query->whereHas('reviews', function ($q) use ($minRating) {
            $q->selectRaw('AVG(rating) as avg_rating')
              ->groupBy('tutor_id')
              ->havingRaw('AVG(rating) >= ?', [$minRating]);
        });
    }
}
