// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favorite.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Favorite _$FavoriteFromJson(Map<String, dynamic> json) => Favorite(
      id: (json['id'] as num).toInt(),
      studentId: (json['student_id'] as num).toInt(),
      tutorId: (json['tutor_id'] as num).toInt(),
      tutor: json['tutor'] == null
          ? null
          : Tutor.from<PERSON>son(json['tutor'] as Map<String, dynamic>),
      student: json['student'] == null
          ? null
          : Student.fromJson(json['student'] as Map<String, dynamic>),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$FavoriteToJson(Favorite instance) => <String, dynamic>{
      'id': instance.id,
      'student_id': instance.studentId,
      'tutor_id': instance.tutorId,
      'tutor': instance.tutor,
      'student': instance.student,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

FavoriteCheck _$FavoriteCheckFromJson(Map<String, dynamic> json) =>
    FavoriteCheck(
      tutorId: (json['tutor_id'] as num).toInt(),
      isFavorited: json['is_favorited'] as bool,
    );

Map<String, dynamic> _$FavoriteCheckToJson(FavoriteCheck instance) =>
    <String, dynamic>{
      'tutor_id': instance.tutorId,
      'is_favorited': instance.isFavorited,
    };

FavoriteCount _$FavoriteCountFromJson(Map<String, dynamic> json) =>
    FavoriteCount(
      favoritesCount: (json['favorites_count'] as num).toInt(),
    );

Map<String, dynamic> _$FavoriteCountToJson(FavoriteCount instance) =>
    <String, dynamic>{
      'favorites_count': instance.favoritesCount,
    };
