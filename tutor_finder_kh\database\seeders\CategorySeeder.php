<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            'Mathematics',
            'English',
            'Physics',
            'Chemistry',
            'Biology',
            'History',
            'Geography',
            'Computer Science',
            'Programming',
            'Web Development',
            'Data Science',
            'Machine Learning',
            'Artificial Intelligence',
            'Economics',
            'Business Studies',
            'Accounting',
            'Finance',
            'Marketing',
            'Psychology',
            'Sociology',
            'Philosophy',
            'Literature',
            'Creative Writing',
            'Art',
            'Music',
            'Languages',
            'French',
            'Spanish',
            'German',
            'Chinese',
            'Japanese',
            'Korean',
            'Arabic',
            'Statistics',
            'Calculus',
            'Algebra',
            'Geometry',
            'Trigonometry',
            'SAT Prep',
            'ACT Prep',
            'IELTS',
            'TOEFL',
            'GRE',
            'GMAT',
        ];

        foreach ($categories as $category) {
            Category::create(['name' => $category]);
        }
    }
}
