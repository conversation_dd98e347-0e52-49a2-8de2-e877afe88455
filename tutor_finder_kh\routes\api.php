<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TutorController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\FavoriteController;
use App\Http\Controllers\Api\MessageController;
use App\Http\Controllers\Api\TutorPostController;
use App\Http\Controllers\Api\GroupController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('v1')->group(function () {

    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('register/student', [AuthController::class, 'registerStudent']);
        Route::post('register/tutor', [AuthController::class, 'registerTutor']);
        Route::post('login', [AuthController::class, 'login']);
    });

    // Public tutor routes
    Route::prefix('tutors')->group(function () {
        Route::get('/', [TutorController::class, 'index']);
        Route::get('featured', [TutorController::class, 'featured']);
        Route::get('filter-options', [TutorController::class, 'filterOptions']);
        Route::get('search', [TutorController::class, 'search']);
        Route::get('search/subject', [TutorController::class, 'searchBySubject']);
        Route::get('online', [TutorController::class, 'onlineTutors']);
        Route::get('offline', [TutorController::class, 'offlineTutors']);
        Route::get('subject/{subject}', [TutorController::class, 'bySubject']);
        Route::get('grade/{gradeLevel}', [TutorController::class, 'byGradeLevel']);
        Route::get('{id}', [TutorController::class, 'show']);
        Route::get('{id}/statistics', [TutorController::class, 'statistics']);
        Route::post('location', [TutorController::class, 'byLocation']);
    });

    // Public category routes
    Route::prefix('categories')->group(function () {
        Route::get('/', [CategoryController::class, 'index']);
        Route::get('popular', [CategoryController::class, 'popular']);
        Route::get('with-tutor-count', [CategoryController::class, 'withTutorCount']);
        Route::post('search', [CategoryController::class, 'search']);
        Route::get('{id}', [CategoryController::class, 'show']);
    });

    // Public tutor posts routes
    Route::prefix('posts')->group(function () {
        Route::get('/', [TutorPostController::class, 'index']);
        Route::get('featured', [TutorPostController::class, 'featured']);
        Route::get('tutor/{tutorId}', [TutorPostController::class, 'tutorPosts']);
        Route::get('{slug}', [TutorPostController::class, 'show']);
    });
});

// Protected routes (require authentication)
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {

    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
    });

    // Tutor management routes
    Route::prefix('tutors')->group(function () {
        Route::put('{id}/availability', [TutorController::class, 'updateAvailability']);
    });

    // Favorites routes
    Route::prefix('favorites')->group(function () {
        Route::get('/', [FavoriteController::class, 'index']);
        Route::post('/', [FavoriteController::class, 'store']);
        Route::delete('{tutorId}', [FavoriteController::class, 'destroy']);
        Route::get('check/{tutorId}', [FavoriteController::class, 'check']);
        Route::get('count', [FavoriteController::class, 'count']);
        Route::post('toggle', [FavoriteController::class, 'toggle']);
        Route::delete('/', [FavoriteController::class, 'clear']);
        Route::post('by-subject', [FavoriteController::class, 'bySubject']);
    });

    // Messages routes
    Route::prefix('messages')->group(function () {
        Route::get('conversations', [MessageController::class, 'conversations']);
        Route::get('conversation/{otherUserId}/{otherUserType}', [MessageController::class, 'getConversation']);
        Route::post('send', [MessageController::class, 'sendMessage']);
        Route::put('conversations/{conversationId}/read', [MessageController::class, 'markAsRead']);
        Route::get('unread-count', [MessageController::class, 'getUnreadCount']);
        Route::delete('{messageId}', [MessageController::class, 'deleteMessage']);
    });

    // Group routes
    Route::prefix('groups')->group(function () {
        Route::get('/', [GroupController::class, 'index']);
        Route::post('/', [GroupController::class, 'store']);
        Route::get('user', [MessageController::class, 'getUserGroups']);
        Route::get('{groupId}', [GroupController::class, 'show']);
        Route::post('{groupId}/join', [GroupController::class, 'join']);
        Route::post('{groupId}/leave', [GroupController::class, 'leave']);
        Route::get('{groupId}/messages', [MessageController::class, 'getGroupMessages']);
        Route::post('messages', [MessageController::class, 'sendGroupMessage']);
    });
    // Tutor posts management routes
    Route::prefix('posts')->group(function () {
        Route::post('/', [TutorPostController::class, 'store']);
        Route::put('{post}', [TutorPostController::class, 'update']);
        Route::delete('{post}', [TutorPostController::class, 'destroy']);
        Route::post('{post}/like', [TutorPostController::class, 'toggleLike']);
        Route::post('{post}/bookmark', [TutorPostController::class, 'toggleBookmark']);
    });

    // Admin routes (for category management)
    Route::middleware('admin')->group(function () {
        Route::prefix('categories')->group(function () {
            Route::post('/', [CategoryController::class, 'store']);
            Route::put('{id}', [CategoryController::class, 'update']);
            Route::delete('{id}', [CategoryController::class, 'destroy']);
        });
    });
});

// Test routes (no authentication required)
Route::prefix('test')->group(function () {
    Route::post('messages/send', [MessageController::class, 'sendMessageTest']);
    Route::post('tutor/send-message', [MessageController::class, 'tutorSendMessageTest']);
});

// Health check route
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found'
    ], 404);
});
