<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'reporter_id',
        'tutor_id',
        'reason',
        'description',
        'status',
        'admin_notes',
        'handled_by',
        'resolved_at',
    ];

    protected $casts = [
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the student who made this report.
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'reporter_id');
    }

    /**
     * Get the tutor this report is about.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class);
    }

    /**
     * Get the admin who handled this report.
     */
    public function handler(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'handled_by');
    }

    /**
     * Mark report as resolved.
     */
    public function markAsResolved(int $adminId, string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'handled_by' => $adminId,
            'admin_notes' => $notes,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Mark report as dismissed.
     */
    public function markAsDismissed(int $adminId, string $notes = null): void
    {
        $this->update([
            'status' => 'dismissed',
            'handled_by' => $adminId,
            'admin_notes' => $notes,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Scope a query to only include pending reports.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include resolved reports.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope a query to filter by reason.
     */
    public function scopeByReason($query, string $reason)
    {
        return $query->where('reason', $reason);
    }

    /**
     * Scope a query to filter by tutor.
     */
    public function scopeForTutor($query, int $tutorId)
    {
        return $query->where('tutor_id', $tutorId);
    }
}
