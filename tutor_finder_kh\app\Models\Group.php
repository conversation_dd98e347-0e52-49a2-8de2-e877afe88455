<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Group extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'creator_id',
        'creator_type',
        'subject',
        'max_members',
        'is_active',
        'group_type',
        'image',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'max_members' => 'integer',
    ];

    /**
     * Get the creator of the group (polymorphic).
     */
    public function creator()
    {
        return $this->morphTo('creator', 'creator_type', 'creator_id');
    }

    /**
     * Get the group members.
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'group_members')
                    ->withPivot(['joined_at', 'role', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * Get the group messages.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(GroupMessage::class)->orderBy('created_at', 'asc');
    }

    /**
     * Get active members only.
     */
    public function activeMembers(): BelongsToMany
    {
        return $this->members()->wherePivot('is_active', true);
    }

    /**
     * Check if user is a member of the group.
     */
    public function hasMember(int $userId): bool
    {
        return $this->members()->where('user_id', $userId)->exists();
    }

    /**
     * Add a member to the group.
     */
    public function addMember(int $userId, string $role = 'member'): bool
    {
        if ($this->hasMember($userId)) {
            return false;
        }

        if ($this->members()->count() >= $this->max_members) {
            return false;
        }

        $this->members()->attach($userId, [
            'joined_at' => now(),
            'role' => $role,
            'is_active' => true,
        ]);

        return true;
    }

    /**
     * Remove a member from the group.
     */
    public function removeMember(int $userId): bool
    {
        return $this->members()->detach($userId) > 0;
    }

    /**
     * Get member count.
     */
    public function getMemberCountAttribute(): int
    {
        return $this->activeMembers()->count();
    }

    /**
     * Check if group is full.
     */
    public function getIsFullAttribute(): bool
    {
        return $this->member_count >= $this->max_members;
    }

    /**
     * Scope for active groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for groups by subject.
     */
    public function scopeBySubject($query, string $subject)
    {
        return $query->where('subject', $subject);
    }

    /**
     * Scope for groups created by tutors.
     */
    public function scopeByTutors($query)
    {
        return $query->where('creator_type', 'tutor');
    }
}
