<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class ComprehensiveDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Clear existing data (except admin users)
        $this->clearExistingData();

        // Seed all data
        $this->seedUsers($now);
        $this->seedTutors($now);
        $this->seedStudents($now);
        $this->seedTutorCategories();
        $this->seedSessions($now);
        $this->seedChats($now);
        $this->seedReviews($now);
        $this->seedFavorites($now);
        $this->seedNotifications($now);
        $this->seedGroups($now);
        $this->seedGroupMembers();
        $this->seedGroupMessages($now);
        $this->seedConversations($now);
        $this->seedMessages($now);

        $this->command->info('Comprehensive data seeded successfully!');
    }

    private function clearExistingData(): void
    {
        DB::table('messages')->delete();
        DB::table('conversations')->delete();
        DB::table('group_messages')->delete();
        DB::table('group_members')->delete();
        DB::table('groups')->delete();
        DB::table('notifications')->delete();
        DB::table('favorites')->delete();
        DB::table('tutor_reviews')->delete();
        DB::table('chats')->delete();
        DB::table('sessions')->delete();
        DB::table('tutor_categories')->delete();
        DB::table('students')->delete();
        DB::table('tutors')->delete();
        DB::table('users')->where('user_type', '!=', 'admin')->delete();
    }

    private function seedUsers(Carbon $now): void
    {
        $users = [
            // Tutors
            [
                'name' => 'Dr. Sarah Johnson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456789',
                'date_of_birth' => '1985-03-15',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/sarah_johnson.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Prof. Michael Chen',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456790',
                'date_of_birth' => '1980-07-22',
                'gender' => 'male',
                'address' => 'Siem Reap, Cambodia',
                'avatar' => 'avatars/michael_chen.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Emily Davis',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456791',
                'date_of_birth' => '1988-11-08',
                'gender' => 'female',
                'address' => 'Battambang, Cambodia',
                'avatar' => 'avatars/emily_davis.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. James Wilson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456792',
                'date_of_birth' => '1982-05-30',
                'gender' => 'male',
                'address' => 'Kampong Cham, Cambodia',
                'avatar' => 'avatars/james_wilson.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Lisa Park',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456793',
                'date_of_birth' => '1990-09-12',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/lisa_park.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. Robert Kim',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456794',
                'date_of_birth' => '1983-12-03',
                'gender' => 'male',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/robert_kim.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Anna Martinez',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456795',
                'date_of_birth' => '1987-06-18',
                'gender' => 'female',
                'address' => 'Siem Reap, Cambodia',
                'avatar' => 'avatars/anna_martinez.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. David Thompson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456796',
                'date_of_birth' => '1979-04-25',
                'gender' => 'male',
                'address' => 'Battambang, Cambodia',
                'avatar' => 'avatars/david_thompson.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            // Students
            [
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654321',
                'date_of_birth' => '2005-08-15',
                'gender' => 'male',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/john_smith.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Emma Johnson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654322',
                'date_of_birth' => '2004-12-08',
                'gender' => 'female',
                'address' => 'Siem Reap, Cambodia',
                'avatar' => 'avatars/emma_johnson.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Michael Brown',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654323',
                'date_of_birth' => '2006-03-22',
                'gender' => 'male',
                'address' => 'Battambang, Cambodia',
                'avatar' => 'avatars/michael_brown.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Sophia Davis',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654324',
                'date_of_birth' => '2005-11-14',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/sophia_davis.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'William Wilson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654325',
                'date_of_birth' => '2004-07-09',
                'gender' => 'male',
                'address' => 'Kampong Cham, Cambodia',
                'avatar' => 'avatars/william_wilson.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Olivia Miller',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'student',
                'phone' => '+855987654326',
                'date_of_birth' => '2006-01-28',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'avatar' => 'avatars/olivia_miller.jpg',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('users')->insert($users);
    }

    private function seedTutors(Carbon $now): void
    {
        $userIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');

        $tutors = [
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Dr. Sarah Johnson',
                'subjects' => json_encode(['Mathematics', 'Statistics', 'Calculus']),
                'experience_years' => 8,
                'education_level' => 'PhD',
                'hourly_rate' => 25.00,
                'bio' => 'Expert in calculus and algebra with PhD in Mathematics from Royal University of Phnom Penh. Passionate about making complex mathematical concepts accessible to students.',
                'qualifications' => json_encode(['PhD Mathematics', 'Teaching Certificate', 'Research Publications']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode([
                    'Monday' => ['09:00-12:00', '14:00-17:00'],
                    'Tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'Wednesday' => ['09:00-12:00', '14:00-17:00'],
                    'Thursday' => ['09:00-12:00', '14:00-17:00'],
                    'Friday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Phnom Penh',
                'latitude' => 11.5564,
                'longitude' => 104.9282,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.9,
                'reviews_count' => 127,
                'total_sessions' => 450,
                'response_time' => 30,
                'teaching_style' => 'Interactive and patient approach with real-world applications',
                'specializations' => json_encode(['Algebra', 'Calculus', 'Statistics', 'Linear Algebra']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Prof. Michael Chen',
                'subjects' => json_encode(['Physics', 'Mathematics', 'Engineering']),
                'experience_years' => 12,
                'education_level' => 'PhD',
                'hourly_rate' => 30.00,
                'bio' => 'Physics professor with extensive research experience in quantum mechanics and theoretical physics. Published researcher with 15+ academic papers.',
                'qualifications' => json_encode(['PhD Physics', 'Research Publications', 'University Professor']),
                'languages' => json_encode(['English', 'Chinese', 'Khmer']),
                'availability' => json_encode([
                    'Monday' => ['10:00-13:00', '15:00-18:00'],
                    'Wednesday' => ['10:00-13:00', '15:00-18:00'],
                    'Friday' => ['10:00-13:00', '15:00-18:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Siem Reap',
                'latitude' => 13.3671,
                'longitude' => 103.8448,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 89,
                'total_sessions' => 320,
                'response_time' => 45,
                'teaching_style' => 'Research-based learning with practical experiments',
                'specializations' => json_encode(['Quantum Physics', 'Mechanics', 'Thermodynamics', 'Electromagnetism']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Ms. Emily Davis',
                'subjects' => json_encode(['English', 'Literature', 'Creative Writing']),
                'experience_years' => 6,
                'education_level' => 'Masters',
                'hourly_rate' => 20.00,
                'bio' => 'English literature specialist with focus on creative writing and grammar. TESOL certified with experience teaching international students.',
                'qualifications' => json_encode(['MA English Literature', 'TESOL Certificate', 'Creative Writing Workshop Leader']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode([
                    'Tuesday' => ['08:00-11:00', '13:00-16:00'],
                    'Thursday' => ['08:00-11:00', '13:00-16:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00'],
                    'Sunday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Battambang',
                'latitude' => 13.0957,
                'longitude' => 103.2027,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.7,
                'reviews_count' => 156,
                'total_sessions' => 280,
                'response_time' => 20,
                'teaching_style' => 'Creative and engaging methods with personalized feedback',
                'specializations' => json_encode(['Creative Writing', 'Grammar', 'Literature Analysis', 'Essay Writing']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Dr. James Wilson',
                'subjects' => json_encode(['Chemistry', 'Biology', 'Biochemistry']),
                'experience_years' => 10,
                'education_level' => 'PhD',
                'hourly_rate' => 28.00,
                'bio' => 'Chemistry and biology expert with laboratory research background. Specializes in making complex scientific concepts understandable.',
                'qualifications' => json_encode(['PhD Chemistry', 'Lab Research Experience', 'Scientific Publications']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode([
                    'Monday' => ['09:00-12:00', '14:00-17:00'],
                    'Tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'Thursday' => ['09:00-12:00', '14:00-17:00'],
                    'Friday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Kampong Cham',
                'latitude' => 12.0045,
                'longitude' => 105.4603,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.6,
                'reviews_count' => 98,
                'total_sessions' => 210,
                'response_time' => 35,
                'teaching_style' => 'Hands-on laboratory approach with visual demonstrations',
                'specializations' => json_encode(['Organic Chemistry', 'Biochemistry', 'Molecular Biology', 'Lab Techniques']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Ms. Lisa Park',
                'subjects' => json_encode(['Biology', 'Environmental Science', 'Ecology']),
                'experience_years' => 5,
                'education_level' => 'Masters',
                'hourly_rate' => 22.00,
                'bio' => 'Biology teacher with passion for environmental conservation and ecology. Field research experience in Southeast Asian ecosystems.',
                'qualifications' => json_encode(['MS Biology', 'Environmental Science Certificate', 'Field Research Experience']),
                'languages' => json_encode(['English', 'Khmer', 'Korean']),
                'availability' => json_encode([
                    'Wednesday' => ['08:00-11:00', '13:00-16:00'],
                    'Friday' => ['08:00-11:00', '13:00-16:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00'],
                    'Sunday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Phnom Penh',
                'latitude' => 11.5564,
                'longitude' => 104.9282,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 134,
                'total_sessions' => 195,
                'response_time' => 25,
                'teaching_style' => 'Field-based learning and practical applications',
                'specializations' => json_encode(['Ecology', 'Genetics', 'Environmental Biology', 'Conservation']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Dr. Robert Kim',
                'subjects' => json_encode(['Computer Science', 'Programming', 'Web Development']),
                'experience_years' => 7,
                'education_level' => 'Masters',
                'hourly_rate' => 26.00,
                'bio' => 'Full-stack developer and computer science educator. Expert in modern web technologies and programming languages.',
                'qualifications' => json_encode(['MS Computer Science', 'Industry Certifications', 'Open Source Contributor']),
                'languages' => json_encode(['English', 'Korean', 'Khmer']),
                'availability' => json_encode([
                    'Monday' => ['10:00-13:00', '15:00-18:00'],
                    'Tuesday' => ['10:00-13:00', '15:00-18:00'],
                    'Wednesday' => ['10:00-13:00', '15:00-18:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Phnom Penh',
                'latitude' => 11.5564,
                'longitude' => 104.9282,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.7,
                'reviews_count' => 76,
                'total_sessions' => 165,
                'response_time' => 15,
                'teaching_style' => 'Project-based learning with real-world applications',
                'specializations' => json_encode(['JavaScript', 'Python', 'React', 'Node.js', 'Database Design']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Ms. Anna Martinez',
                'subjects' => json_encode(['Spanish', 'French', 'Languages']),
                'experience_years' => 9,
                'education_level' => 'Masters',
                'hourly_rate' => 24.00,
                'bio' => 'Multilingual educator specializing in Romance languages. Native Spanish speaker with extensive European teaching experience.',
                'qualifications' => json_encode(['MA Applied Linguistics', 'DELE Examiner', 'European Teaching Certificate']),
                'languages' => json_encode(['Spanish', 'French', 'English', 'Khmer']),
                'availability' => json_encode([
                    'Tuesday' => ['09:00-12:00', '14:00-17:00'],
                    'Wednesday' => ['09:00-12:00', '14:00-17:00'],
                    'Thursday' => ['09:00-12:00', '14:00-17:00'],
                    'Sunday' => ['10:00-13:00', '15:00-18:00']
                ]),
                'location' => 'Siem Reap',
                'latitude' => 13.3671,
                'longitude' => 103.8448,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.9,
                'reviews_count' => 112,
                'total_sessions' => 245,
                'response_time' => 20,
                'teaching_style' => 'Immersive conversation-based learning',
                'specializations' => json_encode(['Conversational Spanish', 'Business French', 'Language Certification Prep']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Dr. David Thompson',
                'subjects' => json_encode(['History', 'Geography', 'Social Studies']),
                'experience_years' => 15,
                'education_level' => 'PhD',
                'hourly_rate' => 27.00,
                'bio' => 'History professor with specialization in Southeast Asian history and geography. Published author and documentary consultant.',
                'qualifications' => json_encode(['PhD History', 'Published Author', 'Documentary Consultant']),
                'languages' => json_encode(['English', 'Khmer', 'Thai']),
                'availability' => json_encode([
                    'Monday' => ['08:00-11:00', '13:00-16:00'],
                    'Wednesday' => ['08:00-11:00', '13:00-16:00'],
                    'Friday' => ['08:00-11:00', '13:00-16:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'location' => 'Battambang',
                'latitude' => 13.0957,
                'longitude' => 103.2027,
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 203,
                'total_sessions' => 387,
                'response_time' => 40,
                'teaching_style' => 'Storytelling approach with cultural context',
                'specializations' => json_encode(['Southeast Asian History', 'World Geography', 'Cultural Studies']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('tutors')->insert($tutors);
    }

    private function seedStudents(Carbon $now): void
    {
        $userIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $students = [
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'John Smith',
                'grade_level' => 'Grade 12',
                'school' => 'Phnom Penh International School',
                'learning_preferences' => json_encode(['Visual Learning', 'Interactive Sessions']),
                'subjects_of_interest' => json_encode(['Mathematics', 'Physics', 'Computer Science']),
                'preferred_learning_style' => 'Visual and Hands-on',
                'goals' => 'Prepare for university entrance exams and improve problem-solving skills',
                'availability' => json_encode([
                    'Monday' => ['16:00-18:00'],
                    'Tuesday' => ['16:00-18:00'],
                    'Wednesday' => ['16:00-18:00'],
                    'Thursday' => ['16:00-18:00'],
                    'Saturday' => ['09:00-12:00', '14:00-17:00']
                ]),
                'budget_range' => '$15-25 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Emma Johnson',
                'grade_level' => 'Grade 11',
                'school' => 'Siem Reap High School',
                'learning_preferences' => json_encode(['Discussion-based', 'Practice-oriented']),
                'subjects_of_interest' => json_encode(['English', 'Literature', 'History']),
                'preferred_learning_style' => 'Discussion and Analysis',
                'goals' => 'Improve English writing skills and literary analysis',
                'availability' => json_encode([
                    'Tuesday' => ['15:00-17:00'],
                    'Thursday' => ['15:00-17:00'],
                    'Saturday' => ['10:00-12:00'],
                    'Sunday' => ['10:00-12:00', '14:00-16:00']
                ]),
                'budget_range' => '$18-22 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Michael Brown',
                'grade_level' => 'Grade 10',
                'school' => 'Battambang Secondary School',
                'learning_preferences' => json_encode(['Step-by-step guidance', 'Practical examples']),
                'subjects_of_interest' => json_encode(['Chemistry', 'Biology', 'Mathematics']),
                'preferred_learning_style' => 'Structured and Methodical',
                'goals' => 'Build strong foundation in sciences for future medical studies',
                'availability' => json_encode([
                    'Monday' => ['17:00-19:00'],
                    'Wednesday' => ['17:00-19:00'],
                    'Friday' => ['17:00-19:00'],
                    'Sunday' => ['09:00-11:00', '15:00-17:00']
                ]),
                'budget_range' => '$20-28 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Sophia Davis',
                'grade_level' => 'Grade 11',
                'school' => 'Royal University of Fine Arts Prep',
                'learning_preferences' => json_encode(['Creative approach', 'Multimedia learning']),
                'subjects_of_interest' => json_encode(['Art', 'English', 'French']),
                'preferred_learning_style' => 'Creative and Visual',
                'goals' => 'Develop artistic skills and language proficiency for international art programs',
                'availability' => json_encode([
                    'Tuesday' => ['16:00-18:00'],
                    'Thursday' => ['16:00-18:00'],
                    'Saturday' => ['10:00-12:00', '14:00-16:00'],
                    'Sunday' => ['10:00-12:00']
                ]),
                'budget_range' => '$20-25 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'William Wilson',
                'grade_level' => 'Grade 12',
                'school' => 'Kampong Cham High School',
                'learning_preferences' => json_encode(['Problem-solving focus', 'Real-world applications']),
                'subjects_of_interest' => json_encode(['Economics', 'Business Studies', 'Mathematics']),
                'preferred_learning_style' => 'Analytical and Practical',
                'goals' => 'Prepare for business school and develop analytical thinking skills',
                'availability' => json_encode([
                    'Monday' => ['15:00-17:00'],
                    'Wednesday' => ['15:00-17:00'],
                    'Friday' => ['15:00-17:00'],
                    'Saturday' => ['09:00-11:00', '13:00-15:00']
                ]),
                'budget_range' => '$22-30 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'name' => 'Olivia Miller',
                'grade_level' => 'Grade 10',
                'school' => 'International School of Phnom Penh',
                'learning_preferences' => json_encode(['Interactive sessions', 'Group discussions']),
                'subjects_of_interest' => json_encode(['Biology', 'Environmental Science', 'Geography']),
                'preferred_learning_style' => 'Collaborative and Exploratory',
                'goals' => 'Develop environmental awareness and scientific research skills',
                'availability' => json_encode([
                    'Tuesday' => ['17:00-19:00'],
                    'Thursday' => ['17:00-19:00'],
                    'Saturday' => ['10:00-12:00'],
                    'Sunday' => ['14:00-16:00']
                ]),
                'budget_range' => '$18-24 per hour',
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('students')->insert($students);
    }

    private function seedTutorCategories(): void
    {
        $categoryIds = DB::table('categories')->pluck('id', 'name');
        $tutorIds = DB::table('tutors')->join('users', 'tutors.user_id', '=', 'users.id')
                                      ->pluck('tutors.id', 'users.email');

        $tutorCategories = [
            // Dr. Sarah Johnson - Mathematics
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Mathematics']],

            // Prof. Michael Chen - Physics, Mathematics
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Physics']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Mathematics']],

            // Ms. Emily Davis - English
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['English']],

            // Dr. James Wilson - Chemistry, Biology
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Chemistry']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Biology']],

            // Ms. Lisa Park - Biology
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Biology']],

            // Dr. Robert Kim - Computer Science, Programming
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Computer Science']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Programming']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Web Development']],

            // Ms. Anna Martinez - Languages
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['French']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Languages']],

            // Dr. David Thompson - History, Geography
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['History']],
            ['tutor_id' => $tutorIds['<EMAIL>'], 'category_id' => $categoryIds['Geography']],
        ];

        DB::table('tutor_categories')->insert($tutorCategories);
    }

    private function seedSessions(Carbon $now): void
    {
        $tutorIds = DB::table('tutors')->join('users', 'tutors.user_id', '=', 'users.id')
                                      ->pluck('tutors.id', 'users.email');
        $studentIds = DB::table('students')->join('users', 'students.user_id', '=', 'users.id')
                                          ->pluck('students.id', 'users.email');

        $sessions = [
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->addDays(1)->setTime(14, 0),
                'duration_minutes' => 60,
                'price' => 25.00,
                'status' => 'booked',
                'notes' => 'Focus on calculus derivatives and applications',
                'meeting_link' => 'https://meet.google.com/abc-defg-hij',
                'session_type' => 'online',
                'location' => null,
                'started_at' => null,
                'ended_at' => null,
                'cancellation_reason' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->subDays(2)->setTime(15, 0),
                'duration_minutes' => 90,
                'price' => 45.00,
                'status' => 'completed',
                'notes' => 'Physics mechanics - Newton\'s laws review',
                'meeting_link' => 'https://meet.google.com/xyz-uvwx-yz',
                'session_type' => 'online',
                'location' => null,
                'started_at' => $now->copy()->subDays(2)->setTime(15, 0),
                'ended_at' => $now->copy()->subDays(2)->setTime(16, 30),
                'cancellation_reason' => null,
                'created_at' => $now->copy()->subDays(3),
                'updated_at' => $now->copy()->subDays(2),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->addDays(3)->setTime(10, 0),
                'duration_minutes' => 60,
                'price' => 20.00,
                'status' => 'confirmed',
                'notes' => 'Essay writing techniques and structure',
                'meeting_link' => null,
                'session_type' => 'in_person',
                'location' => 'Battambang Public Library',
                'started_at' => null,
                'ended_at' => null,
                'cancellation_reason' => null,
                'created_at' => $now->copy()->subDays(1),
                'updated_at' => $now->copy()->subDays(1),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->subDays(1)->setTime(16, 0),
                'duration_minutes' => 75,
                'price' => 35.00,
                'status' => 'completed',
                'notes' => 'Organic chemistry reactions and mechanisms',
                'meeting_link' => 'https://meet.google.com/chem-lab-session',
                'session_type' => 'online',
                'location' => null,
                'started_at' => $now->copy()->subDays(1)->setTime(16, 0),
                'ended_at' => $now->copy()->subDays(1)->setTime(17, 15),
                'cancellation_reason' => null,
                'created_at' => $now->copy()->subDays(2),
                'updated_at' => $now->copy()->subDays(1),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->addDays(2)->setTime(11, 0),
                'duration_minutes' => 60,
                'price' => 22.00,
                'status' => 'booked',
                'notes' => 'Ecosystem dynamics and biodiversity',
                'meeting_link' => 'https://meet.google.com/bio-eco-study',
                'session_type' => 'online',
                'location' => null,
                'started_at' => null,
                'ended_at' => null,
                'cancellation_reason' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_date' => $now->copy()->subDays(5)->setTime(13, 0),
                'duration_minutes' => 120,
                'price' => 52.00,
                'status' => 'completed',
                'notes' => 'JavaScript fundamentals and DOM manipulation',
                'meeting_link' => 'https://meet.google.com/code-session-js',
                'session_type' => 'online',
                'location' => null,
                'started_at' => $now->copy()->subDays(5)->setTime(13, 0),
                'ended_at' => $now->copy()->subDays(5)->setTime(15, 0),
                'cancellation_reason' => null,
                'created_at' => $now->copy()->subDays(6),
                'updated_at' => $now->copy()->subDays(5),
            ],
        ];

        DB::table('sessions')->insert($sessions);
    }

    private function seedChats(Carbon $now): void
    {
        $sessionIds = DB::table('sessions')->pluck('id');
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $chats = [
            // Session 1 chats (Sarah Johnson & John Smith)
            [
                'session_id' => $sessionIds[0],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Hi John! Ready for our calculus session tomorrow?',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subHours(2),
                'created_at' => $now->copy()->subHours(3),
                'updated_at' => $now->copy()->subHours(2),
            ],
            [
                'session_id' => $sessionIds[0],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'Yes, Dr. Johnson! I\'ve been practicing the problems you sent.',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subHours(1),
                'created_at' => $now->copy()->subHours(2),
                'updated_at' => $now->copy()->subHours(1),
            ],
            [
                'session_id' => $sessionIds[0],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Great! We\'ll focus on the chain rule and its applications.',
                'message_type' => 'text',
                'is_read' => false,
                'read_at' => null,
                'created_at' => $now->copy()->subHours(1),
                'updated_at' => $now->copy()->subHours(1),
            ],
            // Session 2 chats (Michael Chen & John Smith) - Completed session
            [
                'session_id' => $sessionIds[1],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Excellent work today on Newton\'s laws! Your understanding has improved significantly.',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subDays(2)->addHours(1),
                'created_at' => $now->copy()->subDays(2)->addMinutes(30),
                'updated_at' => $now->copy()->subDays(2)->addHours(1),
            ],
            [
                'session_id' => $sessionIds[1],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'Thank you Prof. Chen! The examples really helped clarify the concepts.',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subDays(2)->addHours(2),
                'created_at' => $now->copy()->subDays(2)->addHours(1),
                'updated_at' => $now->copy()->subDays(2)->addHours(2),
            ],
        ];

        DB::table('chats')->insert($chats);
    }

    private function seedReviews(Carbon $now): void
    {
        $tutorIds = DB::table('tutors')->join('users', 'tutors.user_id', '=', 'users.id')
                                      ->pluck('tutors.id', 'users.email');
        $studentIds = DB::table('students')->join('users', 'students.user_id', '=', 'users.id')
                                          ->pluck('students.id', 'users.email');
        $sessionIds = DB::table('sessions')->where('status', 'completed')->pluck('id');

        $reviews = [
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_id' => $sessionIds[0] ?? null,
                'rating' => 5,
                'comment' => 'Prof. Chen is an excellent physics tutor! He explains complex concepts clearly and provides great examples.',
                'is_anonymous' => false,
                'is_approved' => true,
                'review_type' => 'tutor',
                'created_at' => $now->copy()->subDays(2),
                'updated_at' => $now->copy()->subDays(2),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_id' => $sessionIds[1] ?? null,
                'rating' => 4,
                'comment' => 'Dr. Wilson made organic chemistry much easier to understand. Great use of visual aids!',
                'is_anonymous' => false,
                'is_approved' => true,
                'review_type' => 'tutor',
                'created_at' => $now->copy()->subDays(1),
                'updated_at' => $now->copy()->subDays(1),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_id' => $sessionIds[2] ?? null,
                'rating' => 5,
                'comment' => 'Amazing programming tutor! Robert helped me understand JavaScript concepts that I was struggling with.',
                'is_anonymous' => false,
                'is_approved' => true,
                'review_type' => 'tutor',
                'created_at' => $now->copy()->subDays(5),
                'updated_at' => $now->copy()->subDays(5),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_id' => null,
                'rating' => 5,
                'comment' => 'Dr. Johnson is patient and thorough. Her teaching style really helps with understanding calculus.',
                'is_anonymous' => true,
                'is_approved' => true,
                'review_type' => 'tutor',
                'created_at' => $now->copy()->subDays(7),
                'updated_at' => $now->copy()->subDays(7),
            ],
            [
                'tutor_id' => $tutorIds['<EMAIL>'],
                'student_id' => $studentIds['<EMAIL>'],
                'session_id' => null,
                'rating' => 4,
                'comment' => 'Ms. Davis helped me improve my essay writing significantly. Very encouraging teacher!',
                'is_anonymous' => false,
                'is_approved' => true,
                'review_type' => 'tutor',
                'created_at' => $now->copy()->subDays(10),
                'updated_at' => $now->copy()->subDays(10),
            ],
        ];

        DB::table('tutor_reviews')->insert($reviews);
    }

    private function seedFavorites(Carbon $now): void
    {
        $tutorIds = DB::table('tutors')->join('users', 'tutors.user_id', '=', 'users.id')
                                      ->pluck('tutors.id', 'users.email');
        $studentIds = DB::table('students')->join('users', 'students.user_id', '=', 'users.id')
                                          ->pluck('students.id', 'users.email');

        $favorites = [
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(5),
                'updated_at' => $now->copy()->subDays(5),
            ],
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(3),
                'updated_at' => $now->copy()->subDays(3),
            ],
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(8),
                'updated_at' => $now->copy()->subDays(8),
            ],
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(2),
                'updated_at' => $now->copy()->subDays(2),
            ],
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(6),
                'updated_at' => $now->copy()->subDays(6),
            ],
            [
                'student_id' => $studentIds['<EMAIL>'],
                'tutor_id' => $tutorIds['<EMAIL>'],
                'created_at' => $now->copy()->subDays(4),
                'updated_at' => $now->copy()->subDays(4),
            ],
        ];

        DB::table('favorites')->insert($favorites);
    }

    private function seedNotifications(Carbon $now): void
    {
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $notifications = [
            [
                'user_id' => $studentUserIds['<EMAIL>'],
                'user_type' => 'student',
                'title' => 'Session Confirmed',
                'message' => 'Your calculus session with Dr. Sarah Johnson has been confirmed for tomorrow at 2:00 PM.',
                'type' => 'booking',
                'data' => json_encode(['session_id' => 1, 'tutor_name' => 'Dr. Sarah Johnson']),
                'is_read' => false,
                'read_at' => null,
                'created_at' => $now->copy()->subHours(2),
                'updated_at' => $now->copy()->subHours(2),
            ],
            [
                'user_id' => $tutorUserIds['<EMAIL>'],
                'user_type' => 'tutor',
                'title' => 'New Session Booking',
                'message' => 'John Smith has booked a session with you for tomorrow at 2:00 PM.',
                'type' => 'booking',
                'data' => json_encode(['session_id' => 1, 'student_name' => 'John Smith']),
                'is_read' => true,
                'read_at' => $now->copy()->subHours(1),
                'created_at' => $now->copy()->subHours(3),
                'updated_at' => $now->copy()->subHours(1),
            ],
            [
                'user_id' => $tutorUserIds['<EMAIL>'],
                'user_type' => 'tutor',
                'title' => 'New Review Received',
                'message' => 'You received a 5-star review from John Smith for your physics session.',
                'type' => 'review',
                'data' => json_encode(['review_id' => 1, 'rating' => 5, 'student_name' => 'John Smith']),
                'is_read' => false,
                'read_at' => null,
                'created_at' => $now->copy()->subDays(2),
                'updated_at' => $now->copy()->subDays(2),
            ],
            [
                'user_id' => $studentUserIds['<EMAIL>'],
                'user_type' => 'student',
                'title' => 'Session Reminder',
                'message' => 'Your English session with Ms. Emily Davis is scheduled for tomorrow at 10:00 AM.',
                'type' => 'booking',
                'data' => json_encode(['session_id' => 3, 'tutor_name' => 'Ms. Emily Davis']),
                'is_read' => false,
                'read_at' => null,
                'created_at' => $now->copy()->subHours(12),
                'updated_at' => $now->copy()->subHours(12),
            ],
            [
                'user_id' => $studentUserIds['<EMAIL>'],
                'user_type' => 'student',
                'title' => 'Session Completed',
                'message' => 'Your chemistry session with Dr. James Wilson has been completed. Please leave a review!',
                'type' => 'system',
                'data' => json_encode(['session_id' => 4, 'tutor_name' => 'Dr. James Wilson']),
                'is_read' => true,
                'read_at' => $now->copy()->subDays(1)->addHours(2),
                'created_at' => $now->copy()->subDays(1),
                'updated_at' => $now->copy()->subDays(1)->addHours(2),
            ],
        ];

        DB::table('notifications')->insert($notifications);
    }

    private function seedGroups(Carbon $now): void
    {
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');

        $groups = [
            [
                'name' => 'Mathematics Study Group',
                'description' => 'A group for students learning calculus, algebra, and advanced mathematics topics.',
                'creator_id' => $tutorUserIds['<EMAIL>'],
                'creator_type' => 'tutor',
                'subject' => 'Mathematics',
                'max_members' => 25,
                'is_active' => true,
                'group_type' => 'study',
                'image' => 'groups/math_study_group.jpg',
                'created_at' => $now->copy()->subDays(15),
                'updated_at' => $now->copy()->subDays(1),
            ],
            [
                'name' => 'Physics Discussion Forum',
                'description' => 'Discuss physics concepts, share resources, and collaborate on problem-solving.',
                'creator_id' => $tutorUserIds['<EMAIL>'],
                'creator_type' => 'tutor',
                'subject' => 'Physics',
                'max_members' => 30,
                'is_active' => true,
                'group_type' => 'discussion',
                'image' => 'groups/physics_discussion.jpg',
                'created_at' => $now->copy()->subDays(20),
                'updated_at' => $now->copy()->subDays(2),
            ],
            [
                'name' => 'Programming Bootcamp',
                'description' => 'Learn programming fundamentals, share code, and work on projects together.',
                'creator_id' => $tutorUserIds['<EMAIL>'],
                'creator_type' => 'tutor',
                'subject' => 'Programming',
                'max_members' => 20,
                'is_active' => true,
                'group_type' => 'study',
                'image' => 'groups/programming_bootcamp.jpg',
                'created_at' => $now->copy()->subDays(10),
                'updated_at' => $now->copy()->subHours(6),
            ],
            [
                'name' => 'English Writing Workshop',
                'description' => 'Improve your writing skills, share essays, and get feedback from peers.',
                'creator_id' => $tutorUserIds['<EMAIL>'],
                'creator_type' => 'tutor',
                'subject' => 'English',
                'max_members' => 15,
                'is_active' => true,
                'group_type' => 'study',
                'image' => 'groups/writing_workshop.jpg',
                'created_at' => $now->copy()->subDays(12),
                'updated_at' => $now->copy()->subDays(3),
            ],
        ];

        DB::table('groups')->insert($groups);
    }

    private function seedGroupMembers(): void
    {
        $groupIds = DB::table('groups')->pluck('id', 'name');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');
        $now = Carbon::now();

        $groupMembers = [
            // Mathematics Study Group members
            ['group_id' => $groupIds['Mathematics Study Group'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(10), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['Mathematics Study Group'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(8), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['Mathematics Study Group'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(5), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],

            // Physics Discussion Forum members
            ['group_id' => $groupIds['Physics Discussion Forum'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(15), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['Physics Discussion Forum'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(12), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],

            // Programming Bootcamp members
            ['group_id' => $groupIds['Programming Bootcamp'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(7), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['Programming Bootcamp'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(6), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],

            // English Writing Workshop members
            ['group_id' => $groupIds['English Writing Workshop'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(9), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['English Writing Workshop'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(7), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
            ['group_id' => $groupIds['English Writing Workshop'], 'user_id' => $studentUserIds['<EMAIL>'], 'role' => 'member', 'joined_at' => $now->copy()->subDays(4), 'is_active' => true, 'created_at' => $now, 'updated_at' => $now],
        ];

        DB::table('group_members')->insert($groupMembers);
    }

    private function seedGroupMessages(Carbon $now): void
    {
        $groupIds = DB::table('groups')->pluck('id', 'name');
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $groupMessages = [
            // Mathematics Study Group messages
            [
                'group_id' => $groupIds['Mathematics Study Group'],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Welcome to the Mathematics Study Group! Feel free to ask questions and share resources.',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(15),
                'updated_at' => $now->copy()->subDays(15),
            ],
            [
                'group_id' => $groupIds['Mathematics Study Group'],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'Thank you Dr. Johnson! I have a question about derivatives.',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(10),
                'updated_at' => $now->copy()->subDays(10),
            ],
            [
                'group_id' => $groupIds['Mathematics Study Group'],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'Can someone help me with integration by parts?',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(5),
                'updated_at' => $now->copy()->subDays(5),
            ],
            // Programming Bootcamp messages
            [
                'group_id' => $groupIds['Programming Bootcamp'],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Today we\'ll be covering JavaScript fundamentals. Please review the materials I shared.',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(7),
                'updated_at' => $now->copy()->subDays(7),
            ],
            [
                'group_id' => $groupIds['Programming Bootcamp'],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'I\'m having trouble with async/await. Could you explain it again?',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(3),
                'updated_at' => $now->copy()->subDays(3),
            ],
            // English Writing Workshop messages
            [
                'group_id' => $groupIds['English Writing Workshop'],
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'message' => 'Remember to focus on your thesis statement - it should be clear and arguable.',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(8),
                'updated_at' => $now->copy()->subDays(8),
            ],
            [
                'group_id' => $groupIds['English Writing Workshop'],
                'sender_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'message' => 'I\'ve uploaded my essay draft. Could everyone please provide feedback?',
                'message_type' => 'text',
                'created_at' => $now->copy()->subDays(4),
                'updated_at' => $now->copy()->subDays(4),
            ],
        ];

        DB::table('group_messages')->insert($groupMessages);
    }

    private function seedConversations(Carbon $now): void
    {
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $conversations = [
            [
                'user1_id' => $studentUserIds['<EMAIL>'],
                'user2_id' => $tutorUserIds['<EMAIL>'],
                'last_message_at' => $now->copy()->subHours(1),
                'created_at' => $now->copy()->subDays(5),
                'updated_at' => $now->copy()->subHours(1),
            ],
            [
                'user1_id' => $studentUserIds['<EMAIL>'],
                'user2_id' => $tutorUserIds['<EMAIL>'],
                'last_message_at' => $now->copy()->subHours(6),
                'created_at' => $now->copy()->subDays(8),
                'updated_at' => $now->copy()->subHours(6),
            ],
            [
                'user1_id' => $studentUserIds['<EMAIL>'],
                'user2_id' => $tutorUserIds['<EMAIL>'],
                'last_message_at' => $now->copy()->subDays(1),
                'created_at' => $now->copy()->subDays(3),
                'updated_at' => $now->copy()->subDays(1),
            ],
            [
                'user1_id' => $studentUserIds['<EMAIL>'],
                'user2_id' => $tutorUserIds['<EMAIL>'],
                'last_message_at' => $now->copy()->subDays(2),
                'created_at' => $now->copy()->subDays(6),
                'updated_at' => $now->copy()->subDays(2),
            ],
        ];

        DB::table('conversations')->insert($conversations);
    }

    private function seedMessages(Carbon $now): void
    {
        $tutorUserIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');
        $studentUserIds = DB::table('users')->where('user_type', 'student')->pluck('id', 'email');

        $messages = [
            // Conversation 1: John Smith & Sarah Johnson
            [
                'sender_id' => $studentUserIds['<EMAIL>'],
                'receiver_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'receiver_type' => 'tutor',
                'message' => 'Hi Dr. Johnson, I need help with calculus derivatives.',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subDays(5)->addHours(1),
                'created_at' => $now->copy()->subDays(5),
                'updated_at' => $now->copy()->subDays(5)->addHours(1),
            ],
            [
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'receiver_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'receiver_type' => 'student',
                'message' => 'Hello John! I\'d be happy to help you with derivatives. When would you like to schedule a session?',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subDays(5)->addHours(2),
                'created_at' => $now->copy()->subDays(5)->addHours(1),
                'updated_at' => $now->copy()->subDays(5)->addHours(2),
            ],
            [
                'sender_id' => $studentUserIds['<EMAIL>'],
                'receiver_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'receiver_type' => 'tutor',
                'message' => 'How about tomorrow at 2 PM? I\'m available then.',
                'message_type' => 'text',
                'is_read' => false,
                'read_at' => null,
                'created_at' => $now->copy()->subHours(1),
                'updated_at' => $now->copy()->subHours(1),
            ],
            // Conversation 2: Emma Johnson & Emily Davis
            [
                'sender_id' => $studentUserIds['<EMAIL>'],
                'receiver_id' => $tutorUserIds['<EMAIL>'],
                'sender_type' => 'student',
                'receiver_type' => 'tutor',
                'message' => 'Ms. Davis, could you help me with essay structure for my English assignment?',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subDays(8)->addHours(2),
                'created_at' => $now->copy()->subDays(8),
                'updated_at' => $now->copy()->subDays(8)->addHours(2),
            ],
            [
                'sender_id' => $tutorUserIds['<EMAIL>'],
                'receiver_id' => $studentUserIds['<EMAIL>'],
                'sender_type' => 'tutor',
                'receiver_type' => 'student',
                'message' => 'Of course, Emma! Let\'s work on creating a strong thesis statement and organizing your ideas.',
                'message_type' => 'text',
                'is_read' => true,
                'read_at' => $now->copy()->subHours(6),
                'created_at' => $now->copy()->subHours(6),
                'updated_at' => $now->copy()->subHours(6),
            ],
        ];

        DB::table('messages')->insert($messages);
    }
}
