<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return response()->json([
        'message' => 'Tutor Finder API',
        'version' => '1.0.0',
        'status' => 'active',
        'endpoints' => [
            'api' => url('/api/v1'),
            'health' => url('/api/health'),
            'docs' => url('/api/docs'),
        ]
    ]);
});

Route::get('/docs', function () {
    return response()->json([
        'message' => 'API Documentation',
        'base_url' => url('/api/v1'),
        'authentication' => 'Bearer Token (Laravel Sanctum)',
        'endpoints' => [
            'auth' => [
                'POST /auth/register/student' => 'Register as student',
                'POST /auth/register/tutor' => 'Register as tutor',
                'POST /auth/login' => 'Login user',
                'POST /auth/logout' => 'Logout user (protected)',
                'GET /auth/profile' => 'Get user profile (protected)',
                'PUT /auth/profile' => 'Update user profile (protected)',
            ],
            'tutors' => [
                'GET /tutors' => 'Get all tutors with filters',
                'GET /tutors/{id}' => 'Get specific tutor',
                'GET /tutors/featured' => 'Get featured tutors',
                'GET /tutors/search' => 'Search tutors',
            ],
            'categories' => [
                'GET /categories' => 'Get all categories',
                'GET /categories/{id}' => 'Get specific category',
                'GET /categories/popular' => 'Get popular categories',
            ],
            'favorites' => [
                'GET /favorites' => 'Get user favorites (protected)',
                'POST /favorites' => 'Add to favorites (protected)',
                'DELETE /favorites/{tutorId}' => 'Remove from favorites (protected)',
            ],
            'messages' => [
                'GET /messages/conversations' => 'Get conversations (protected)',
                'POST /messages/send' => 'Send message (protected)',
                'GET /messages/unread-count' => 'Get unread count (protected)',
            ]
        ]
    ]);
});
