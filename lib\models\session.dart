import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'session.g.dart';

@JsonSerializable()
class Session {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'tutor_id')
  final int tutorId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'student_id')
  final int studentId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'session_date')
  final String sessionDate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'duration_minutes')
  final int durationMinutes;
  final String price;
  final String status;
  final String? notes;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'meeting_link')
  final String? meetingLink;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'session_type')
  final String sessionType;
  final String? location;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'started_at')
  final String? startedAt;
  @J<PERSON><PERSON><PERSON>(name: 'ended_at')
  final String? endedAt;
  @J<PERSON><PERSON><PERSON>(name: 'cancellation_reason')
  final String? cancellationReason;
  final Tutor? tutor;
  final Student? student;
  final List<Chat>? chats;
  final TutorReview? review;
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  Session({
    required this.id,
    required this.tutorId,
    required this.studentId,
    required this.sessionDate,
    required this.durationMinutes,
    required this.price,
    required this.status,
    this.notes,
    this.meetingLink,
    required this.sessionType,
    this.location,
    this.startedAt,
    this.endedAt,
    this.cancellationReason,
    this.tutor,
    this.student,
    this.chats,
    this.review,
    this.createdAt,
    this.updatedAt,
  });

  factory Session.fromJson(Map<String, dynamic> json) => _$SessionFromJson(json);
  Map<String, dynamic> toJson() => _$SessionToJson(this);

  // Helper methods
  double get priceValue => double.tryParse(price) ?? 0.0;
  double get durationHours => durationMinutes / 60.0;
  bool get isOnline => sessionType == 'online';
  bool get isInPerson => sessionType == 'in_person';
  bool get isBooked => status == 'booked';
  bool get isConfirmed => status == 'confirmed';
  bool get isInProgress => status == 'in_progress';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isNoShow => status == 'no_show';
  bool get canBeCancelled => isBooked || isConfirmed;
  bool get canBeStarted => isConfirmed;
  bool get canBeCompleted => isInProgress;
  bool get canBeReviewed => isCompleted && review == null;
  bool get hasReview => review != null;

  DateTime? get sessionDateTime {
    try {
      return DateTime.parse(sessionDate);
    } catch (e) {
      return null;
    }
  }

  String get statusDisplayText {
    switch (status) {
      case 'booked':
        return 'Booked';
      case 'confirmed':
        return 'Confirmed';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'no_show':
        return 'No Show';
      default:
        return status;
    }
  }

  Session copyWith({
    int? id,
    int? tutorId,
    int? studentId,
    String? sessionDate,
    int? durationMinutes,
    String? price,
    String? status,
    String? notes,
    String? meetingLink,
    String? sessionType,
    String? location,
    String? startedAt,
    String? endedAt,
    String? cancellationReason,
    Tutor? tutor,
    Student? student,
    List<Chat>? chats,
    TutorReview? review,
    String? createdAt,
    String? updatedAt,
  }) {
    return Session(
      id: id ?? this.id,
      tutorId: tutorId ?? this.tutorId,
      studentId: studentId ?? this.studentId,
      sessionDate: sessionDate ?? this.sessionDate,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      price: price ?? this.price,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      meetingLink: meetingLink ?? this.meetingLink,
      sessionType: sessionType ?? this.sessionType,
      location: location ?? this.location,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      tutor: tutor ?? this.tutor,
      student: student ?? this.student,
      chats: chats ?? this.chats,
      review: review ?? this.review,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class Chat {
  final int id;
  @JsonKey(name: 'session_id')
  final int sessionId;
  @JsonKey(name: 'sender_id')
  final int senderId;
  @JsonKey(name: 'sender_type')
  final String senderType;
  final String message;
  @JsonKey(name: 'message_type')
  final String messageType;
  @JsonKey(name: 'file_path')
  final String? filePath;
  @JsonKey(name: 'is_read')
  final bool isRead;
  @JsonKey(name: 'read_at')
  final String? readAt;
  @JsonKey(name: 'created_at')
  final String? createdAt;
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  Chat({
    required this.id,
    required this.sessionId,
    required this.senderId,
    required this.senderType,
    required this.message,
    required this.messageType,
    this.filePath,
    required this.isRead,
    this.readAt,
    this.createdAt,
    this.updatedAt,
  });

  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);
  Map<String, dynamic> toJson() => _$ChatToJson(this);

  // Helper methods
  bool get isFromStudent => senderType == 'student';
  bool get isFromTutor => senderType == 'tutor';
  bool get isTextMessage => messageType == 'text';
  bool get isImageMessage => messageType == 'image';
  bool get isFileMessage => messageType == 'file';
  bool get isSystemMessage => messageType == 'system';
  bool get hasFile => filePath != null && filePath!.isNotEmpty;

  DateTime? get createdDateTime {
    try {
      return createdAt != null ? DateTime.parse(createdAt!) : null;
    } catch (e) {
      return null;
    }
  }

  Chat copyWith({
    int? id,
    int? sessionId,
    int? senderId,
    String? senderType,
    String? message,
    String? messageType,
    String? filePath,
    bool? isRead,
    String? readAt,
    String? createdAt,
    String? updatedAt,
  }) {
    return Chat(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      senderId: senderId ?? this.senderId,
      senderType: senderType ?? this.senderType,
      message: message ?? this.message,
      messageType: messageType ?? this.messageType,
      filePath: filePath ?? this.filePath,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
