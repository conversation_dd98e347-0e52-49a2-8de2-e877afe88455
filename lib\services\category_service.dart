import '../models/api_response.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class CategoryService {
  static final CategoryService _instance = CategoryService._internal();
  factory CategoryService() => _instance;
  CategoryService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get all categories with tutor counts
  Future<ApiResponse<List<TutorCategory>>> getCategories() async {
    return await _apiClient.get<List<TutorCategory>>(
      ApiConfig.categoriesList,
      fromJson: (data) => (data as List)
          .map((item) => TutorCategory.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Get popular categories
  Future<ApiResponse<List<TutorCategory>>> getPopularCategories() async {
    return await _apiClient.get<List<TutorCategory>>(
      ApiConfig.popularCategories,
      fromJson: (data) => (data as List)
          .map((item) => TutorCategory.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Get specific category with its tutors
  Future<ApiResponse<Map<String, dynamic>>> getCategoryById(int id) async {
    return await _apiClient.get<Map<String, dynamic>>(
      ApiConfig.categoryDetails(id),
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  // Search categories by name
  List<TutorCategory> searchCategories(List<TutorCategory> categories, String query) {
    if (query.isEmpty) return categories;

    final lowerQuery = query.toLowerCase();
    return categories.where((category) {
      return category.name.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // Get categories with most tutors
  List<TutorCategory> getCategoriesWithMostTutors(List<TutorCategory> categories) {
    final categoriesWithCounts = categories.where((cat) => (cat.tutorCount ?? 0) > 0).toList();
    categoriesWithCounts.sort((a, b) => (b.tutorCount ?? 0).compareTo(a.tutorCount ?? 0));
    return categoriesWithCounts;
  }

  // Get category statistics
  Map<String, dynamic> getCategoryStats(List<TutorCategory> categories) {
    if (categories.isEmpty) {
      return {
        'total': 0,
        'withTutors': 0,
        'totalTutors': 0,
        'averageTutorsPerCategory': 0.0,
      };
    }

    final categoriesWithTutors = categories.where((cat) => (cat.tutorCount ?? 0) > 0).length;
    final totalTutors = categories.fold<int>(0, (sum, cat) => sum + (cat.tutorCount ?? 0));

    return {
      'total': categories.length,
      'withTutors': categoriesWithTutors,
      'totalTutors': totalTutors,
      'averageTutorsPerCategory': categoriesWithTutors > 0 ? totalTutors / categoriesWithTutors : 0.0,
    };
  }

  // Group categories alphabetically
  Map<String, List<TutorCategory>> groupCategoriesAlphabetically(List<TutorCategory> categories) {
    final grouped = <String, List<TutorCategory>>{};

    for (final category in categories) {
      final firstLetter = category.name.isNotEmpty
          ? category.name[0].toUpperCase()
          : '#';

      if (!grouped.containsKey(firstLetter)) {
        grouped[firstLetter] = [];
      }
      grouped[firstLetter]!.add(category);
    }

    // Sort categories within each group
    for (final key in grouped.keys) {
      grouped[key]!.sort((a, b) => a.name.compareTo(b.name));
    }

    return grouped;
  }

  // Get recommended categories based on popular subjects
  List<TutorCategory> getRecommendedCategories(List<TutorCategory> categories) {
    final recommendedNames = [
      'Mathematics',
      'English',
      'Physics',
      'Chemistry',
      'Biology',
      'Computer Science',
      'Programming',
      'Languages',
    ];

    final recommended = <TutorCategory>[];

    for (final name in recommendedNames) {
      final category = categories.firstWhere(
        (cat) => cat.name.toLowerCase().contains(name.toLowerCase()),
        orElse: () => TutorCategory(id: -1, name: ''),
      );

      if (category.id != -1) {
        recommended.add(category);
      }
    }

    return recommended;
  }

  // Filter categories by minimum tutor count
  List<TutorCategory> filterCategoriesByTutorCount(List<TutorCategory> categories, int minCount) {
    return categories.where((cat) => (cat.tutorCount ?? 0) >= minCount).toList();
  }
}
