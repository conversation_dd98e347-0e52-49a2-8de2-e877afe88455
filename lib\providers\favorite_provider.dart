import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/favorite.dart';
import '../services/favorite_service.dart';

enum FavoriteState {
  initial,
  loading,
  loaded,
  error,
}

class FavoriteProvider extends ChangeNotifier {
  final FavoriteService _favoriteService = FavoriteService();
  
  FavoriteState _state = FavoriteState.initial;
  String? _errorMessage;
  List<Favorite> _favorites = [];
  Set<int> _favoriteTutorIds = {};
  PaginatedResponse<Favorite>? _paginatedFavorites;
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  int _favoritesCount = 0;

  // Getters
  FavoriteState get state => _state;
  String? get errorMessage => _errorMessage;
  List<Favorite> get favorites => _favorites;
  Set<int> get favoriteTutorIds => _favoriteTutorIds;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  int get currentPage => _currentPage;
  int get totalFavorites => _paginatedFavorites?.total ?? 0;
  int get favoritesCount => _favoritesCount;

  // Check if tutor is favorited
  bool isFavorited(int tutorId) {
    return _favoriteTutorIds.contains(tutorId);
  }

  // Load favorites with pagination
  Future<void> loadFavorites({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _favorites.clear();
      _favoriteTutorIds.clear();
    }

    if (!_hasMore && !refresh) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _favoriteService.getFavorites(
        page: _currentPage,
        perPage: 15,
      );

      if (response.isSuccess && response.data != null) {
        _paginatedFavorites = response.data!;
        
        if (refresh) {
          _favorites = response.data!.data;
        } else {
          _favorites.addAll(response.data!.data);
        }

        // Update favorite tutor IDs set
        for (final favorite in response.data!.data) {
          _favoriteTutorIds.add(favorite.tutorId);
        }

        _hasMore = response.data!.hasNextPage;
        if (_hasMore) {
          _currentPage++;
        }

        _setState(FavoriteState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load favorites');
      }
    } catch (e) {
      _setError('Failed to load favorites: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add tutor to favorites
  Future<bool> addToFavorites(int tutorId) async {
    if (_favoriteTutorIds.contains(tutorId)) {
      return true; // Already favorited
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await _favoriteService.addToFavorites(tutorId);

      if (response.isSuccess && response.data != null) {
        _favorites.insert(0, response.data!);
        _favoriteTutorIds.add(tutorId);
        _favoritesCount++;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to add to favorites');
        return false;
      }
    } catch (e) {
      _setError('Failed to add to favorites: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Remove tutor from favorites
  Future<bool> removeFromFavorites(int tutorId) async {
    if (!_favoriteTutorIds.contains(tutorId)) {
      return true; // Not favorited
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await _favoriteService.removeFromFavorites(tutorId);

      if (response.isSuccess) {
        _favorites.removeWhere((favorite) => favorite.tutorId == tutorId);
        _favoriteTutorIds.remove(tutorId);
        _favoritesCount--;
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to remove from favorites');
        return false;
      }
    } catch (e) {
      _setError('Failed to remove from favorites: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Toggle favorite status
  Future<bool> toggleFavorite(int tutorId) async {
    if (_favoriteTutorIds.contains(tutorId)) {
      return await removeFromFavorites(tutorId);
    } else {
      return await addToFavorites(tutorId);
    }
  }

  // Load favorites count
  Future<void> loadFavoritesCount() async {
    try {
      final response = await _favoriteService.getFavoritesCount();
      if (response.isSuccess && response.data != null) {
        _favoritesCount = response.data!.favoritesCount;
        notifyListeners();
      }
    } catch (e) {
      print('Error loading favorites count: $e');
    }
  }

  // Load favorite tutor IDs for quick checking
  Future<void> loadFavoriteTutorIds() async {
    try {
      final tutorIds = await _favoriteService.getFavoriteTutorIds();
      _favoriteTutorIds = Set.from(tutorIds);
      notifyListeners();
    } catch (e) {
      print('Error loading favorite tutor IDs: $e');
    }
  }

  // Check multiple favorites
  Future<Map<int, bool>> checkMultipleFavorites(List<int> tutorIds) async {
    try {
      return await _favoriteService.checkMultipleFavorites(tutorIds);
    } catch (e) {
      print('Error checking multiple favorites: $e');
      return {};
    }
  }

  // Clear all favorites
  Future<bool> clearAllFavorites() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _favoriteService.clearAllFavorites();

      if (response.isSuccess) {
        _favorites.clear();
        _favoriteTutorIds.clear();
        _favoritesCount = 0;
        _setState(FavoriteState.loaded);
        return true;
      } else {
        _setError(response.message ?? 'Failed to clear favorites');
        return false;
      }
    } catch (e) {
      _setError('Failed to clear favorites: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear error
  void clearError() {
    _clearError();
  }

  // Refresh favorites
  Future<void> refresh() async {
    await Future.wait([
      loadFavorites(refresh: true),
      loadFavoritesCount(),
    ]);
  }

  // Initialize
  Future<void> initialize() async {
    await Future.wait([
      loadFavorites(refresh: true),
      loadFavoriteTutorIds(),
      loadFavoritesCount(),
    ]);
  }

  // Private methods
  void _setState(FavoriteState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = FavoriteState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == FavoriteState.error) {
      _state = _favorites.isNotEmpty ? FavoriteState.loaded : FavoriteState.initial;
    }
    notifyListeners();
  }

  // Helper methods
  bool hasFavorites() {
    return _favorites.isNotEmpty;
  }

  Favorite? getFavoriteByTutorId(int tutorId) {
    try {
      return _favorites.firstWhere((favorite) => favorite.tutorId == tutorId);
    } catch (e) {
      return null;
    }
  }
}
