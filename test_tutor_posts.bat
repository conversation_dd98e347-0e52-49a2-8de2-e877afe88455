@echo off
echo ========================================
echo    Testing TutorPost Controller
echo ========================================
echo.

echo [1/5] Testing posts listing...
curl -s "http://127.0.0.1:8000/api/v1/posts" | head -10
echo.
echo.

echo [2/5] Testing featured posts...
curl -s "http://127.0.0.1:8000/api/v1/posts/featured" | head -10
echo.
echo.

echo [3/5] Testing posts by tutor...
curl -s "http://127.0.0.1:8000/api/v1/posts/tutor/1" | head -10
echo.
echo.

echo [4/5] Testing health endpoint...
curl -s "http://127.0.0.1:8000/api/health"
echo.
echo.

echo [5/5] Testing tutor filter options...
curl -s "http://127.0.0.1:8000/api/v1/tutors/filter-options" | head -20
echo.
echo.

echo ========================================
echo    TutorPost Controller Test Complete
echo ========================================
echo.
echo If you see JSON responses above, the controller is working!
echo If you see errors, make sure to:
echo 1. Start Laravel server: php artisan serve
echo 2. Run migrations: php artisan migrate
echo 3. Check database connection in .env file
