import 'package:flutter/material.dart';
import '../Screens/splash_screen.dart';
import '../Screens/login_screen.dart';
import '../Screens/register_screen.dart';
import '../Screens/main_screen.dart';
import '../Screens/tutor_search_screen.dart';
import '../Screens/subject_details_screen.dart';
import '../Screens/chat_screen.dart';
import '../Screens/conversations_screen.dart';
import '../Screens/notifications_screen.dart';
import '../Screens/tutor_dashboard_screen.dart';
import '../Screens/profile_edit_screen.dart';
import '../Screens/my_sessions_screen.dart';
import '../Screens/More_Screen/payment_methods_screen.dart';
import '../Screens/transaction_history_screen.dart';
import '../Screens/settings_screen.dart';
import '../Screens/help_center_screen.dart';


class AppRoute {
  static const String splashScreen = "/";
  static const String loginScreen = "/login";
  static const String registerScreen = "/register";
  static const String mainScreen = "/main";
  static const String tutorSearchScreen = "/tutor-search";
  static const String subjectDetailsScreen = "/subject-details";
  static const String chatScreen = "/chat";
  static const String conversationsScreen = "/conversations";
  static const String notificationsScreen = "/notifications";
  static const String tutorDashboardScreen = "/tutor-dashboard";
  static const String profileEditScreen = "/profile-edit";
  static const String mySessionsScreen = "/my-sessions";
  static const String paymentMethodsScreen = "/payment-methods";
  static const String transactionHistoryScreen = "/transaction-history";
  static const String settingsScreen = "/settings";
  static const String helpCenterScreen = "/help-center";

  static final key = GlobalKey<NavigatorState>();

  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    // Debug print to help identify routing issues
    debugPrint('Navigating to route: ${routeSettings.name}');
    debugPrint('Route arguments: ${routeSettings.arguments}');

    switch (routeSettings.name) {
      case splashScreen:
        return _buildRoute(routeSettings, const SplashScreen());
      case loginScreen:
        return _buildRoute(routeSettings, const LoginnScreen());
      case registerScreen:
        return _buildRoute(routeSettings, const RegisterScreen());
      case mainScreen:
        return _buildRoute(routeSettings, const MainScreen());
      case tutorSearchScreen:
        return _buildRoute(routeSettings, const TutorSearchScreen());
      case conversationsScreen:
        return _buildRoute(routeSettings, const ConversationsScreen());
      case notificationsScreen:
        return _buildRoute(routeSettings, const NotiScreen());
      case tutorDashboardScreen:
        return _buildRoute(routeSettings, const TutorDashboardScreen());
      case profileEditScreen:
        return _buildRoute(routeSettings, const ProfileEditScreen());
      case mySessionsScreen:
        return _buildRoute(routeSettings, const MySessionsScreen());
      case paymentMethodsScreen:
        return _buildRoute(routeSettings, const PaymentMethodsScreen());
      case transactionHistoryScreen:
        return _buildRoute(routeSettings, const TransactionHistoryScreen());
      case settingsScreen:
        return _buildRoute(routeSettings, const SettingsScreen());
      case helpCenterScreen:
        return _buildRoute(routeSettings, const HelpCenterScreen());

      // Dynamic routes with parameters
      case subjectDetailsScreen:
        final args = routeSettings.arguments as Map<String, dynamic>?;
        return _buildRoute(
          routeSettings,
          SubjectDetailScreen(
            subjectName: args?['subjectName'] ?? 'Unknown Subject',
            subjectId: args?['subjectId'],
          ),
        );
      case chatScreen:
        final args = routeSettings.arguments as Map<String, dynamic>?;
        return _buildRoute(
          routeSettings,
          ChatScreen(
            otherUserId: args?['otherUserId'] ?? 0,
            otherUserType: args?['otherUserType'] ?? 'user',
            otherUserName: args?['otherUserName'] ?? 'User',
            otherUserAvatar: args?['otherUserAvatar'],
          ),
        );

      default:
        // Return a default error screen instead of throwing exception
        return _buildRoute(
          routeSettings,
          _buildErrorScreen("Route not found: ${routeSettings.name}")
        );
    }
  }

  static Route<dynamic> _buildRoute(
      RouteSettings routeSettings, Widget newScreen) {
    return MaterialPageRoute(
        settings: routeSettings, builder: (BuildContext context) => newScreen);
  }

  static Widget _buildErrorScreen(String message) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Navigation Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => navigateToMain(),
                child: const Text('Go to Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for navigation
  static void navigateToLogin() {
    key.currentState?.pushNamed(loginScreen);
  }

  static void navigateToRegister() {
    key.currentState?.pushNamed(registerScreen);
  }

  static void navigateToMain() {
    key.currentState?.pushNamedAndRemoveUntil(
      mainScreen,
      (Route<dynamic> route) => false,
    );
  }

  static void navigateToTutorSearch() {
    key.currentState?.pushNamed(tutorSearchScreen);
  }

  static void navigateToSubjectDetails(String subjectName, {int? subjectId}) {
    key.currentState?.pushNamed(
      subjectDetailsScreen,
      arguments: {
        'subjectName': subjectName,
        'subjectId': subjectId,
      },
    );
  }

  static void navigateToChat({
    required int otherUserId,
    required String otherUserType,
    required String otherUserName,
    String? otherUserAvatar,
  }) {
    key.currentState?.pushNamed(
      chatScreen,
      arguments: {
        'otherUserId': otherUserId,
        'otherUserType': otherUserType,
        'otherUserName': otherUserName,
        'otherUserAvatar': otherUserAvatar,
      },
    );
  }

  static void navigateToConversations() {
    key.currentState?.pushNamed(conversationsScreen);
  }

  static void navigateToNotifications() {
    key.currentState?.pushNamed(notificationsScreen);
  }

  static void navigateToTutorDashboard() {
    key.currentState?.pushNamed(tutorDashboardScreen);
  }

  static void navigateBack() {
    if (key.currentState?.canPop() == true) {
      key.currentState?.pop();
    }
  }

  static void logout() {
    key.currentState?.pushNamedAndRemoveUntil(
      splashScreen,
      (Route<dynamic> route) => false,
    );
  }
}

class RouteException implements Exception {
  String message;
  RouteException(this.message);
}
