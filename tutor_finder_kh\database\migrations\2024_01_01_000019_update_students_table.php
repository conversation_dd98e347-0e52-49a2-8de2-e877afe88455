<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Add user_id foreign key if it doesn't exist
            if (!Schema::hasColumn('students', 'user_id')) {
                $table->foreignId('user_id')->after('id')->constrained('users')->onDelete('cascade');
            }
            
            // Add new fields for student profile
            if (!Schema::hasColumn('students', 'grade_level')) {
                $table->string('grade_level')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('students', 'school')) {
                $table->string('school')->nullable()->after('grade_level');
            }
            if (!Schema::hasColumn('students', 'learning_preferences')) {
                $table->json('learning_preferences')->nullable()->after('school');
            }
            if (!Schema::hasColumn('students', 'subjects_of_interest')) {
                $table->json('subjects_of_interest')->nullable()->after('learning_preferences');
            }
            if (!Schema::hasColumn('students', 'preferred_learning_style')) {
                $table->string('preferred_learning_style')->nullable()->after('subjects_of_interest');
            }
            if (!Schema::hasColumn('students', 'goals')) {
                $table->text('goals')->nullable()->after('preferred_learning_style');
            }
            if (!Schema::hasColumn('students', 'availability')) {
                $table->json('availability')->nullable()->after('goals');
            }
            if (!Schema::hasColumn('students', 'budget_range')) {
                $table->string('budget_range')->nullable()->after('availability');
            }
            
            // Add indexes for better performance
            $table->index('user_id');
            $table->index('grade_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('students', function (Blueprint $table) {
            $table->dropColumn([
                'grade_level',
                'school',
                'learning_preferences',
                'subjects_of_interest',
                'preferred_learning_style',
                'goals',
                'availability',
                'budget_range'
            ]);
            
            if (Schema::hasColumn('students', 'user_id')) {
                $table->dropForeign(['user_id']);
                $table->dropColumn('user_id');
            }
        });
    }
};
