<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Tutor;
use App\Models\User;
use App\Services\TutorFilterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TutorController extends Controller
{
    /**
     * Get all tutors with comprehensive filtering for students
     */
    public function index(Request $request)
    {
        try {
            $query = Tutor::with(['user', 'categories']);

            // Apply all filters using the service
            TutorFilterService::applyFilters($query, $request);

            // Apply sorting using the service
            TutorFilterService::applySorting($query, $request);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $tutors = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                    'from' => $tutors->firstItem(),
                    'to' => $tutors->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tutors',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific tutor
     */
    public function show($id)
    {
        try {
            $tutor = Tutor::with(['user'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $tutor
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Advanced search for tutors with comprehensive filtering
     */
    public function search(Request $request)
    {
        try {
            $query = Tutor::with(['user', 'categories', 'reviews']);

            // Text search
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->whereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'LIKE', "%{$searchTerm}%");
                    })
                    ->orWhere('bio', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('specializations', 'LIKE', "%{$searchTerm}%")
                    ->orWhereHas('categories', function ($catQuery) use ($searchTerm) {
                        $catQuery->where('name', 'LIKE', "%{$searchTerm}%");
                    });
                });
            }

            // Apply existing filters using the service
            TutorFilterService::applyFilters($query, $request);

            // Apply sorting using the service
            TutorFilterService::applySorting($query, $request);

            // Pagination
            $perPage = $request->get('per_page', 20);
            $tutors = $query->paginate($perPage);

            // Transform the data for the mobile app
            $transformedTutors = collect($tutors->items())->map(function ($tutor) {
                return [
                    'id' => $tutor->id,
                    'name' => $tutor->user->name ?? 'Unknown',
                    'subject' => $tutor->categories->pluck('name')->join(', ') ?: 'General',
                    'location' => $tutor->location ?? 'Not specified',
                    'major' => $tutor->education ?? 'Not specified',
                    'grade_level' => $tutor->grade_levels ?? 'All levels',
                    'rating' => (float) ($tutor->rating ?? 0),
                    'reviews_count' => $tutor->reviews_count ?? 0,
                    'hourly_rate' => (int) ($tutor->hourly_rate ?? 0),
                    'profile_image' => $tutor->profile_image ?? null,
                    'experience' => $tutor->experience ?? 'Not specified',
                    'is_online' => (bool) ($tutor->is_online ?? false),
                    'is_offline' => (bool) ($tutor->is_offline ?? true),
                    'is_verified' => (bool) ($tutor->is_verified ?? false),
                    'is_available_now' => (bool) ($tutor->is_available ?? false),
                    'bio' => $tutor->bio ?? 'No description available',
                    'languages' => $tutor->languages ? explode(',', $tutor->languages) : ['Khmer'],
                    'availability' => $tutor->availability ?? 'Contact for availability',
                    'specializations' => $tutor->specializations ? explode(',', $tutor->specializations) : [],
                    'education' => $tutor->education ?? 'Not specified',
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedTutors,
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ],
                'filters_applied' => $request->all(),
                'message' => 'Search completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search tutors by subject
     */
    public function searchBySubject(Request $request)
    {
        $request->validate([
            'subject' => 'required|string',
        ]);

        try {
            $subject = $request->subject;
            
            $tutors = Tutor::with(['user'])
                ->where('subjects', 'LIKE', '%' . $subject . '%')
                ->where('is_available', true)
                ->orderBy('hourly_rate', 'asc')
                ->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get featured tutors
     */
    public function featured()
    {
        try {
            $tutors = Tutor::with(['user'])
                ->where('is_verified', true)
                ->where('is_available', true)
                ->where('rating', '>=', 4.0)
                ->orderBy('rating', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $tutors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch featured tutors',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tutor statistics
     */
    public function statistics($id)
    {
        try {
            $tutor = Tutor::findOrFail($id);
            
            $stats = [
                'total_students' => 0, // You can implement this based on your booking/session system
                'total_sessions' => 0, // You can implement this based on your session system
                'average_rating' => $tutor->rating,
                'total_reviews' => $tutor->reviews_count,
                'subjects_count' => count(json_decode($tutor->subjects, true) ?? []),
                'experience_years' => $tutor->experience_years,
                'hourly_rate' => $tutor->hourly_rate,
                'is_verified' => $tutor->is_verified,
                'is_available' => $tutor->is_available,
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tutor statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update tutor availability
     */
    public function updateAvailability(Request $request, $id)
    {
        $request->validate([
            'is_available' => 'required|boolean',
        ]);

        try {
            $tutor = Tutor::findOrFail($id);
            
            // Check if the authenticated user owns this tutor profile
            if ($request->user()->id !== $tutor->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $tutor->update([
                'is_available' => $request->is_available
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Availability updated successfully',
                'data' => $tutor
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update availability',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tutors by location (if you have location data)
     */
    public function byLocation(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'nullable|numeric|min:1|max:100', // radius in km
        ]);

        try {
            $latitude = $request->latitude;
            $longitude = $request->longitude;
            $radius = $request->get('radius', 10); // default 10km

            // This assumes you have latitude and longitude columns in your tutors table
            // You might need to add these columns or implement location differently
            $tutors = Tutor::with(['user'])
                ->selectRaw("
                    *, 
                    (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
                ", [$latitude, $longitude, $latitude])
                ->having('distance', '<=', $radius)
                ->where('is_available', true)
                ->orderBy('distance', 'asc')
                ->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Location search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tutors by specific subject with advanced filtering
     */
    public function bySubject(Request $request, $subject)
    {
        try {
            $query = Tutor::with(['user', 'categories'])
                ->whereHas('user', function ($q) {
                    $q->where('user_type', 'tutor')->where('is_active', true);
                })
                ->where(function ($q) use ($subject) {
                    $q->whereJsonContains('subjects', $subject)
                      ->orWhere('subjects', 'LIKE', '%' . $subject . '%');
                });

            // Apply additional filters
            $this->applyCommonFilters($query, $request);

            $tutors = $query->orderBy('rating', 'desc')->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tutors by subject',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get online tutors only
     */
    public function onlineTutors(Request $request)
    {
        try {
            $query = Tutor::with(['user', 'categories'])
                ->whereHas('user', function ($q) {
                    $q->where('user_type', 'tutor')->where('is_active', true);
                })
                ->where(function ($q) {
                    $q->whereJsonContains('availability', ['online' => true])
                      ->orWhere('teaching_style', 'LIKE', '%online%');
                });

            $this->applyCommonFilters($query, $request);

            $tutors = $query->orderBy('rating', 'desc')->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch online tutors',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offline/in-person tutors only
     */
    public function offlineTutors(Request $request)
    {
        try {
            $query = Tutor::with(['user', 'categories'])
                ->whereHas('user', function ($q) {
                    $q->where('user_type', 'tutor')->where('is_active', true);
                })
                ->where(function ($q) {
                    $q->whereJsonContains('availability', ['in_person' => true])
                      ->orWhere('teaching_style', 'LIKE', '%in-person%')
                      ->orWhere('teaching_style', 'LIKE', '%offline%');
                });

            $this->applyCommonFilters($query, $request);

            $tutors = $query->orderBy('rating', 'desc')->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch offline tutors',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tutors by grade level
     */
    public function byGradeLevel(Request $request, $gradeLevel)
    {
        try {
            $query = Tutor::with(['user', 'categories'])
                ->whereHas('user', function ($q) {
                    $q->where('user_type', 'tutor')->where('is_active', true);
                })
                ->where(function ($q) use ($gradeLevel) {
                    $q->whereJsonContains('specializations', $gradeLevel)
                      ->orWhere('education_level', 'LIKE', '%' . $gradeLevel . '%')
                      ->orWhere('bio', 'LIKE', '%' . $gradeLevel . '%');
                });

            $this->applyCommonFilters($query, $request);

            $tutors = $query->orderBy('rating', 'desc')->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $tutors->items(),
                'pagination' => [
                    'current_page' => $tutors->currentPage(),
                    'last_page' => $tutors->lastPage(),
                    'per_page' => $tutors->perPage(),
                    'total' => $tutors->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tutors by grade level',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply common filters to query
     */
    private function applyCommonFilters($query, Request $request)
    {
        // Price range
        if ($request->has('min_rate')) {
            $query->where('hourly_rate', '>=', $request->min_rate);
        }
        if ($request->has('max_rate')) {
            $query->where('hourly_rate', '<=', $request->max_rate);
        }

        // Rating
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        // Experience
        if ($request->has('min_experience')) {
            $query->where('experience_years', '>=', $request->min_experience);
        }

        // Location
        if ($request->has('location')) {
            $query->where('location', 'LIKE', '%' . $request->location . '%');
        }

        // Availability
        if ($request->has('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        // Verification
        if ($request->has('is_verified')) {
            $query->where('is_verified', $request->boolean('is_verified'));
        }
    }

    /**
     * Get available filter options for the frontend
     */
    public function filterOptions()
    {
        try {
            $options = TutorFilterService::getFilterOptions();

            // Add dynamic options from database
            $subjects = Tutor::whereNotNull('subjects')
                ->pluck('subjects')
                ->map(function ($subjects) {
                    return json_decode($subjects, true);
                })
                ->flatten()
                ->unique()
                ->values()
                ->toArray();

            $locations = Tutor::whereNotNull('location')
                ->pluck('location')
                ->unique()
                ->values()
                ->toArray();

            $languages = Tutor::whereNotNull('languages')
                ->pluck('languages')
                ->map(function ($languages) {
                    return json_decode($languages, true);
                })
                ->flatten()
                ->unique()
                ->values()
                ->toArray();

            $options['subjects'] = $subjects;
            $options['locations'] = $locations;
            $options['languages'] = $languages;

            return response()->json([
                'success' => true,
                'data' => $options
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch filter options',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
