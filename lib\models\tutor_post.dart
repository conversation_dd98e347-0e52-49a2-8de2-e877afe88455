import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'tutor_post.g.dart';

@JsonSerializable()
class TutorPost {
  final int id;
  final int tutorId;
  final int? categoryId;
  final String title;
  final String content;
  final String? excerpt;
  final String postType;
  final List<String>? tags;
  final String? featuredImage;
  final List<PostAttachment>? attachments;
  final bool isPublished;
  final bool isFeatured;
  final DateTime? publishedAt;
  final int viewsCount;
  final int likesCount;
  final int commentsCount;
  final String? metaTitle;
  final String? metaDescription;
  final String slug;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  
  // Relationships
  final Tutor? tutor;
  final TutorCategory? category;
  final List<TutorReview>? reviews;

  const TutorPost({
    required this.id,
    required this.tutorId,
    this.categoryId,
    required this.title,
    required this.content,
    this.excerpt,
    required this.postType,
    this.tags,
    this.featuredImage,
    this.attachments,
    required this.isPublished,
    required this.isFeatured,
    this.publishedAt,
    required this.viewsCount,
    required this.likesCount,
    required this.commentsCount,
    this.metaTitle,
    this.metaDescription,
    required this.slug,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.tutor,
    this.category,
    this.reviews,
  });

  factory TutorPost.fromJson(Map<String, dynamic> json) => _$TutorPostFromJson(json);
  Map<String, dynamic> toJson() => _$TutorPostToJson(this);

  // Helper getters
  String get featuredImageUrl {
    if (featuredImage != null) {
      return 'https://your-domain.com/storage/$featuredImage';
    }
    return '';
  }

  String get displayExcerpt {
    if (excerpt != null && excerpt!.isNotEmpty) {
      return excerpt!;
    }
    // Generate excerpt from content
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    if (plainText.length <= 150) {
      return plainText;
    }
    return '${plainText.substring(0, 150)}...';
  }

  int get readingTime {
    final wordCount = content.split(' ').length;
    return (wordCount / 200).ceil(); // Assuming 200 words per minute
  }

  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;
  bool get hasTags => tags != null && tags!.isNotEmpty;
}

@JsonSerializable()
class PostAttachment {
  final String name;
  final String path;
  final int size;
  final String type;

  const PostAttachment({
    required this.name,
    required this.path,
    required this.size,
    required this.type,
  });

  factory PostAttachment.fromJson(Map<String, dynamic> json) => _$PostAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$PostAttachmentToJson(this);

  String get url => 'https://your-domain.com/storage/$path';
  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

@JsonSerializable()
class CreatePostRequest {
  final String title;
  final String content;
  final String? excerpt;
  final String postType;
  final int? categoryId;
  final List<String>? tags;
  final bool isPublished;
  final bool isFeatured;
  final String? metaTitle;
  final String? metaDescription;

  const CreatePostRequest({
    required this.title,
    required this.content,
    this.excerpt,
    required this.postType,
    this.categoryId,
    this.tags,
    this.isPublished = false,
    this.isFeatured = false,
    this.metaTitle,
    this.metaDescription,
  });

  factory CreatePostRequest.fromJson(Map<String, dynamic> json) => _$CreatePostRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePostRequestToJson(this);
}

@JsonSerializable()
class UpdatePostRequest {
  final String? title;
  final String? content;
  final String? excerpt;
  final String? postType;
  final int? categoryId;
  final List<String>? tags;
  final bool? isPublished;
  final bool? isFeatured;
  final String? metaTitle;
  final String? metaDescription;

  const UpdatePostRequest({
    this.title,
    this.content,
    this.excerpt,
    this.postType,
    this.categoryId,
    this.tags,
    this.isPublished,
    this.isFeatured,
    this.metaTitle,
    this.metaDescription,
  });

  factory UpdatePostRequest.fromJson(Map<String, dynamic> json) => _$UpdatePostRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePostRequestToJson(this);
}

// Post types enum
enum PostType {
  article,
  tutorial,
  announcement,
  resource,
  tip;

  String get displayName {
    switch (this) {
      case PostType.article:
        return 'Article';
      case PostType.tutorial:
        return 'Tutorial';
      case PostType.announcement:
        return 'Announcement';
      case PostType.resource:
        return 'Resource';
      case PostType.tip:
        return 'Tip';
    }
  }
}

@JsonSerializable()
class PaginatedPostResponse {
  final List<TutorPost> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedPostResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginatedPostResponse.fromJson(Map<String, dynamic> json) => _$PaginatedPostResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PaginatedPostResponseToJson(this);
}
