import 'package:flutter/material.dart';
import '../config/ui_constants.dart';

// Enhanced Loading Component
class EnhancedLoading extends StatefulWidget {
  final String? message;
  final double size;
  final Color? color;

  const EnhancedLoading({
    super.key,
    this.message,
    this.size = 40.0,
    this.color,
  });

  @override
  State<EnhancedLoading> createState() => _EnhancedLoadingState();
}

class _EnhancedLoadingState extends State<EnhancedLoading>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..repeat(reverse: true);

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_rotationController);

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: Listenable.merge([_rotationAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: const BoxDecoration(
                    gradient: UIConstants.primaryGradient,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.school,
                    color: Colors.white,
                    size: widget.size * 0.5,
                  ),
                ),
              ),
            );
          },
        ),
        if (widget.message != null) ...[
          const SizedBox(height: UIConstants.spacingLg),
          Text(
            widget.message!,
            style: UIConstants.bodyMd.copyWith(
              color: UIConstants.neutralGray600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

// Enhanced Empty State Component
class EnhancedEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String? actionText;
  final VoidCallback? onAction;

  const EnhancedEmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(UIConstants.spacing3xl),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x1A2563EB), Color(0x1A1D4ED8)],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 60,
                color: UIConstants.primaryBlue,
              ),
            ),
            const SizedBox(height: UIConstants.spacing2xl),
            Text(
              title,
              style: UIConstants.headingSm.copyWith(
                color: UIConstants.neutralGray800,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: UIConstants.spacingSm),
            Text(
              subtitle,
              style: UIConstants.bodyMd.copyWith(
                color: UIConstants.neutralGray500,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: UIConstants.spacing2xl),
              EnhancedButton(
                text: actionText!,
                onPressed: onAction,
                icon: const Icon(Icons.add),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Enhanced Card Component
class EnhancedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final List<BoxShadow>? boxShadow;
  final double? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showShimmer;

  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.boxShadow,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showShimmer = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget cardContent = Container(
      padding: padding ?? const EdgeInsets.all(UIConstants.spacingLg),
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(borderRadius ?? UIConstants.radiusLg),
        boxShadow: boxShadow ?? UIConstants.shadowMd,
        border: border,
      ),
      child: showShimmer ? _buildShimmerOverlay() : child,
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius ?? UIConstants.radiusLg),
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }

  Widget _buildShimmerOverlay() {
    return Stack(
      children: [
        child,
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: UIConstants.shimmerGradient,
              borderRadius: BorderRadius.circular(borderRadius ?? UIConstants.radiusLg),
            ),
          ),
        ),
      ],
    );
  }
}

// Enhanced Button Component
class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final Size? minimumSize;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.minimumSize,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: UIConstants.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: UIConstants.curveDefault,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.isOutlined ? _buildOutlinedButton() : _buildFilledButton(),
          );
        },
      ),
    );
  }

  Widget _buildFilledButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: widget.backgroundColor != null
            ? null
            : UIConstants.primaryGradient,
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(
          widget.borderRadius ?? UIConstants.radiusLg,
        ),
        boxShadow: UIConstants.shadowSm,
      ),
      child: ElevatedButton(
        onPressed: widget.isLoading ? null : widget.onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          foregroundColor: widget.foregroundColor ?? Colors.white,
          padding: widget.padding ?? const EdgeInsets.symmetric(
            horizontal: UIConstants.spacing2xl,
            vertical: UIConstants.spacingLg,
          ),
          minimumSize: widget.minimumSize,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              widget.borderRadius ?? UIConstants.radiusLg,
            ),
          ),
        ),
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildOutlinedButton() {
    return OutlinedButton(
      onPressed: widget.isLoading ? null : widget.onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: widget.foregroundColor ?? UIConstants.primaryBlue,
        padding: widget.padding ?? const EdgeInsets.symmetric(
          horizontal: UIConstants.spacing2xl,
          vertical: UIConstants.spacingLg,
        ),
        minimumSize: widget.minimumSize,
        side: BorderSide(
          color: widget.backgroundColor ?? UIConstants.primaryBlue,
          width: 2,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            widget.borderRadius ?? UIConstants.radiusLg,
          ),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (widget.isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.isOutlined
                ? UIConstants.primaryBlue
                : Colors.white,
          ),
        ),
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.icon!,
          const SizedBox(width: UIConstants.spacingSm),
          Text(
            widget.text,
            style: UIConstants.labelMd.copyWith(
              color: widget.isOutlined
                  ? UIConstants.primaryBlue
                  : Colors.white,
            ),
          ),
        ],
      );
    }

    return Text(
      widget.text,
      style: UIConstants.labelMd.copyWith(
        color: widget.isOutlined
            ? UIConstants.primaryBlue
            : Colors.white,
      ),
    );
  }
}

// Enhanced Search Bar Component
class EnhancedSearchBar extends StatefulWidget {
  final String hintText;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final bool showFilter;
  final VoidCallback? onFilterTap;

  const EnhancedSearchBar({
    super.key,
    this.hintText = 'Search...',
    this.onChanged,
    this.onClear,
    this.controller,
    this.showFilter = false,
    this.onFilterTap,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: UIConstants.animationNormal,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: UIConstants.curveDefault,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UIConstants.radiusXl),
        boxShadow: _hasFocus ? UIConstants.shadowLg : UIConstants.shadowMd,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              onChanged: widget.onChanged,
              onTap: () {
                setState(() => _hasFocus = true);
                _animationController.forward();
              },
              onEditingComplete: () {
                setState(() => _hasFocus = false);
                _animationController.reverse();
              },
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: UIConstants.bodyMd.copyWith(
                  color: UIConstants.neutralGray400,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: _hasFocus
                      ? UIConstants.primaryBlue
                      : UIConstants.neutralGray400,
                ),
                suffixIcon: widget.controller?.text.isNotEmpty == true
                    ? FadeTransition(
                        opacity: _fadeAnimation,
                        child: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            widget.controller?.clear();
                            widget.onClear?.call();
                          },
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: UIConstants.spacingLg,
                  vertical: UIConstants.spacingMd,
                ),
              ),
            ),
          ),
          if (widget.showFilter)
            Container(
              margin: const EdgeInsets.only(right: UIConstants.spacingSm),
              child: IconButton(
                onPressed: widget.onFilterTap,
                icon: const Icon(Icons.tune),
                style: IconButton.styleFrom(
                  backgroundColor: UIConstants.primaryBlue.withValues(alpha: 0.1),
                  foregroundColor: UIConstants.primaryBlue,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
