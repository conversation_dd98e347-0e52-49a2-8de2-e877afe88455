import 'package:flutter/material.dart';

class MySessionsScreen extends StatefulWidget {
  const MySessionsScreen({super.key});

  @override
  State<MySessionsScreen> createState() => _MySessionsScreenState();
}

class _MySessionsScreenState extends State<MySessionsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('My Sessions'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Completed'),
            Tab(text: 'Cancelled'),
          ],
        ),
      ),
      body: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
        controller: _tabController,
        children: [
          _buildUpcomingSessions(),
          _buildCompletedSessions(),
          _buildCancelledSessions(),
        ],
      ),
    );
  }

  Widget _buildUpcomingSessions() {
    final upcomingSessions = [
      {
        'id': '1',
        'tutorName': 'Dr. Sarah Johnson',
        'subject': 'Mathematics',
        'date': 'Today',
        'time': '2:00 PM - 3:00 PM',
        'type': 'Online',
        'status': 'confirmed',
        'avatar': 'assets/images/avatar1.png',
      },
      {
        'id': '2',
        'tutorName': 'Prof. Michael Chen',
        'subject': 'Physics',
        'date': 'Tomorrow',
        'time': '10:00 AM - 11:30 AM',
        'type': 'In-person',
        'status': 'pending',
        'avatar': 'assets/images/avatar2.png',
      },
      {
        'id': '3',
        'tutorName': 'Ms. Emily Davis',
        'subject': 'English Literature',
        'date': 'Dec 16, 2024',
        'time': '4:00 PM - 5:00 PM',
        'type': 'Online',
        'status': 'confirmed',
        'avatar': 'assets/images/avatar3.png',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: upcomingSessions.length,
      itemBuilder: (context, index) {
        final session = upcomingSessions[index];
        return _buildSessionCard(session, isUpcoming: true);
      },
    );
  }

  Widget _buildCompletedSessions() {
    final completedSessions = [
      {
        'id': '4',
        'tutorName': 'Dr. Sarah Johnson',
        'subject': 'Mathematics',
        'date': 'Dec 10, 2024',
        'time': '2:00 PM - 3:00 PM',
        'type': 'Online',
        'status': 'completed',
        'rating': 5,
        'avatar': 'assets/images/avatar1.png',
      },
      {
        'id': '5',
        'tutorName': 'Prof. Michael Chen',
        'subject': 'Physics',
        'date': 'Dec 8, 2024',
        'time': '10:00 AM - 11:30 AM',
        'type': 'In-person',
        'status': 'completed',
        'rating': 4,
        'avatar': 'assets/images/avatar2.png',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: completedSessions.length,
      itemBuilder: (context, index) {
        final session = completedSessions[index];
        return _buildSessionCard(session, isCompleted: true);
      },
    );
  }

  Widget _buildCancelledSessions() {
    final cancelledSessions = [
      {
        'id': '6',
        'tutorName': 'Ms. Emily Davis',
        'subject': 'English Literature',
        'date': 'Dec 5, 2024',
        'time': '4:00 PM - 5:00 PM',
        'type': 'Online',
        'status': 'cancelled',
        'reason': 'Tutor unavailable',
        'avatar': 'assets/images/avatar3.png',
      },
    ];

    if (cancelledSessions.isEmpty) {
      return _buildEmptyState('No cancelled sessions', Icons.cancel_outlined);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: cancelledSessions.length,
      itemBuilder: (context, index) {
        final session = cancelledSessions[index];
        return _buildSessionCard(session, isCancelled: true);
      },
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session, {
    bool isUpcoming = false,
    bool isCompleted = false,
    bool isCancelled = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: Colors.blue.shade100,
                  child: Icon(Icons.person, color: Colors.blue.shade700),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        session['tutorName'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        session['subject'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(session['status']),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(session['date'], style: TextStyle(color: Colors.grey.shade600)),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Text(session['time'], style: TextStyle(color: Colors.grey.shade600)),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  session['type'] == 'Online' ? Icons.videocam : Icons.location_on,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(session['type'], style: TextStyle(color: Colors.grey.shade600)),
              ],
            ),
            if (isCompleted && session['rating'] != null) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  const Text('Your rating: '),
                  ...List.generate(5, (index) => Icon(
                    index < session['rating'] ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 16,
                  )),
                ],
              ),
            ],
            if (isCancelled && session['reason'] != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.red.shade700, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Reason: ${session['reason']}',
                      style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 16),
            _buildActionButtons(session, isUpcoming, isCompleted, isCancelled),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    String text;
    
    switch (status) {
      case 'confirmed':
        color = Colors.green;
        text = 'Confirmed';
        break;
      case 'pending':
        color = Colors.orange;
        text = 'Pending';
        break;
      case 'completed':
        color = Colors.blue;
        text = 'Completed';
        break;
      case 'cancelled':
        color = Colors.red;
        text = 'Cancelled';
        break;
      default:
        color = Colors.grey;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActionButtons(Map<String, dynamic> session, bool isUpcoming, bool isCompleted, bool isCancelled) {
    if (isUpcoming) {
      return Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => _showCancelDialog(session),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: () => _joinSession(session),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
              ),
              child: Text(session['type'] == 'Online' ? 'Join' : 'Details'),
            ),
          ),
        ],
      );
    } else if (isCompleted) {
      return Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => _bookAgain(session),
              child: const Text('Book Again'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: () => _writeReview(session),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
              ),
              child: const Text('Write Review'),
            ),
          ),
        ],
      );
    } else {
      return SizedBox(
        width: double.infinity,
        child: OutlinedButton(
          onPressed: () => _bookAgain(session),
          child: const Text('Book Again'),
        ),
      );
    }
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _joinSession(Map<String, dynamic> session) {
    if (session['type'] == 'Online') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Joining online session...')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Session details opened')),
      );
    }
  }

  void _showCancelDialog(Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Session'),
        content: const Text('Are you sure you want to cancel this session?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Session cancelled')),
              );
            },
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _bookAgain(Map<String, dynamic> session) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Booking again with ${session['tutorName']}')),
    );
  }

  void _writeReview(Map<String, dynamic> session) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Review feature coming soon!')),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
