<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    /**
     * Get the tutors that belong to this category.
     */
    public function tutors(): BelongsToMany
    {
        return $this->belongsToMany(Tutor::class, 'tutor_categories');
    }

    /**
     * Get the count of tutors in this category.
     */
    public function getTutorCountAttribute(): int
    {
        return $this->tutors()->where('status', 'approved')->count();
    }
}
