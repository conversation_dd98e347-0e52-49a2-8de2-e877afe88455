import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../config/api_thmeme.dart';
import '../models/user.dart';
import 'custom_button.dart';

class TutorCard extends StatelessWidget {
  final Tutor tutor;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final VoidCallback? onChat;
  final bool isFavorite;
  final bool showActions;

  const TutorCard({
    super.key,
    required this.tutor,
    this.onTap,
    this.onFavorite,
    this.onChat,
    this.isFavorite = false,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: AppTheme.spacingM),
                _buildInfo(),
                if (tutor.description != null) ...[
                  const SizedBox(height: AppTheme.spacingS),
                  _buildDescription(),
                ],
                const SizedBox(height: AppTheme.spacingM),
                _buildSubjects(),
                if (showActions) ...[
                  const SizedBox(height: AppTheme.spacingM),
                  _buildActions(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildAvatar(),
        const SizedBox(width: AppTheme.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      tutor.name ?? 'Tutor',
                      style: AppTheme.headingSmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildOnlineStatus(),
                ],
              ),
              const SizedBox(height: AppTheme.spacingXS),
              _buildRating(),
              const SizedBox(height: AppTheme.spacingXS),
              _buildPrice(),
            ],
          ),
        ),
        _buildFavoriteButton(),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppTheme.radiusM - 2),
        child: tutor.image != null
            ? CachedNetworkImage(
                imageUrl: tutor.image!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Shimmer.fromColors(
                  baseColor: Colors.grey.shade300,
                  highlightColor: Colors.grey.shade100,
                  child: Container(
                    color: Colors.white,
                  ),
                ),
                errorWidget: (context, url, error) => _buildAvatarFallback(),
              )
            : _buildAvatarFallback(),
      ),
    );
  }

  Widget _buildAvatarFallback() {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppTheme.primaryGradient,
      ),
      child: Center(
        child: Text(
          (tutor.name?.isNotEmpty ?? false) ? tutor.name![0].toUpperCase() : '?',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildOnlineStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: (tutor.isOnline ?? false)
            ? AppTheme.successColor.withValues(alpha: 0.1)
            : AppTheme.textLight.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: (tutor.isOnline ?? false) ? AppTheme.successColor : AppTheme.textLight,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppTheme.spacingXS),
          Text(
            (tutor.isOnline ?? false) ? 'Online' : 'Offline',
            style: AppTheme.captionText.copyWith(
              color: (tutor.isOnline ?? false) ? AppTheme.successColor : AppTheme.textLight,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRating() {
    return Row(
      children: [
        ...List.generate(5, (index) {
          return Icon(
            index < tutor.rating.floor()
                ? Icons.star
                : (index < tutor.rating ? Icons.star_half : Icons.star_border),
            color: AppTheme.accentColor,
            size: 16,
          );
        }),
        const SizedBox(width: AppTheme.spacingXS),
        Text(
          '${tutor.rating.toStringAsFixed(1)} (${tutor.totalReviews})',
          style: AppTheme.bodySmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPrice() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
      ),
      child: Text(
        '\$${tutor.hourlyRateValue.toStringAsFixed(0)}/hour',
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return IconButton(
      onPressed: onFavorite,
      icon: Icon(
        isFavorite ? Icons.favorite : Icons.favorite_border,
        color: isFavorite ? AppTheme.errorColor : AppTheme.textLight,
        size: 24,
      ),
    );
  }

  Widget _buildInfo() {
    return Row(
      children: [
        _buildInfoItem(
          icon: Icons.school_outlined,
          text: '${tutor.experienceYears ?? 0} years exp',
        ),
        const SizedBox(width: AppTheme.spacingM),
        _buildInfoItem(
          icon: Icons.people_outlined,
          text: '${tutor.totalStudents} students',
        ),
        const SizedBox(width: AppTheme.spacingM),
        _buildInfoItem(
          icon: Icons.location_on_outlined,
          text: tutor.location ?? 'Remote',
        ),
      ],
    );
  }

  Widget _buildInfoItem({required IconData icon, required String text}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: AppTheme.textLight,
        ),
        const SizedBox(width: AppTheme.spacingXS),
        Text(
          text,
          style: AppTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return Text(
      tutor.description!,
      style: AppTheme.bodyMedium,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubjects() {
    if (tutor.categories == null || tutor.categories!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Wrap(
      spacing: AppTheme.spacingS,
      runSpacing: AppTheme.spacingS,
      children: tutor.categories!.take(3).map((category) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingS,
            vertical: AppTheme.spacingXS,
          ),
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusS),
          ),
          child: Text(
            category.name,
            style: AppTheme.captionText.copyWith(
              color: AppTheme.secondaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: OutlineButton(
            text: 'View Profile',
            onPressed: onTap,
            size: ButtonSize.small,
          ),
        ),
        const SizedBox(width: AppTheme.spacingS),
        Expanded(
          child: PrimaryButton(
            text: 'Chat',
            onPressed: onChat,
            size: ButtonSize.small,
            icon: Icons.chat_outlined,
          ),
        ),
      ],
    );
  }
}
