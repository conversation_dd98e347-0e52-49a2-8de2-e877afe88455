import 'package:shared_preferences/shared_preferences.dart';

class AppSharedPref {
  static Future<void> login(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString("email", email);
    prefs.setString("password", password);
  }

  static Future<void> register(String fullname, String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setString("fullname", fullname);
    prefs.setString("email", email);
    prefs.setString("password", password);
  }

  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.clear();
  }
}
