# Laravel + Flutter Tutor Finder Setup Guide

## 🚀 Quick Setup

Your project now has a complete Laravel backend integrated with your Flutter frontend! Here's how to get everything running:

## 📋 Prerequisites

- PHP 8.1 or higher
- Composer
- MySQL/MariaDB
- Flutter SDK
- Android Studio/VS Code

## 🔧 Laravel Backend Setup

### 1. Navigate to Laravel Directory
```bash
cd tutor_finder_kh
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Environment Configuration
The `.env` file is already configured. Update these settings if needed:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tutor_finder_kh
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 4. Create Database
Create a MySQL database named `tutor_finder_kh`:
```sql
CREATE DATABASE tutor_finder_kh;
```

### 5. Run Migrations
```bash
php artisan migrate
```

### 6. Install Laravel Sanctum (for API authentication)
```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
```

### 7. Start Laravel Server
```bash
php artisan serve
```
The API will be available at: `http://127.0.0.1:8000`

## 📱 Flutter Frontend Setup

### 1. Navigate to Flutter Directory (if not already there)
```bash
cd ..  # Go back to root directory
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Update API Configuration
The API is already configured in `lib/config/api_config.dart`. Choose the appropriate base URL:

- **Android Emulator**: `http://********:8000/api/v1` (default)
- **iOS Simulator**: `http://127.0.0.1:8000/api/v1`
- **Physical Device**: `http://YOUR_IP:8000/api/v1`

### 4. Run Flutter App
```bash
flutter run
```

## 🎯 API Endpoints Created

### Authentication
- `POST /api/v1/auth/register/student` - Register student
- `POST /api/v1/auth/register/tutor` - Register tutor
- `POST /api/v1/auth/login` - Login
- `POST /api/v1/auth/logout` - Logout (protected)
- `GET /api/v1/auth/profile` - Get profile (protected)
- `PUT /api/v1/auth/profile` - Update profile (protected)

### Tutors
- `GET /api/v1/tutors` - Get all tutors with filters
- `GET /api/v1/tutors/{id}` - Get specific tutor
- `GET /api/v1/tutors/featured` - Get featured tutors
- `GET /api/v1/tutors/search/subject` - Search by subject
- `GET /api/v1/tutors/{id}/statistics` - Get tutor stats
- `PUT /api/v1/tutors/{id}/availability` - Update availability (protected)

### Categories
- `GET /api/v1/categories` - Get all categories
- `GET /api/v1/categories/{id}` - Get specific category
- `GET /api/v1/categories/popular` - Get popular categories
- `GET /api/v1/categories/with-tutor-count` - Categories with tutor count

### Favorites (Protected)
- `GET /api/v1/favorites` - Get user's favorites
- `POST /api/v1/favorites` - Add to favorites
- `DELETE /api/v1/favorites/{tutorId}` - Remove from favorites
- `GET /api/v1/favorites/check/{tutorId}` - Check if favorited
- `POST /api/v1/favorites/toggle` - Toggle favorite status

### Messages (Protected)
- `GET /api/v1/messages/conversations` - Get conversations
- `GET /api/v1/messages/conversation/{userId}/{userType}` - Get conversation messages
- `POST /api/v1/messages/send` - Send message
- `PUT /api/v1/messages/conversations/{id}/read` - Mark as read
- `GET /api/v1/messages/unread-count` - Get unread count

## 🔐 Authentication

The API uses Laravel Sanctum for authentication. After login, include the token in requests:
```
Authorization: Bearer YOUR_TOKEN_HERE
```

## 🗄️ Database Models Created

- **User** - Base user model (students and tutors)
- **Student** - Student profile
- **Tutor** - Tutor profile
- **Category** - Subject categories
- **Favorite** - User's favorite tutors
- **Message** - Chat messages
- **Conversation** - Chat conversations
- **Session** - Tutoring sessions (existing)
- **Review** - Tutor reviews (existing)

## 🧪 Testing the API

### 1. Register a Student
```bash
curl -X POST http://127.0.0.1:8000/api/v1/auth/register/student \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

### 2. Login
```bash
curl -X POST http://127.0.0.1:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. Get Tutors (with token)
```bash
curl -X GET http://127.0.0.1:8000/api/v1/tutors \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🚨 Troubleshooting

### Laravel Issues
- **500 Error**: Check `storage/logs/laravel.log`
- **Database Connection**: Verify `.env` database settings
- **CORS Issues**: Install `laravel-cors` if needed

### Flutter Issues
- **Network Error**: Check API base URL in `api_config.dart`
- **Android Emulator**: Use `********` instead of `127.0.0.1`
- **iOS Simulator**: Use `127.0.0.1`

## 📝 Next Steps

1. **Seed Database**: Create seeders for categories and sample tutors
2. **File Upload**: Add profile picture upload functionality
3. **Real-time Chat**: Implement WebSocket for real-time messaging
4. **Push Notifications**: Add Firebase for notifications
5. **Payment Integration**: Add payment gateway for sessions

## 🎉 You're All Set!

Your Laravel backend is now fully integrated with your Flutter app. The API provides all the endpoints your Flutter app needs for:

- ✅ User authentication (students and tutors)
- ✅ Tutor browsing and search
- ✅ Categories management
- ✅ Favorites system
- ✅ Messaging system
- ✅ Profile management

Start both servers and test the integration!
