import '../models/group.dart';
import '../services/api_client.dart';

class GroupService {
  final ApiClient _apiClient = ApiClient();

  /// Get all available groups with optional filters
  Future<List<Group>> getGroups({
    String? subject,
    String? creatorType,
    String? search,
    int page = 1,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
      };

      if (subject != null) queryParams['subject'] = subject;
      if (creatorType != null) queryParams['creator_type'] = creatorType;
      if (search != null) queryParams['search'] = search;

      // Use the raw API call instead of generic method
      final response = await _apiClient.rawGet('/groups', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> groupsData = response['data'] ?? [];
        return groupsData.map((json) => Group.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch groups');
      }
    } catch (e) {
      throw Exception('Failed to fetch groups: $e');
    }
  }

  /// Get user's joined groups
  Future<List<Group>> getUserGroups() async {
    try {
      final response = await _apiClient.rawGet('/groups/user');

      if (response['success'] == true) {
        final List<dynamic> groupsData = response['data'] ?? [];
        return groupsData.map((json) => Group.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch user groups');
      }
    } catch (e) {
      throw Exception('Failed to fetch user groups: $e');
    }
  }

  /// Get a specific group with details
  Future<Group> getGroup(int groupId) async {
    try {
      final response = await _apiClient.get<Group>(
        '/groups/$groupId',
        fromJson: (data) => Group.fromJson(data),
      );

      if (response.isSuccess && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch group');
      }
    } catch (e) {
      throw Exception('Failed to fetch group: $e');
    }
  }

  /// Create a new group (tutors only)
  Future<Group> createGroup({
    required String name,
    String? description,
    String? subject,
    int maxMembers = 50,
    String groupType = 'study',
  }) async {
    try {
      final Map<String, dynamic> data = {
        'name': name,
        'max_members': maxMembers,
        'group_type': groupType,
      };

      if (description != null) data['description'] = description;
      if (subject != null) data['subject'] = subject;

      final response = await _apiClient.post<Group>(
        '/groups',
        data: data,
        fromJson: (data) => Group.fromJson(data),
      );

      if (response.isSuccess && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to create group');
      }
    } catch (e) {
      throw Exception('Failed to create group: $e');
    }
  }

  /// Join a group
  Future<bool> joinGroup(int groupId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/groups/$groupId/join',
        data: {},
        fromJson: (data) => data,
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw Exception(response.message ?? 'Failed to join group');
      }
    } catch (e) {
      throw Exception('Failed to join group: $e');
    }
  }

  /// Leave a group
  Future<bool> leaveGroup(int groupId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/groups/$groupId/leave',
        data: {},
        fromJson: (data) => data,
      );

      if (response.isSuccess) {
        return true;
      } else {
        throw Exception(response.message ?? 'Failed to leave group');
      }
    } catch (e) {
      throw Exception('Failed to leave group: $e');
    }
  }

  /// Get group messages
  Future<List<GroupMessage>> getGroupMessages(int groupId, {int page = 1}) async {
    try {
      final response = await _apiClient.rawGet(
        '/groups/$groupId/messages',
        queryParams: {'page': page.toString()},
      );

      if (response['success'] == true) {
        final List<dynamic> messagesData = response['data'] ?? [];
        return messagesData.map((json) => GroupMessage.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch group messages');
      }
    } catch (e) {
      throw Exception('Failed to fetch group messages: $e');
    }
  }

  /// Send a message to a group
  Future<GroupMessage> sendGroupMessage({
    required int groupId,
    required String message,
    String messageType = 'text',
    int? replyToId,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'group_id': groupId,
        'message': message,
        'message_type': messageType,
      };

      if (replyToId != null) data['reply_to_id'] = replyToId;

      final response = await _apiClient.post<GroupMessage>(
        '/groups/messages',
        data: data,
        fromJson: (data) => GroupMessage.fromJson(data),
      );

      if (response.isSuccess && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to send message');
      }
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  /// Get available subjects for group filtering
  Future<List<String>> getSubjects() async {
    try {
      // This would typically come from a subjects API endpoint
      // For now, return common subjects
      return [
        'Mathematics',
        'Physics',
        'Chemistry',
        'Biology',
        'English',
        'History',
        'Geography',
        'Computer Science',
        'Economics',
        'Psychology',
      ];
    } catch (e) {
      throw Exception('Failed to fetch subjects: $e');
    }
  }
}
