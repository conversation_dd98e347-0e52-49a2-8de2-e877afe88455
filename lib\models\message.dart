import 'package:json_annotation/json_annotation.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_id')
  final int senderId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'receiver_id')
  final int receiverId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_type')
  final String senderType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'receiver_type')
  final String receiverType;
  final String message;
  @<PERSON>son<PERSON>ey(name: 'message_type')
  final String messageType;
  @<PERSON>son<PERSON>ey(name: 'file_path')
  final String? filePath;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read')
  final bool isRead;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at')
  final String? readAt;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.senderType,
    required this.receiverType,
    required this.message,
    this.messageType = 'text',
    this.filePath,
    this.isRead = false,
    this.readAt,
    this.createdAt,
    this.updatedAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  // Helper methods
  bool get isFromStudent => senderType == 'student';
  bool get isFromTutor => senderType == 'tutor';
  bool get isTextMessage => messageType == 'text';
  bool get isImageMessage => messageType == 'image';
  bool get isFileMessage => messageType == 'file';
  bool get hasFile => filePath != null && filePath!.isNotEmpty;

  DateTime? get createdDateTime {
    try {
      return createdAt != null ? DateTime.parse(createdAt!) : null;
    } catch (e) {
      return null;
    }
  }

  DateTime? get readDateTime {
    try {
      return readAt != null ? DateTime.parse(readAt!) : null;
    } catch (e) {
      return null;
    }
  }

  Message copyWith({
    int? id,
    int? senderId,
    int? receiverId,
    String? senderType,
    String? receiverType,
    String? message,
    String? messageType,
    String? filePath,
    bool? isRead,
    String? readAt,
    String? createdAt,
    String? updatedAt,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      senderType: senderType ?? this.senderType,
      receiverType: receiverType ?? this.receiverType,
      message: message ?? this.message,
      messageType: messageType ?? this.messageType,
      filePath: filePath ?? this.filePath,
      isRead: isRead ?? this.isRead,
      readAt: readAt ?? this.readAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class Conversation {
  final int id;
  @JsonKey(name: 'other_user_id')
  final int otherUserId;
  @JsonKey(name: 'other_user_type')
  final String otherUserType;
  @JsonKey(name: 'other_user_name')
  final String otherUserName;
  @JsonKey(name: 'other_user_avatar')
  final String? otherUserAvatar;
  @JsonKey(name: 'last_message')
  final String? lastMessage;
  @JsonKey(name: 'last_message_at')
  final String? lastMessageAt;
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  Conversation({
    required this.id,
    required this.otherUserId,
    required this.otherUserType,
    required this.otherUserName,
    this.otherUserAvatar,
    this.lastMessage,
    this.lastMessageAt,
    this.unreadCount = 0,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) => _$ConversationFromJson(json);
  Map<String, dynamic> toJson() => _$ConversationToJson(this);

  DateTime? get lastMessageDateTime {
    try {
      return lastMessageAt != null ? DateTime.parse(lastMessageAt!) : null;
    } catch (e) {
      return null;
    }
  }

  bool get hasUnreadMessages => unreadCount > 0;
  bool get isWithTutor => otherUserType == 'tutor';
  bool get isWithStudent => otherUserType == 'student';

  Conversation copyWith({
    int? id,
    int? otherUserId,
    String? otherUserType,
    String? otherUserName,
    String? otherUserAvatar,
    String? lastMessage,
    String? lastMessageAt,
    int? unreadCount,
  }) {
    return Conversation(
      id: id ?? this.id,
      otherUserId: otherUserId ?? this.otherUserId,
      otherUserType: otherUserType ?? this.otherUserType,
      otherUserName: otherUserName ?? this.otherUserName,
      otherUserAvatar: otherUserAvatar ?? this.otherUserAvatar,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

@JsonSerializable()
class SendMessageRequest {
  @JsonKey(name: 'receiver_id')
  final int receiverId;
  @JsonKey(name: 'receiver_type')
  final String receiverType;
  @JsonKey(name: 'sender_type')
  final String? senderType;
  @JsonKey(name: 'sender_id')
  final int? senderId;
  final String message;
  @JsonKey(name: 'message_type')
  final String messageType;

  SendMessageRequest({
    required this.receiverId,
    required this.receiverType,
    required this.message,
    this.senderType,
    this.senderId,
    this.messageType = 'text',
  });

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) => _$SendMessageRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SendMessageRequestToJson(this);
}

@JsonSerializable()
class UnreadCount {
  @JsonKey(name: 'unread_count')
  final int unreadCount;

  UnreadCount({required this.unreadCount});

  factory UnreadCount.fromJson(Map<String, dynamic> json) => _$UnreadCountFromJson(json);
  Map<String, dynamic> toJson() => _$UnreadCountToJson(this);
}
