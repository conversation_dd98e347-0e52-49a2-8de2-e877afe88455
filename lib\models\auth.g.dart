// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
      user: json['user'],
      token: json['token'] as String,
      tokenType: json['token_type'] as String?,
    );

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'token': instance.token,
      'token_type': instance.tokenType,
    };

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      email: json['email'] as String,
      password: json['password'] as String,
      userType: json['user_type'] as String,
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'user_type': instance.userType,
    };

StudentRegisterRequest _$StudentRegisterRequestFromJson(
        Map<String, dynamic> json) =>
    StudentRegisterRequest(
      name: json['name'] as String,
      email: json['email'] as String,
      password: json['password'] as String,
      passwordConfirmation: json['password_confirmation'] as String,
      phone: json['phone'] as String?,
    );

Map<String, dynamic> _$StudentRegisterRequestToJson(
        StudentRegisterRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'email': instance.email,
      'password': instance.password,
      'password_confirmation': instance.passwordConfirmation,
      'phone': instance.phone,
    };

TutorRegisterRequest _$TutorRegisterRequestFromJson(
        Map<String, dynamic> json) =>
    TutorRegisterRequest(
      name: json['name'] as String,
      email: json['email'] as String,
      password: json['password'] as String,
      passwordConfirmation: json['password_confirmation'] as String,
      phone: json['phone'] as String?,
      description: json['description'] as String?,
      location: json['location'] as String?,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      experienceYears: (json['experience_years'] as num?)?.toInt(),
      qualifications: json['qualifications'] as String?,
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$TutorRegisterRequestToJson(
        TutorRegisterRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'email': instance.email,
      'password': instance.password,
      'password_confirmation': instance.passwordConfirmation,
      'phone': instance.phone,
      'description': instance.description,
      'location': instance.location,
      'hourly_rate': instance.hourlyRate,
      'experience_years': instance.experienceYears,
      'qualifications': instance.qualifications,
      'languages': instance.languages,
    };

UpdateProfileRequest _$UpdateProfileRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateProfileRequest(
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      gender: json['gender'] as String?,
      location: json['location'] as String?,
      bio: json['bio'] as String?,
      description: json['description'] as String?,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      experienceYears: (json['experience_years'] as num?)?.toInt(),
      qualifications: json['qualifications'] as String?,
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isOnline: json['is_online'] as bool?,
    );

Map<String, dynamic> _$UpdateProfileRequestToJson(
        UpdateProfileRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'phone': instance.phone,
      'date_of_birth': instance.dateOfBirth,
      'gender': instance.gender,
      'location': instance.location,
      'bio': instance.bio,
      'description': instance.description,
      'hourly_rate': instance.hourlyRate,
      'experience_years': instance.experienceYears,
      'qualifications': instance.qualifications,
      'languages': instance.languages,
      'is_online': instance.isOnline,
    };
