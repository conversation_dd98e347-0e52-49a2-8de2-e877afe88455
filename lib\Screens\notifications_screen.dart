
import 'package:flutter/material.dart';

class NotiScreen extends StatefulWidget {
  const NotiScreen({super.key});

  @override
  State<NotiScreen> createState() => _NotiScreenState();
}

class _NotiScreenState extends State<NotiScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _notifications = [];
  bool _isLoading = true;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    // Simulate loading delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock notifications data
    setState(() {
      _notifications = [
        {
          'id': '1',
          'type': 'booking',
          'title': 'Booking Confirmed',
          'message': 'Your tutoring session with Dr. <PERSON> has been successfully confirmed for tomorrow at 2:00 PM. Please make sure to prepare your materials and join the session 5 minutes early. The session will cover advanced calculus topics including derivatives and integrals. If you need to reschedule, please contact us at least 24 hours in advance.',
          'additionalInfo': 'Session Details:\n• Subject: Advanced Calculus\n• Duration: 90 minutes\n• Location: Online via Zoom\n• Meeting ID will be sent 30 minutes before the session',
          'time': DateTime.now().subtract(const Duration(minutes: 30)),
          'isRead': false,
          'icon': Icons.event_available,
          'color': Colors.green,
          'avatar': 'assets/images/avatar1.png',
        },
        {
          'id': '2',
          'type': 'message',
          'title': 'New Message from Tutor',
          'message': 'Prof. Michael Chen sent you a detailed message about your Physics assignment. He has provided feedback on your recent homework submission and wants to discuss some concepts that need clarification. The message includes specific recommendations for improvement and additional resources for better understanding of quantum mechanics principles.',
          'additionalInfo': 'Message Preview:\n"Hi! I\'ve reviewed your assignment on quantum mechanics. Your understanding of wave functions is good, but we need to work on the mathematical applications. I\'ve attached some additional practice problems that will help you master these concepts before our next session."',
          'time': DateTime.now().subtract(const Duration(hours: 2)),
          'isRead': false,
          'icon': Icons.message,
          'color': Colors.blue,
          'avatar': 'assets/images/avatar2.png',
        },
        {
          'id': '3',
          'type': 'system',
          'title': 'Profile Successfully Updated',
          'message': 'Your profile has been successfully updated with new information including your academic preferences, learning goals, and contact details. These updates will help us match you with more suitable tutors and provide personalized learning recommendations. Your profile is now 95% complete.',
          'additionalInfo': 'Updated Information:\n• Academic Level: University\n• Preferred Subjects: Mathematics, Physics\n• Learning Style: Visual and Practical\n• Availability: Weekday evenings',
          'time': DateTime.now().subtract(const Duration(hours: 5)),
          'isRead': true,
          'icon': Icons.person,
          'color': Colors.orange,
          'avatar': null,
        },
        {
          'id': '4',
          'type': 'booking',
          'title': 'Session Reminder',
          'message': 'Don\'t forget your English Literature session with Ms. Emily Davis starts in 1 hour. Today\'s topic will be "Shakespeare\'s Hamlet: Character Analysis and Themes". Please have your copy of the play ready and review Act 3 before the session. Ms. Davis has prepared interactive exercises to help you understand the complex themes and character motivations.',
          'additionalInfo': 'Preparation Checklist:\n• Read Act 3 of Hamlet\n• Prepare questions about character motivations\n• Have notebook ready for taking notes\n• Ensure stable internet connection for online session',
          'time': DateTime.now().subtract(const Duration(hours: 8)),
          'isRead': false,
          'icon': Icons.schedule,
          'color': Colors.purple,
          'avatar': 'assets/images/avatar3.png',
        },
        {
          'id': '5',
          'type': 'system',
          'title': 'Payment Successful',
          'message': 'Your payment of \$50 for Math tutoring has been processed successfully',
          'time': DateTime.now().subtract(const Duration(days: 1)),
          'isRead': true,
          'icon': Icons.payment,
          'color': Colors.green,
          'avatar': null,
        },
        {
          'id': '6',
          'type': 'message',
          'title': 'Assignment Feedback',
          'message': 'Dr. Sarah Johnson has provided feedback on your calculus homework',
          'time': DateTime.now().subtract(const Duration(days: 2)),
          'isRead': true,
          'icon': Icons.feedback,
          'color': Colors.blue,
          'avatar': 'assets/images/avatar1.png',
        },
      ];

      _unreadCount = _notifications.where((n) => !n['isRead']).length;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: [
            const Text(
              'Notifications',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            if (_unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$_unreadCount',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (_unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: Text(
                'Mark all read',
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.grey.shade700),
            onSelected: (value) {
              switch (value) {
                case 'settings':
                  _showNotificationSettings();
                  break;
                case 'clear':
                  _clearAllNotifications();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Notification Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue.shade700,
          unselectedLabelColor: Colors.grey.shade600,
          indicatorColor: Colors.blue.shade700,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Unread'),
            Tab(text: 'Important'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildAllNotifications(),
                _buildUnreadNotifications(),
                _buildImportantNotifications(),
              ],
            ),
    );
  }

  Widget _buildAllNotifications() {
    if (_notifications.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: _notifications.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildUnreadNotifications() {
    final unreadNotifications = _notifications.where((n) => !n['isRead']).toList();

    if (unreadNotifications.isEmpty) {
      return _buildEmptyState(
        title: 'No Unread Notifications',
        subtitle: 'All caught up! You have no unread notifications.',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: unreadNotifications.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final notification = unreadNotifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildImportantNotifications() {
    final importantNotifications = _notifications
        .where((n) => n['type'] == 'booking' || n['type'] == 'payment')
        .toList();

    if (importantNotifications.isEmpty) {
      return _buildEmptyState(
        title: 'No Important Notifications',
        subtitle: 'Important notifications like bookings and payments will appear here.',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: importantNotifications.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final notification = importantNotifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildEmptyState({
    String title = 'No Notifications',
    String subtitle = 'You\'ll see notifications about your bookings, messages, and updates here.',
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.notifications_none,
                size: 60,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification) {
    return Container(
      decoration: BoxDecoration(
        color: notification['isRead'] ? Colors.white : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: notification['isRead']
            ? Border.all(color: Colors.grey.shade200)
            : Border.all(color: Colors.blue.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _handleNotificationTap(notification),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar or Icon
                if (notification['avatar'] != null)
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: AssetImage(notification['avatar']),
                    backgroundColor: Colors.grey.shade200,
                  )
                else
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: (notification['color'] as Color).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Icon(
                      notification['icon'] as IconData,
                      color: notification['color'] as Color,
                      size: 24,
                    ),
                  ),
                const SizedBox(width: 16),
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification['title'],
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: notification['isRead']
                                    ? FontWeight.w600
                                    : FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                          if (!notification['isRead'])
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification['message'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _formatTime(notification['time']),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                // Actions
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.grey.shade400, size: 20),
                  onSelected: (value) {
                    switch (value) {
                      case 'mark_read':
                        _markAsRead(notification['id']);
                        break;
                      case 'delete':
                        _deleteNotification(notification['id']);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (!notification['isRead'])
                      const PopupMenuItem(
                        value: 'mark_read',
                        child: Row(
                          children: [
                            Icon(Icons.mark_email_read, size: 18),
                            SizedBox(width: 8),
                            Text('Mark as read'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18),
                          SizedBox(width: 8),
                          Text('Delete'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${time.day}/${time.month}/${time.year}';
    }
  }

  void _handleNotificationTap(Map<String, dynamic> notification) {
    // Mark as read when tapped
    if (!notification['isRead']) {
      _markAsRead(notification['id']);
    }

    // Show options dialog
    _showNotificationOptionsDialog(notification);
  }

  void _showNotificationOptionsDialog(Map<String, dynamic> notification) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                notification['icon'] as IconData,
                color: notification['color'] as Color,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  notification['title'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'What would you like to do?',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 16),
              // Preview of the message (truncated)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  notification['message'].length > 100
                      ? '${notification['message'].substring(0, 100)}...'
                      : notification['message'],
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Close',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _showFullMessageDialog(notification);
              },
              icon: const Icon(Icons.article, size: 18),
              label: const Text('Go to Full Text'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showFullMessageDialog(Map<String, dynamic> notification) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                notification['icon'] as IconData,
                color: notification['color'] as Color,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  notification['title'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Time stamp
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _formatTime(notification['time']),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Full message
                Container(
                  width: double.maxFinite,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    notification['message'],
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Additional info if available
                if (notification['additionalInfo'] != null) ...[
                  Text(
                    'Additional Information:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.maxFinite,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      notification['additionalInfo'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Close',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            if (notification['type'] == 'booking')
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _handleBookingAction(notification);
                },
                icon: const Icon(Icons.event, size: 18),
                label: const Text('View Booking'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  foregroundColor: Colors.white,
                ),
              ),
            if (notification['type'] == 'message')
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _handleMessageAction(notification);
                },
                icon: const Icon(Icons.chat, size: 18),
                label: const Text('Reply'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade700,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        );
      },
    );
  }

  void _handleBookingAction(Map<String, dynamic> notification) {
    // TODO: Navigate to booking details
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Booking details coming soon!')),
    );
  }

  void _handleMessageAction(Map<String, dynamic> notification) {
    // TODO: Navigate to chat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chat feature coming soon!')),
    );
  }

  void _markAsRead(String notificationId) {
    setState(() {
      final index = _notifications.indexWhere((n) => n['id'] == notificationId);
      if (index != -1) {
        _notifications[index]['isRead'] = true;
        _unreadCount = _notifications.where((n) => !n['isRead']).length;
      }
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification['isRead'] = true;
      }
      _unreadCount = 0;
    });
  }

  void _deleteNotification(String notificationId) {
    setState(() {
      _notifications.removeWhere((n) => n['id'] == notificationId);
      _unreadCount = _notifications.where((n) => !n['isRead']).length;
    });
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _notifications.clear();
                _unreadCount = 0;
              });
              Navigator.pop(context);
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification Settings',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('Push Notifications'),
              trailing: Switch(
                value: true,
                onChanged: (value) {
                  // TODO: Implement notification toggle
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('Email Notifications'),
              trailing: Switch(
                value: false,
                onChanged: (value) {
                  // TODO: Implement email toggle
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('Session Reminders'),
              trailing: Switch(
                value: true,
                onChanged: (value) {
                  // TODO: Implement reminder toggle
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}