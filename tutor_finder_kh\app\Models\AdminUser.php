<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AdminUser extends Authenticatable
{
    use Has<PERSON><PERSON>Tokens, HasFactory, Notifiable;

    protected $fillable = [
        'username',
        'email',
        'name',
        'password',
        'role',
        'is_active',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'password' => 'hashed',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime',
    ];

    /**
     * Get the reports handled by this admin.
     */
    public function handledReports(): HasMany
    {
        return $this->hasMany(Report::class, 'handled_by');
    }

    /**
     * Get the documents verified by this admin.
     */
    public function verifiedDocuments(): HasMany
    {
        return $this->hasMany(Document::class, 'verified_by');
    }

    /**
     * Check if admin has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if admin is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if admin can perform a specific action.
     */
    public function canPerform(string $action): bool
    {
        $permissions = [
            'super_admin' => ['*'],
            'admin' => [
                'manage_tutors',
                'manage_students',
                'handle_reports',
                'verify_documents',
                'view_analytics',
            ],
            'moderator' => [
                'handle_reports',
                'moderate_reviews',
            ],
        ];

        $rolePermissions = $permissions[$this->role] ?? [];
        
        return in_array('*', $rolePermissions) || in_array($action, $rolePermissions);
    }

    /**
     * Scope a query to only include active admins.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by role.
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }
}
