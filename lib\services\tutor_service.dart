import '../models/api_response.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class TutorService {
  static final TutorService _instance = TutorService._internal();
  factory TutorService() => _instance;
  TutorService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get all tutors with filtering and pagination

  Future<ApiResponse<PaginatedResponse<Tutor>>> getTutors({
    String? search,
    int? categoryId,
    String? location,
    bool? isOnline,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? perPage,
    
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (search != null && search.isNotEmpty) queryParameters['search'] = search;
    if (categoryId != null) queryParameters['category_id'] = categoryId;
    if (location != null && location.isNotEmpty) queryParameters['location'] = location;
    if (isOnline != null) queryParameters['is_online'] = isOnline;
    if (minPrice != null) queryParameters['min_price'] = minPrice;
    if (maxPrice != null) queryParameters['max_price'] = maxPrice;
    if (minRating != null) queryParameters['min_rating'] = minRating;
    if (sortBy != null) queryParameters['sort_by'] = sortBy;
    if (sortOrder != null) queryParameters['sort_order'] = sortOrder;
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;

    return await _apiClient.get<PaginatedResponse<Tutor>>(
      ApiConfig.tutorsList,
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse<Tutor>.fromJson(
        data as Map<String, dynamic>,
        (json) => Tutor.fromJson(json as Map<String, dynamic>),
      ),
    );
  }

  // Get featured tutors
  Future<ApiResponse<List<Tutor>>> getFeaturedTutors() async {
    return await _apiClient.get<List<Tutor>>(
      ApiConfig.featuredTutors,
      fromJson: (data) => (data as List)
          .map((item) => Tutor.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  // Search tutors with advanced filters
  Future<ApiResponse<PaginatedResponse<Tutor>>> searchTutors({
    String? query,
    List<int>? categoryIds,
    String? location,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    bool? isOnline,
    String? sortBy,
    String? sortOrder,
    int? page,
    int? perPage,
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (query != null && query.isNotEmpty) queryParameters['query'] = query;
    if (categoryIds != null && categoryIds.isNotEmpty) {
      queryParameters['category_ids'] = categoryIds;
    }
    if (location != null && location.isNotEmpty) queryParameters['location'] = location;
    if (minPrice != null) queryParameters['min_price'] = minPrice;
    if (maxPrice != null) queryParameters['max_price'] = maxPrice;
    if (minRating != null) queryParameters['min_rating'] = minRating;
    if (isOnline != null) queryParameters['is_online'] = isOnline;
    if (sortBy != null) queryParameters['sort_by'] = sortBy;
    if (sortOrder != null) queryParameters['sort_order'] = sortOrder;
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;

    return await _apiClient.get<PaginatedResponse<Tutor>>(
      ApiConfig.searchTutors,
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse<Tutor>.fromJson(
        data as Map<String, dynamic>,
        (json) => Tutor.fromJson(json as Map<String, dynamic>),
      ),
    );
  }

  // Get specific tutor by ID
  Future<ApiResponse<Tutor>> getTutorById(int id) async {
    return await _apiClient.get<Tutor>(
      ApiConfig.tutorDetails(id),
      fromJson: (data) => Tutor.fromJson(data as Map<String, dynamic>),
    );
  }

  // Get tutor availability
  Future<ApiResponse<Map<String, dynamic>>> getTutorAvailability(int id) async {
    return await _apiClient.get<Map<String, dynamic>>(
      ApiConfig.tutorAvailability(id),
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  // Get tutors by category
  Future<ApiResponse<Map<String, dynamic>>> getTutorsByCategory(int categoryId) async {
    return await _apiClient.get<Map<String, dynamic>>(
      ApiConfig.tutorsByCategory(categoryId),
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  // Helper methods for filtering
  static List<Tutor> filterTutorsByRating(List<Tutor> tutors, double minRating) {
    return tutors.where((tutor) => tutor.rating >= minRating).toList();
  }

  static List<Tutor> filterTutorsByPrice(List<Tutor> tutors, double? minPrice, double? maxPrice) {
    return tutors.where((tutor) {
      final price = tutor.hourlyRateValue;
      if (minPrice != null && price < minPrice) return false;
      if (maxPrice != null && price > maxPrice) return false;
      return true;
    }).toList();
  }

  static List<Tutor> filterTutorsByOnlineStatus(List<Tutor> tutors, bool isOnline) {
    return tutors.where((tutor) => tutor.isOnline == isOnline).toList();
  }

  static List<Tutor> filterTutorsByLocation(List<Tutor> tutors, String location) {
    return tutors.where((tutor) {
      return tutor.location?.toLowerCase().contains(location.toLowerCase()) ?? false;
    }).toList();
  }

  static List<Tutor> searchTutorsByName(List<Tutor> tutors, String query) {
    final lowerQuery = query.toLowerCase();
    return tutors.where((tutor) {
      return (tutor.name?.toLowerCase().contains(lowerQuery) ?? false) ||
             (tutor.description?.toLowerCase().contains(lowerQuery) ?? false) ||
             (tutor.qualifications?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  static List<Tutor> sortTutors(List<Tutor> tutors, String sortBy, {bool ascending = true}) {
    switch (sortBy) {
      case 'rating':
        tutors.sort((a, b) => ascending 
            ? a.rating.compareTo(b.rating)
            : b.rating.compareTo(a.rating));
        break;
      case 'price':
        tutors.sort((a, b) => ascending 
            ? a.hourlyRateValue.compareTo(b.hourlyRateValue)
            : b.hourlyRateValue.compareTo(a.hourlyRateValue));
        break;
      case 'experience':
        tutors.sort((a, b) => ascending 
            ? (a.experienceYears ?? 0).compareTo(b.experienceYears ?? 0)
            : (b.experienceYears ?? 0).compareTo(a.experienceYears ?? 0));
        break;
      case 'name':
        tutors.sort((a, b) => ascending
            ? (a.name ?? '').compareTo(b.name ?? '')
            : (b.name ?? '').compareTo(a.name ?? ''));
        break;
      default:
        // Default sort by rating (highest first)
        tutors.sort((a, b) => b.rating.compareTo(a.rating));
    }
    return tutors;
  }

  // Get tutor statistics
  static Map<String, dynamic> getTutorStats(List<Tutor> tutors) {
    if (tutors.isEmpty) {
      return {
        'total': 0,
        'averageRating': 0.0,
        'averagePrice': 0.0,
        'onlineCount': 0,
        'offlineCount': 0,
      };
    }

    final totalRating = tutors.fold<double>(0, (sum, tutor) => sum + tutor.rating);
    final totalPrice = tutors.fold<double>(0, (sum, tutor) => sum + tutor.hourlyRateValue);
    final onlineCount = tutors.where((tutor) => tutor.isOnline ?? false).length;

    return {
      'total': tutors.length,
      'averageRating': totalRating / tutors.length,
      'averagePrice': totalPrice / tutors.length,
      'onlineCount': onlineCount,
      'offlineCount': tutors.length - onlineCount,
    };
  }
}
