<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Clear existing data
        DB::table('tutor_categories')->delete();
        DB::table('categories')->delete();
        DB::table('tutors')->delete();
        DB::table('users')->where('user_type', '!=', 'admin')->delete();

        // Insert categories (simple structure)
        $categories = [
            'Mathematics', 'English', 'Physics', 'Chemistry', 'Biology', 
            'History', 'Geography', 'Computer Science', 'Programming', 
            'Web Development', 'Economics', 'Business Studies', 'Art', 
            'Music', 'French', 'Chinese', 'Khmer Literature'
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert(['name' => $category]);
        }

        // Insert sample users (tutors)
        $users = [
            [
                'name' => '<PERSON>. <PERSON>',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456789',
                'date_of_birth' => '1985-03-15',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Prof. Michael Chen',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456790',
                'date_of_birth' => '1980-07-22',
                'gender' => 'male',
                'address' => 'Siem Reap, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Emily Davis',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456791',
                'date_of_birth' => '1988-11-08',
                'gender' => 'female',
                'address' => 'Battambang, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. James Wilson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456792',
                'date_of_birth' => '1982-05-30',
                'gender' => 'male',
                'address' => 'Kampong Cham, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Lisa Park',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456793',
                'date_of_birth' => '1990-09-12',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('users')->insert($users);

        // Get the inserted user IDs
        $userIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');

        // Insert basic tutor profiles (only with existing columns)
        $tutors = [
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Mathematics', 'Statistics', 'Calculus']),
                'experience_years' => 8,
                'education_level' => 'PhD',
                'hourly_rate' => 25.00,
                'bio' => 'Expert in calculus and algebra with PhD in Mathematics.',
                'qualifications' => json_encode(['PhD Mathematics', 'Teaching Certificate']),
                'languages' => json_encode(['English', 'Khmer']),
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.9,
                'reviews_count' => 127,
                'total_sessions' => 450,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Physics', 'Mathematics']),
                'experience_years' => 12,
                'education_level' => 'PhD',
                'hourly_rate' => 30.00,
                'bio' => 'Physics professor with extensive research experience.',
                'qualifications' => json_encode(['PhD Physics', 'Research Publications']),
                'languages' => json_encode(['English', 'Chinese', 'Khmer']),
                'location' => 'Siem Reap',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 89,
                'total_sessions' => 320,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['English', 'Literature']),
                'experience_years' => 6,
                'education_level' => 'Masters',
                'hourly_rate' => 20.00,
                'bio' => 'English literature specialist with focus on creative writing.',
                'qualifications' => json_encode(['MA English Literature', 'TESOL Certificate']),
                'languages' => json_encode(['English', 'Khmer']),
                'location' => 'Battambang',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.7,
                'reviews_count' => 156,
                'total_sessions' => 280,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Chemistry', 'Biology']),
                'experience_years' => 10,
                'education_level' => 'PhD',
                'hourly_rate' => 28.00,
                'bio' => 'Chemistry and biology expert with laboratory research background.',
                'qualifications' => json_encode(['PhD Chemistry', 'Lab Research Experience']),
                'languages' => json_encode(['English', 'Khmer']),
                'location' => 'Kampong Cham',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.6,
                'reviews_count' => 98,
                'total_sessions' => 210,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Biology', 'Environmental Science']),
                'experience_years' => 5,
                'education_level' => 'Masters',
                'hourly_rate' => 22.00,
                'bio' => 'Biology teacher with passion for environmental conservation.',
                'qualifications' => json_encode(['MS Biology', 'Environmental Science Certificate']),
                'languages' => json_encode(['English', 'Khmer', 'Korean']),
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 134,
                'total_sessions' => 195,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('tutors')->insert($tutors);

        // Link tutors to categories
        $categoryIds = DB::table('categories')->pluck('id', 'name');
        $tutorIds = DB::table('tutors')->pluck('id', 'user_id');

        $tutorCategories = [
            // Dr. Sarah Johnson - Mathematics
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Mathematics']],
            
            // Prof. Michael Chen - Physics, Mathematics
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Physics']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Mathematics']],
            
            // Ms. Emily Davis - English
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['English']],
            
            // Dr. James Wilson - Chemistry, Biology
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Chemistry']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Biology']],
            
            // Ms. Lisa Park - Biology
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Biology']],
        ];

        DB::table('tutor_categories')->insert($tutorCategories);

        $this->command->info('Basic data seeded successfully!');
        $this->command->info('Categories: ' . count($categories));
        $this->command->info('Tutors: ' . count($tutors));
        $this->command->info('Tutor-Category relationships: ' . count($tutorCategories));
    }
}
