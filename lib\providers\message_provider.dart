import 'package:flutter/foundation.dart';

import '../models/message.dart';
import '../services/message_service.dart';

enum MessageState {
  initial,
  loading,
  loaded,
  error,
}

class MessageProvider extends ChangeNotifier {
  final MessageService _messageService = MessageService();
  
  MessageState _state = MessageState.initial;
  String? _errorMessage;
  final List<Message> _messages = [];
  List<Conversation> _conversations = [];
  final Map<String, List<Message>> _conversationMessages = {};
  bool _isLoading = false;
  bool _isSending = false;
  int _unreadCount = 0;

  // Getters
  MessageState get state => _state;
  String? get errorMessage => _errorMessage;
  List<Message> get messages => _messages;
  List<Conversation> get conversations => _conversations;
  bool get isLoading => _isLoading;
  bool get isSending => _isSending;
  int get unreadCount => _unreadCount;

  // Get messages for a specific conversation
  List<Message> getConversationMessages(int otherUserId, String otherUserType) {
    final key = '${otherUserId}_$otherUserType';
    return _conversationMessages[key] ?? [];
  }

  // Load conversations
  Future<void> loadConversations() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _messageService.getConversations();
      if (response.isSuccess && response.data != null) {
        _conversations = response.data!;
        _updateUnreadCount();
        _setState(MessageState.loaded);
      } else {
        // Fallback to mock data when API fails
        _loadMockConversations();
      }
    } catch (e) {
      // Fallback to mock data on network error
      _loadMockConversations();
    } finally {
      _setLoading(false);
    }
  }

  // Load mock conversations for testing when API is not available
  void _loadMockConversations() {
    _conversations = [
      Conversation(
        id: 1,
        otherUserId: 1,
        otherUserType: 'tutor',
        otherUserName: 'Dr. Sarah Johnson',
        otherUserAvatar: 'assets/images/avatar1.png',
        lastMessage: 'Hello! I\'m available for math tutoring sessions.',
        lastMessageAt: DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        unreadCount: 2,
      ),
      Conversation(
        id: 2,
        otherUserId: 2,
        otherUserType: 'tutor',
        otherUserName: 'Prof. Michael Chen',
        otherUserAvatar: 'assets/images/avatar2.png',
        lastMessage: 'When would you like to schedule our physics session?',
        lastMessageAt: DateTime.now().subtract(const Duration(hours: 5)).toIso8601String(),
        unreadCount: 0,
      ),
      Conversation(
        id: 3,
        otherUserId: 3,
        otherUserType: 'tutor',
        otherUserName: 'Ms. Emily Davis',
        otherUserAvatar: 'assets/images/avatar3.png',
        lastMessage: 'Great progress in our last English lesson!',
        lastMessageAt: DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        unreadCount: 1,
      ),
    ];
    _updateUnreadCount();
    _setState(MessageState.loaded);
  }

  // Load conversation messages
  Future<void> loadConversation(int otherUserId, String otherUserType) async {
    final key = '${otherUserId}_$otherUserType';
    _setLoading(true);
    _clearError();

    try {
      final response = await _messageService.getConversation(otherUserId, otherUserType);
      if (response.isSuccess && response.data != null) {
        _conversationMessages[key] = response.data!;
        _setState(MessageState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load messages');
      }
    } catch (e) {
      _setError('Error loading messages: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Send message
  Future<bool> sendMessage(String receiverId, String receiverType, String message) async {
    _setSending(true);
    _clearError();

    try {
      final request = SendMessageRequest(
        receiverId: int.parse(receiverId),
        receiverType: receiverType,
        message: message,
      );

      final response = await _messageService.sendMessage(request);
      if (response.isSuccess && response.data != null) {
        final newMessage = response.data!;
        final key = '${receiverId}_$receiverType';

        // Add message to conversation
        if (_conversationMessages[key] != null) {
          _conversationMessages[key]!.add(newMessage);
        } else {
          _conversationMessages[key] = [newMessage];
        }

        // Update conversations list
        await loadConversations();

        notifyListeners();
        return true;
      } else {
        // Fallback: simulate successful message sending
        return _simulateMessageSending(receiverId, receiverType, message);
      }
    } catch (e) {
      // Fallback: simulate successful message sending on error
      return _simulateMessageSending(receiverId, receiverType, message);
    } finally {
      _setSending(false);
    }
  }

  // Simulate message sending when API is not available
  bool _simulateMessageSending(String receiverId, String receiverType, String messageText) {
    try {
      final key = '${receiverId}_$receiverType';

      // Create a mock message
      final newMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch, // Use timestamp as ID
        senderId: 999, // Mock current user ID
        receiverId: int.parse(receiverId),
        senderType: 'student', // Assuming current user is student
        receiverType: receiverType,
        message: messageText,
        messageType: 'text',
        isRead: false,
        createdAt: DateTime.now().toIso8601String(),
      );

      // Add message to conversation
      if (_conversationMessages[key] != null) {
        _conversationMessages[key]!.add(newMessage);
      } else {
        _conversationMessages[key] = [newMessage];
      }

      // Update the conversation in the list
      final conversationIndex = _conversations.indexWhere(
        (conv) => conv.otherUserId.toString() == receiverId && conv.otherUserType == receiverType
      );

      if (conversationIndex != -1) {
        // Update existing conversation
        final updatedConversation = Conversation(
          id: _conversations[conversationIndex].id,
          otherUserId: _conversations[conversationIndex].otherUserId,
          otherUserType: _conversations[conversationIndex].otherUserType,
          otherUserName: _conversations[conversationIndex].otherUserName,
          otherUserAvatar: _conversations[conversationIndex].otherUserAvatar,
          lastMessage: messageText,
          lastMessageAt: DateTime.now().toIso8601String(),
          unreadCount: 0, // Reset since we just sent a message
        );
        _conversations[conversationIndex] = updatedConversation;
      }

      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Mark messages as read
  Future<void> markAsRead(int conversationId) async {
    try {
      final response = await _messageService.markAsRead(conversationId);
      if (response.isSuccess) {
        // Update local conversation
        final conversationIndex = _conversations.indexWhere((c) => c.id == conversationId);
        if (conversationIndex != -1) {
          _conversations[conversationIndex] = _conversations[conversationIndex].copyWith(unreadCount: 0);
          _updateUnreadCount();
          notifyListeners();
        }
      }
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  // Get unread count
  Future<void> loadUnreadCount() async {
    try {
      final response = await _messageService.getUnreadCount();
      if (response.isSuccess && response.data != null) {
        _unreadCount = response.data!.unreadCount;
        notifyListeners();
      }
    } catch (e) {
      print('Error loading unread count: $e');
    }
  }

  // Initialize
  Future<void> initialize() async {
    await Future.wait([
      loadConversations(),
      loadUnreadCount(),
    ]);
  }

  // Refresh
  Future<void> refresh() async {
    await initialize();
  }

  // Private methods
  void _setState(MessageState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setSending(bool sending) {
    _isSending = sending;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = MessageState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == MessageState.error) {
      _state = _conversations.isNotEmpty ? MessageState.loaded : MessageState.initial;
    }
    notifyListeners();
  }

  void _updateUnreadCount() {
    _unreadCount = _conversations.fold(0, (sum, conversation) => sum + conversation.unreadCount);
  }

  // Clear all data
  void clearAllData() {
    _state = MessageState.initial;
    _errorMessage = null;
    _messages.clear();
    _conversations.clear();
    _conversationMessages.clear();
    _isLoading = false;
    _isSending = false;
    _unreadCount = 0;
    notifyListeners();
  }
}
