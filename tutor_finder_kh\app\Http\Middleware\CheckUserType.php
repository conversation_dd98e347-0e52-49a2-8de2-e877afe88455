<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Student;
use App\Models\Tutor;

class CheckUserType
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $userType): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        $actualUserType = $user instanceof Student ? 'student' : 'tutor';
        
        if ($actualUserType !== $userType) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. This endpoint is only available for ' . $userType . 's.'
            ], 403);
        }

        return $next($request);
    }
}
