import 'package:flutter/material.dart';
import 'package:practice_homework/config/api_config.dart';
import 'package:provider/provider.dart';
import 'services/api_client.dart';
import 'providers/auth_provider.dart';
import 'providers/tutor_provider.dart';
import 'providers/categories.dart';
import 'providers/favorite_provider.dart';
import 'providers/message_provider.dart';
import 'providers/group_provider.dart';
import 'providers/post_provider.dart';

import 'config/ui_constants.dart';
import 'routes/routes_screen.dart';
import 'package:http/http.dart' as http;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize API client
  ApiClient().initialize();
  
  // Test backend connectivity
  print('Testing backend connectivity...');
  try {
    final response = await http.get(Uri.parse('${ApiConfig.baseUrl}/health'));
    print('Backend health check: ${response.statusCode} - ${response.body}');
  } catch (e) {
    print('Backend connectivity test failed: $e');
  }
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});


  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => TutorProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => FavoriteProvider()),
        ChangeNotifierProvider(create: (_) => MessageProvider()),
        ChangeNotifierProvider(create: (_) => GroupProvider()),
        ChangeNotifierProvider(create: (_) => PostProvider()),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'TutorFinder',
        theme: AppTheme.lightTheme,
        initialRoute: AppRoute.splashScreen,
        onGenerateRoute: AppRoute.onGenerateRoute,
        navigatorKey: AppRoute.key,
      ),
    );
  }


}
