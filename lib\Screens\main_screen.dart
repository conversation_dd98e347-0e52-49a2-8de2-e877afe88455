import 'package:flutter/material.dart';
import 'fav_screen.dart';
import 'home_screen.dart';
import 'more_screen.dart';
import 'conversations_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  List<Widget> screenList = [
    const HomeScreen(),
    const FavoriteScreen(),
    const ConversationsScreen(),
    const MoreScreen()
  ];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _body,
      bottomNavigationBar: _bottomNav,
    );
  }

  Widget get _body {
    // return screenList.elementAt(_currentIndex); two type for use
    return screenList[_currentIndex];
  }

  Widget get _bottomNav {
    final items = [
      const BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
      const BottomNavigationBarItem(icon: Icon(Icons.favorite), label: 'Favorite'),
      const BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Messages'),
      const BottomNavigationBarItem(icon: Icon(Icons.more_vert), label: 'More')
    ];
    return BottomNavigationBar(
      items: items,
      selectedItemColor: Colors.red,
      unselectedItemColor: Colors.blueGrey,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
    );
  }
}
