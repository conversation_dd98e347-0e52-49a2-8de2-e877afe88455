<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class MinimalDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Clear existing data
        DB::table('tutor_categories')->delete();
        DB::table('categories')->delete();
        DB::table('tutors')->delete();
        DB::table('users')->where('user_type', '!=', 'admin')->delete();

        // Insert categories (simple structure)
        $categories = [
            'Mathematics', 'English', 'Physics', 'Chemistry', 'Biology', 
            'History', 'Geography', 'Computer Science', 'Programming', 
            'Web Development', 'Economics', 'Business Studies', 'Art', 
            'Music', 'French', 'Chinese', 'Khmer Literature'
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert(['name' => $category]);
        }

        // Insert sample users (tutors)
        $users = [
            [
                'name' => '<PERSON>. <PERSON>',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456789',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Prof. Michael Chen',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456790',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Emily Davis',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456791',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. James Wilson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456792',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Lisa Park',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456793',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('users')->insert($users);

        // Get the inserted user IDs
        $userIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');

        // Insert minimal tutor profiles (only essential columns)
        $tutors = [
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Mathematics']),
                'experience_years' => 8,
                'hourly_rate' => 25.00,
                'bio' => 'Expert in Mathematics',
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Physics']),
                'experience_years' => 12,
                'hourly_rate' => 30.00,
                'bio' => 'Physics professor',
                'location' => 'Siem Reap',
                'is_verified' => true,
                'is_available' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['English']),
                'experience_years' => 6,
                'hourly_rate' => 20.00,
                'bio' => 'English teacher',
                'location' => 'Battambang',
                'is_verified' => true,
                'is_available' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Chemistry']),
                'experience_years' => 10,
                'hourly_rate' => 28.00,
                'bio' => 'Chemistry expert',
                'location' => 'Kampong Cham',
                'is_verified' => true,
                'is_available' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Biology']),
                'experience_years' => 5,
                'hourly_rate' => 22.00,
                'bio' => 'Biology teacher',
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('tutors')->insert($tutors);

        // Link tutors to categories
        $categoryIds = DB::table('categories')->pluck('id', 'name');
        $tutorIds = DB::table('tutors')->pluck('id', 'user_id');

        $tutorCategories = [
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Mathematics']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Physics']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['English']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Chemistry']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Biology']],
        ];

        DB::table('tutor_categories')->insert($tutorCategories);

        $this->command->info('Minimal data seeded successfully!');
        $this->command->info('Categories: ' . count($categories));
        $this->command->info('Tutors: ' . count($tutors));
        $this->command->info('Tutor-Category relationships: ' . count($tutorCategories));
    }
}
