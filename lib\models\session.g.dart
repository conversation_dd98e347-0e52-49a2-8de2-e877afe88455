// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Session _$SessionFromJson(Map<String, dynamic> json) => Session(
      id: (json['id'] as num).toInt(),
      tutorId: (json['tutor_id'] as num).toInt(),
      studentId: (json['student_id'] as num).toInt(),
      sessionDate: json['session_date'] as String,
      durationMinutes: (json['duration_minutes'] as num).toInt(),
      price: json['price'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      meetingLink: json['meeting_link'] as String?,
      sessionType: json['session_type'] as String,
      location: json['location'] as String?,
      startedAt: json['started_at'] as String?,
      endedAt: json['ended_at'] as String?,
      cancellationReason: json['cancellation_reason'] as String?,
      tutor: json['tutor'] == null
          ? null
          : Tutor.fromJson(json['tutor'] as Map<String, dynamic>),
      student: json['student'] == null
          ? null
          : Student.fromJson(json['student'] as Map<String, dynamic>),
      chats: (json['chats'] as List<dynamic>?)
          ?.map((e) => Chat.fromJson(e as Map<String, dynamic>))
          .toList(),
      review: json['review'] == null
          ? null
          : TutorReview.fromJson(json['review'] as Map<String, dynamic>),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$SessionToJson(Session instance) => <String, dynamic>{
      'id': instance.id,
      'tutor_id': instance.tutorId,
      'student_id': instance.studentId,
      'session_date': instance.sessionDate,
      'duration_minutes': instance.durationMinutes,
      'price': instance.price,
      'status': instance.status,
      'notes': instance.notes,
      'meeting_link': instance.meetingLink,
      'session_type': instance.sessionType,
      'location': instance.location,
      'started_at': instance.startedAt,
      'ended_at': instance.endedAt,
      'cancellation_reason': instance.cancellationReason,
      'tutor': instance.tutor,
      'student': instance.student,
      'chats': instance.chats,
      'review': instance.review,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
      id: (json['id'] as num).toInt(),
      sessionId: (json['session_id'] as num).toInt(),
      senderId: (json['sender_id'] as num).toInt(),
      senderType: json['sender_type'] as String,
      message: json['message'] as String,
      messageType: json['message_type'] as String,
      filePath: json['file_path'] as String?,
      isRead: json['is_read'] as bool,
      readAt: json['read_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
      'id': instance.id,
      'session_id': instance.sessionId,
      'sender_id': instance.senderId,
      'sender_type': instance.senderType,
      'message': instance.message,
      'message_type': instance.messageType,
      'file_path': instance.filePath,
      'is_read': instance.isRead,
      'read_at': instance.readAt,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
