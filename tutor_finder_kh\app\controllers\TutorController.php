<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Tutor;
use App\Models\TutorPost;
use App\Models\Category;

class TutorController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Tutor::where('is_active', true)
                     ->where('is_verified', true);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('bio', 'like', "%{$search}%")
                  ->orWhere('education', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->has('category_id')) {
            $categoryId = $request->category_id;
            $query->whereJsonContains('subjects', $categoryId);
        }

        // Filter by location
        if ($request->has('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by online availability
        if ($request->has('is_online')) {
            $query->where('is_online', $request->boolean('is_online'));
        }

        // Filter by rating
        if ($request->has('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        // Filter by hourly rate
        if ($request->has('max_rate')) {
            $query->where('hourly_rate', '<=', $request->max_rate);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'rating');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'rating':
                $query->orderBy('rating', $sortOrder);
                break;
            case 'price':
                $query->orderBy('hourly_rate', $sortOrder);
                break;
            case 'experience':
                $query->orderBy('experience_years', $sortOrder);
                break;
            case 'reviews':
                $query->orderBy('total_reviews', $sortOrder);
                break;
            default:
                $query->orderBy('rating', 'desc');
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $tutors = $query->paginate($perPage);

        // Load categories for each tutor
        $tutors->getCollection()->transform(function ($tutor) {
            if ($tutor->subjects) {
                $tutor->categories = Category::whereIn('id', $tutor->subjects)->get();
            }
            return $tutor;
        });

        return response()->json([
            'success' => true,
            'data' => $tutors->items(),
            'pagination' => [
                'current_page' => $tutors->currentPage(),
                'last_page' => $tutors->lastPage(),
                'per_page' => $tutors->perPage(),
                'total' => $tutors->total(),
                'has_more' => $tutors->hasMorePages()
            ]
        ]);
    }

    public function show($id): JsonResponse
    {
        $tutor = Tutor::where('is_active', true)
                     ->where('is_verified', true)
                     ->find($id);

        if (!$tutor) {
            return response()->json([
                'success' => false,
                'message' => 'Tutor not found'
            ], 404);
        }

        // Load categories
        if ($tutor->subjects) {
            $tutor->categories = Category::whereIn('id', $tutor->subjects)->get();
        }

        // Load recent posts
        $tutor->recent_posts = TutorPost::where('tutor_id', $tutor->id)
                                      ->where('status', 'approved')
                                      ->where('is_active', true)
                                      ->latest()
                                      ->take(5)
                                      ->get();

        return response()->json([
            'success' => true,
            'data' => $tutor
        ]);
    }

    public function featured(): JsonResponse
    {
        $tutors = Tutor::where('is_active', true)
                      ->where('is_verified', true)
                      ->where('rating', '>=', 4.5)
                      ->orderBy('rating', 'desc')
                      ->orderBy('total_reviews', 'desc')
                      ->take(10)
                      ->get();

        // Load categories for each tutor
        $tutors->transform(function ($tutor) {
            if ($tutor->subjects) {
                $tutor->categories = Category::whereIn('id', $tutor->subjects)->get();
            }
            return $tutor;
        });

        return response()->json([
            'success' => true,
            'data' => $tutors
        ]);
    }

    public function byCategory($categoryId): JsonResponse
    {
        $category = Category::find($categoryId);
        
        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }

        $tutors = Tutor::where('is_active', true)
                      ->where('is_verified', true)
                      ->whereJsonContains('subjects', $categoryId)
                      ->orderBy('rating', 'desc')
                      ->get();

        // Load categories for each tutor
        $tutors->transform(function ($tutor) {
            if ($tutor->subjects) {
                $tutor->categories = Category::whereIn('id', $tutor->subjects)->get();
            }
            return $tutor;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'category' => $category,
                'tutors' => $tutors
            ]
        ]);
    }

    public function createPost(Request $request): JsonResponse
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'hourly_rate' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'is_online' => 'boolean',
            'availability' => 'nullable|array'
        ]);

        $post = TutorPost::create([
            'tutor_id' => auth()->id(),
            'category_id' => $request->category_id,
            'title' => $request->title,
            'description' => $request->description,
            'hourly_rate' => $request->hourly_rate,
            'location' => $request->location,
            'is_online' => $request->boolean('is_online'),
            'availability' => $request->availability,
            'status' => 'pending'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Post created successfully and is pending approval',
            'data' => $post
        ], 201);
    }
}
