<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * Get all categories with tutor counts.
     */
    public function index(): JsonResponse
    {
        $categories = Category::withCount([
                'tutors as tutor_count' => function ($query) {
                    $query->where('status', 'approved');
                }
            ])
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get a specific category with its tutors.
     */
    public function show(int $id): JsonResponse
    {
        $category = Category::with([
                'tutors' => function ($query) {
                    $query->approved()
                          ->withCount(['reviews as review_count' => function ($q) {
                              $q->where('is_approved', true);
                          }])
                          ->withAvg(['reviews as average_rating' => function ($q) {
                              $q->where('is_approved', true);
                          }], 'rating')
                          ->orderBy('average_rating', 'desc');
                }
            ])
            ->find($id);

        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }

    /**
     * Get popular categories (with most tutors).
     */
    public function popular(): JsonResponse
    {
        $categories = Category::withCount([
                'tutors as tutor_count' => function ($query) {
                    $query->where('status', 'approved');
                }
            ])
            ->having('tutor_count', '>', 0)
            ->orderBy('tutor_count', 'desc')
            ->limit(8)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }
}
