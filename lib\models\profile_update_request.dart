import 'package:json_annotation/json_annotation.dart';

part 'profile_update_request.g.dart';

@JsonSerializable()
class ProfileUpdateRequest {
  final String? name;
  final String? phone;
  @J<PERSON><PERSON><PERSON>(name: 'date_of_birth')
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  @J<PERSON><PERSON><PERSON>(name: 'tutor_data')
  final TutorUpdateData? tutorData;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'student_data')
  final StudentUpdateData? studentData;

  ProfileUpdateRequest({
    this.name,
    this.phone,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.tutorData,
    this.studentData,
  });

  factory ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileUpdateRequestToJson(this);
}

@JsonSerializable()
class TutorUpdateData {
  final List<String>? subjects;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'experience_years')
  final int? experienceYears;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'education_level')
  final String? educationLevel;
  @J<PERSON><PERSON><PERSON>(name: 'hourly_rate')
  final double? hourlyRate;
  final String? bio;
  final List<String>? qualifications;
  final List<String>? languages;
  final Map<String, dynamic>? availability;
  final String? location;
  @JsonKey(name: 'teaching_style')
  final String? teachingStyle;
  final List<String>? specializations;

  TutorUpdateData({
    this.subjects,
    this.experienceYears,
    this.educationLevel,
    this.hourlyRate,
    this.bio,
    this.qualifications,
    this.languages,
    this.availability,
    this.location,
    this.teachingStyle,
    this.specializations,
  });

  factory TutorUpdateData.fromJson(Map<String, dynamic> json) =>
      _$TutorUpdateDataFromJson(json);
  Map<String, dynamic> toJson() => _$TutorUpdateDataToJson(this);
}

@JsonSerializable()
class StudentUpdateData {
  @JsonKey(name: 'grade_level')
  final String? gradeLevel;
  final String? school;
  @JsonKey(name: 'learning_preferences')
  final List<String>? learningPreferences;
  @JsonKey(name: 'subjects_of_interest')
  final List<String>? subjectsOfInterest;
  @JsonKey(name: 'preferred_learning_style')
  final String? preferredLearningStyle;
  final String? goals;
  final Map<String, dynamic>? availability;
  @JsonKey(name: 'budget_range')
  final String? budgetRange;

  StudentUpdateData({
    this.gradeLevel,
    this.school,
    this.learningPreferences,
    this.subjectsOfInterest,
    this.preferredLearningStyle,
    this.goals,
    this.availability,
    this.budgetRange,
  });

  factory StudentUpdateData.fromJson(Map<String, dynamic> json) =>
      _$StudentUpdateDataFromJson(json);
  Map<String, dynamic> toJson() => _$StudentUpdateDataToJson(this);
}

@JsonSerializable()
class ProfileUpdateResponse {
  final bool success;
  final String message;
  final ProfileUpdateData? data;

  ProfileUpdateResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory ProfileUpdateResponse.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileUpdateResponseToJson(this);
}

@JsonSerializable()
class ProfileUpdateData {
  final dynamic user;
  final dynamic profile;
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;

  ProfileUpdateData({
    required this.user,
    this.profile,
    this.avatarUrl,
  });

  factory ProfileUpdateData.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateDataFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileUpdateDataToJson(this);
}
