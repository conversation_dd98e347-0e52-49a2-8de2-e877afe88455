import '../models/api_response.dart';
import '../models/favorite.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class FavoriteService {
  static final FavoriteService _instance = FavoriteService._internal();
  factory FavoriteService() => _instance;
  FavoriteService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get user's favorite tutors
  Future<ApiResponse<PaginatedResponse<Favorite>>> getFavorites({
    int? page,
    int? perPage,
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;

    return await _apiClient.get<PaginatedResponse<Favorite>>(
      ApiConfig.favoritesList,
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse<Favorite>.fromJson(
        data as Map<String, dynamic>,
        (json) => Favorite.fromJson(json as Map<String, dynamic>),
      ),
    );
  }

  // Add tutor to favorites
  Future<ApiResponse<Favorite>> addToFavorites(int tutorId) async {
    return await _apiClient.post<Favorite>(
      ApiConfig.addFavorite,
      data: {'tutor_id': tutorId},
      fromJson: (data) => Favorite.fromJson(data as Map<String, dynamic>),
    );
  }

  // Remove tutor from favorites
  Future<ApiResponse<void>> removeFromFavorites(int tutorId) async {
    return await _apiClient.delete<void>(
      ApiConfig.removeFavorite(tutorId),
    );
  }

  // Check if tutor is favorited
  Future<ApiResponse<FavoriteCheck>> checkFavorite(int tutorId) async {
    return await _apiClient.get<FavoriteCheck>(
      ApiConfig.checkFavorite(tutorId),
      fromJson: (data) => FavoriteCheck.fromJson(data as Map<String, dynamic>),
    );
  }

  // Get favorites count
  Future<ApiResponse<FavoriteCount>> getFavoritesCount() async {
    return await _apiClient.get<FavoriteCount>(
      ApiConfig.favoritesCount,
      fromJson: (data) => FavoriteCount.fromJson(data as Map<String, dynamic>),
    );
  }

  // Toggle favorite status
  Future<ApiResponse<dynamic>> toggleFavorite(int tutorId) async {
    final checkResponse = await checkFavorite(tutorId);
    
    if (checkResponse.isSuccess && checkResponse.data != null) {
      final isFavorited = checkResponse.data!.isFavorited;
      
      if (isFavorited) {
        // Remove from favorites
        final removeResponse = await removeFromFavorites(tutorId);
        if (removeResponse.isSuccess) {
          return ApiResponse.success(
            data: {'action': 'removed', 'is_favorited': false},
            message: 'Tutor removed from favorites',
          );
        } else {
          return removeResponse;
        }
      } else {
        // Add to favorites
        final addResponse = await addToFavorites(tutorId);
        if (addResponse.isSuccess) {
          return ApiResponse.success(
            data: {'action': 'added', 'is_favorited': true, 'favorite': addResponse.data},
            message: 'Tutor added to favorites',
          );
        } else {
          return addResponse;
        }
      }
    } else {
      return checkResponse;
    }
  }

  // Get all favorite tutor IDs (for quick checking)
  Future<List<int>> getFavoriteTutorIds() async {
    try {
      final response = await getFavorites();
      if (response.isSuccess && response.data != null) {
        return response.data!.data
            .map((favorite) => favorite.tutorId)
            .toList();
      }
    } catch (e) {
      print('Error getting favorite tutor IDs: $e');
    }
    return [];
  }

  // Check if multiple tutors are favorited
  Future<Map<int, bool>> checkMultipleFavorites(List<int> tutorIds) async {
    final result = <int, bool>{};
    
    // Initialize all as false
    for (final tutorId in tutorIds) {
      result[tutorId] = false;
    }
    
    try {
      final favoriteTutorIds = await getFavoriteTutorIds();
      
      // Update status for favorited tutors
      for (final tutorId in favoriteTutorIds) {
        if (result.containsKey(tutorId)) {
          result[tutorId] = true;
        }
      }
    } catch (e) {
      print('Error checking multiple favorites: $e');
    }
    
    return result;
  }

  // Remove multiple favorites
  Future<List<ApiResponse<void>>> removeMultipleFavorites(List<int> tutorIds) async {
    final results = <ApiResponse<void>>[];
    
    for (final tutorId in tutorIds) {
      final response = await removeFromFavorites(tutorId);
      results.add(response);
    }
    
    return results;
  }

  // Add multiple favorites
  Future<List<ApiResponse<Favorite>>> addMultipleFavorites(List<int> tutorIds) async {
    final results = <ApiResponse<Favorite>>[];
    
    for (final tutorId in tutorIds) {
      final response = await addToFavorites(tutorId);
      results.add(response);
    }
    
    return results;
  }

  // Clear all favorites
  Future<ApiResponse<void>> clearAllFavorites() async {
    try {
      final response = await getFavorites();
      if (response.isSuccess && response.data != null) {
        final tutorIds = response.data!.data
            .map((favorite) => favorite.tutorId)
            .toList();
        
        final removeResults = await removeMultipleFavorites(tutorIds);
        
        // Check if all removals were successful
        final allSuccessful = removeResults.every((result) => result.isSuccess);
        
        if (allSuccessful) {
          return ApiResponse.success(message: 'All favorites cleared successfully');
        } else {
          return ApiResponse.error(message: 'Some favorites could not be removed');
        }
      } else {
        return ApiResponse.error(message: 'Could not fetch favorites to clear');
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error clearing favorites: $e');
    }
  }
}
