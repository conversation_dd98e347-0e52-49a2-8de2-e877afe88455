<?php
// Simple test script to verify image upload functionality
// Run this from the tutor_finder_kh directory

require_once 'vendor/autoload.php';

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

echo "Testing Image Upload Functionality\n";
echo "==================================\n\n";

// Test 1: Check if storage directories exist
echo "1. Checking storage directories...\n";
$directories = [
    'storage/app/public',
    'storage/app/public/avatars',
    'storage/app/public/posts/images',
    'storage/app/public/posts/attachments'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "✅ $dir exists\n";
    } else {
        echo "❌ $dir does not exist\n";
        mkdir($dir, 0755, true);
        echo "✅ Created $dir\n";
    }
}

// Test 2: Check if storage link exists
echo "\n2. Checking storage link...\n";
if (is_link('public/storage')) {
    echo "✅ Storage link exists\n";
} else {
    echo "❌ Storage link does not exist\n";
    echo "Run: php artisan storage:link\n";
}

// Test 3: Check directory permissions
echo "\n3. Checking directory permissions...\n";
foreach ($directories as $dir) {
    if (is_writable($dir)) {
        echo "✅ $dir is writable\n";
    } else {
        echo "❌ $dir is not writable\n";
        chmod($dir, 0755);
        echo "✅ Fixed permissions for $dir\n";
    }
}

// Test 4: Check file upload limits
echo "\n4. Checking PHP upload limits...\n";
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');
$maxFileUploads = ini_get('max_file_uploads');

echo "upload_max_filesize: $uploadMaxFilesize\n";
echo "post_max_size: $postMaxSize\n";
echo "max_file_uploads: $maxFileUploads\n";

// Convert to bytes for comparison
function convertToBytes($value) {
    $unit = strtolower(substr($value, -1));
    $value = (int) $value;
    switch ($unit) {
        case 'g': $value *= 1024;
        case 'm': $value *= 1024;
        case 'k': $value *= 1024;
    }
    return $value;
}

$uploadBytes = convertToBytes($uploadMaxFilesize);
$postBytes = convertToBytes($postMaxSize);
$requiredBytes = 5 * 1024 * 1024; // 5MB

if ($uploadBytes >= $requiredBytes) {
    echo "✅ upload_max_filesize is sufficient\n";
} else {
    echo "❌ upload_max_filesize is too small (need at least 5M)\n";
}

if ($postBytes >= $requiredBytes) {
    echo "✅ post_max_size is sufficient\n";
} else {
    echo "❌ post_max_size is too small (need at least 5M)\n";
}

// Test 5: Check Laravel configuration
echo "\n5. Checking Laravel configuration...\n";
if (file_exists('.env')) {
    echo "✅ .env file exists\n";
    
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'APP_URL=') !== false) {
        echo "✅ APP_URL is configured\n";
    } else {
        echo "❌ APP_URL is not configured\n";
    }
    
    if (strpos($envContent, 'FILESYSTEM_DISK=') !== false) {
        echo "✅ FILESYSTEM_DISK is configured\n";
    } else {
        echo "❌ FILESYSTEM_DISK is not configured\n";
    }
} else {
    echo "❌ .env file does not exist\n";
}

echo "\n6. Testing file operations...\n";
$testFile = 'storage/app/public/avatars/test.txt';
if (file_put_contents($testFile, 'test')) {
    echo "✅ Can write to avatars directory\n";
    unlink($testFile);
    echo "✅ Can delete from avatars directory\n";
} else {
    echo "❌ Cannot write to avatars directory\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests pass, image upload should work correctly.\n";
echo "If any tests fail, please fix the issues before testing the app.\n";
?>
