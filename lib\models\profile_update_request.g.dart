// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_update_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProfileUpdateRequest _$ProfileUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    ProfileUpdateRequest(
      name: json['name'] as String?,
      phone: json['phone'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      gender: json['gender'] as String?,
      address: json['address'] as String?,
      tutorData: json['tutor_data'] == null
          ? null
          : TutorUpdateData.fromJson(
              json['tutor_data'] as Map<String, dynamic>),
      studentData: json['student_data'] == null
          ? null
          : StudentUpdateData.fromJson(
              json['student_data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProfileUpdateRequestToJson(
        ProfileUpdateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'phone': instance.phone,
      'date_of_birth': instance.dateOfBirth,
      'gender': instance.gender,
      'address': instance.address,
      'tutor_data': instance.tutorData,
      'student_data': instance.studentData,
    };

TutorUpdateData _$TutorUpdateDataFromJson(Map<String, dynamic> json) =>
    TutorUpdateData(
      subjects: (json['subjects'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      experienceYears: (json['experience_years'] as num?)?.toInt(),
      educationLevel: json['education_level'] as String?,
      hourlyRate: (json['hourly_rate'] as num?)?.toDouble(),
      bio: json['bio'] as String?,
      qualifications: (json['qualifications'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      languages: (json['languages'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      availability: json['availability'] as Map<String, dynamic>?,
      location: json['location'] as String?,
      teachingStyle: json['teaching_style'] as String?,
      specializations: (json['specializations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$TutorUpdateDataToJson(TutorUpdateData instance) =>
    <String, dynamic>{
      'subjects': instance.subjects,
      'experience_years': instance.experienceYears,
      'education_level': instance.educationLevel,
      'hourly_rate': instance.hourlyRate,
      'bio': instance.bio,
      'qualifications': instance.qualifications,
      'languages': instance.languages,
      'availability': instance.availability,
      'location': instance.location,
      'teaching_style': instance.teachingStyle,
      'specializations': instance.specializations,
    };

StudentUpdateData _$StudentUpdateDataFromJson(Map<String, dynamic> json) =>
    StudentUpdateData(
      gradeLevel: json['grade_level'] as String?,
      school: json['school'] as String?,
      learningPreferences: (json['learning_preferences'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      subjectsOfInterest: (json['subjects_of_interest'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      preferredLearningStyle: json['preferred_learning_style'] as String?,
      goals: json['goals'] as String?,
      availability: json['availability'] as Map<String, dynamic>?,
      budgetRange: json['budget_range'] as String?,
    );

Map<String, dynamic> _$StudentUpdateDataToJson(StudentUpdateData instance) =>
    <String, dynamic>{
      'grade_level': instance.gradeLevel,
      'school': instance.school,
      'learning_preferences': instance.learningPreferences,
      'subjects_of_interest': instance.subjectsOfInterest,
      'preferred_learning_style': instance.preferredLearningStyle,
      'goals': instance.goals,
      'availability': instance.availability,
      'budget_range': instance.budgetRange,
    };

ProfileUpdateResponse _$ProfileUpdateResponseFromJson(
        Map<String, dynamic> json) =>
    ProfileUpdateResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : ProfileUpdateData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProfileUpdateResponseToJson(
        ProfileUpdateResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

ProfileUpdateData _$ProfileUpdateDataFromJson(Map<String, dynamic> json) =>
    ProfileUpdateData(
      user: json['user'],
      profile: json['profile'],
      avatarUrl: json['avatar_url'] as String?,
    );

Map<String, dynamic> _$ProfileUpdateDataToJson(ProfileUpdateData instance) =>
    <String, dynamic>{
      'user': instance.user,
      'profile': instance.profile,
      'avatar_url': instance.avatarUrl,
    };
