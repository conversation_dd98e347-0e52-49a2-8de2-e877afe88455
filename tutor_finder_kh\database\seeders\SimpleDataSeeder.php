<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class SimpleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Clear existing data
        DB::table('tutor_categories')->delete();
        DB::table('categories')->delete();
        DB::table('tutors')->delete();
        DB::table('users')->where('user_type', '!=', 'admin')->delete();

        // Insert categories
        $categories = [
            ['name' => 'Mathematics'],
            ['name' => 'English'],
            ['name' => 'Physics'],
            ['name' => 'Chemistry'],
            ['name' => 'Biology'],
            ['name' => 'History'],
            ['name' => 'Geography'],
            ['name' => 'Computer Science'],
            ['name' => 'Programming'],
            ['name' => 'Web Development'],
            ['name' => 'Economics'],
            ['name' => 'Business Studies'],
            ['name' => 'Art'],
            ['name' => 'Music'],
            ['name' => 'French'],
            ['name' => 'Chinese'],
            ['name' => 'Khmer Literature'],
        ];

        DB::table('categories')->insert($categories);

        // Insert sample users (tutors)
        $users = [
            [
                'name' => 'Dr. Sarah Johnson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456789',
                'date_of_birth' => '1985-03-15',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Prof. Michael Chen',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456790',
                'date_of_birth' => '1980-07-22',
                'gender' => 'male',
                'address' => 'Siem Reap, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Emily Davis',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456791',
                'date_of_birth' => '1988-11-08',
                'gender' => 'female',
                'address' => 'Battambang, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Dr. James Wilson',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456792',
                'date_of_birth' => '1982-05-30',
                'gender' => 'male',
                'address' => 'Kampong Cham, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Ms. Lisa Park',
                'email' => '<EMAIL>',
                'email_verified_at' => $now,
                'password' => Hash::make('password'),
                'user_type' => 'tutor',
                'phone' => '+855123456793',
                'date_of_birth' => '1990-09-12',
                'gender' => 'female',
                'address' => 'Phnom Penh, Cambodia',
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('users')->insert($users);

        // Get the inserted user IDs
        $userIds = DB::table('users')->where('user_type', 'tutor')->pluck('id', 'email');

        // Insert tutor profiles
        $tutors = [
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Mathematics', 'Statistics', 'Calculus']),
                'experience_years' => 8,
                'education_level' => 'PhD',
                'hourly_rate' => 25.00,
                'bio' => 'Expert in calculus and algebra with PhD in Mathematics from Royal University of Phnom Penh.',
                'qualifications' => json_encode(['PhD Mathematics', 'Teaching Certificate']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']),
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.9,
                'reviews_count' => 127,
                'total_sessions' => 450,
                'response_time' => 30,
                'teaching_style' => 'Interactive and patient approach',
                'specializations' => json_encode(['Algebra', 'Calculus', 'Statistics']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Physics', 'Mathematics']),
                'experience_years' => 12,
                'education_level' => 'PhD',
                'hourly_rate' => 30.00,
                'bio' => 'Physics professor with extensive research experience in quantum mechanics.',
                'qualifications' => json_encode(['PhD Physics', 'Research Publications']),
                'languages' => json_encode(['English', 'Chinese', 'Khmer']),
                'availability' => json_encode(['Monday', 'Wednesday', 'Friday', 'Saturday']),
                'location' => 'Siem Reap',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 89,
                'total_sessions' => 320,
                'response_time' => 45,
                'teaching_style' => 'Research-based learning',
                'specializations' => json_encode(['Quantum Physics', 'Mechanics', 'Thermodynamics']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['English', 'Literature']),
                'experience_years' => 6,
                'education_level' => 'Masters',
                'hourly_rate' => 20.00,
                'bio' => 'English literature specialist with focus on creative writing and grammar.',
                'qualifications' => json_encode(['MA English Literature', 'TESOL Certificate']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode(['Tuesday', 'Thursday', 'Saturday', 'Sunday']),
                'location' => 'Battambang',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.7,
                'reviews_count' => 156,
                'total_sessions' => 280,
                'response_time' => 20,
                'teaching_style' => 'Creative and engaging methods',
                'specializations' => json_encode(['Creative Writing', 'Grammar', 'Literature Analysis']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Chemistry', 'Biology']),
                'experience_years' => 10,
                'education_level' => 'PhD',
                'hourly_rate' => 28.00,
                'bio' => 'Chemistry and biology expert with laboratory research background.',
                'qualifications' => json_encode(['PhD Chemistry', 'Lab Research Experience']),
                'languages' => json_encode(['English', 'Khmer']),
                'availability' => json_encode(['Monday', 'Tuesday', 'Thursday', 'Friday']),
                'location' => 'Kampong Cham',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.6,
                'reviews_count' => 98,
                'total_sessions' => 210,
                'response_time' => 35,
                'teaching_style' => 'Hands-on laboratory approach',
                'specializations' => json_encode(['Organic Chemistry', 'Biochemistry', 'Molecular Biology']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'user_id' => $userIds['<EMAIL>'],
                'subjects' => json_encode(['Biology', 'Environmental Science']),
                'experience_years' => 5,
                'education_level' => 'Masters',
                'hourly_rate' => 22.00,
                'bio' => 'Biology teacher with passion for environmental conservation and ecology.',
                'qualifications' => json_encode(['MS Biology', 'Environmental Science Certificate']),
                'languages' => json_encode(['English', 'Khmer', 'Korean']),
                'availability' => json_encode(['Wednesday', 'Friday', 'Saturday', 'Sunday']),
                'location' => 'Phnom Penh',
                'is_verified' => true,
                'is_available' => true,
                'rating' => 4.8,
                'reviews_count' => 134,
                'total_sessions' => 195,
                'response_time' => 25,
                'teaching_style' => 'Field-based learning and practical applications',
                'specializations' => json_encode(['Ecology', 'Genetics', 'Environmental Biology']),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        DB::table('tutors')->insert($tutors);

        // Link tutors to categories
        $categoryIds = DB::table('categories')->pluck('id', 'name');
        $tutorIds = DB::table('tutors')->pluck('id', 'user_id');

        $tutorCategories = [
            // Dr. Sarah Johnson - Mathematics
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Mathematics']],

            // Prof. Michael Chen - Physics, Mathematics
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Physics']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Mathematics']],

            // Ms. Emily Davis - English
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['English']],

            // Dr. James Wilson - Chemistry, Biology
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Chemistry']],
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Biology']],

            // Ms. Lisa Park - Biology
            ['tutor_id' => $tutorIds[$userIds['<EMAIL>']], 'category_id' => $categoryIds['Biology']],
        ];

        DB::table('tutor_categories')->insert($tutorCategories);

        $this->command->info('Sample data seeded successfully!');
        $this->command->info('Categories: ' . count($categories));
        $this->command->info('Tutors: ' . count($tutors));
        $this->command->info('Tutor-Category relationships: ' . count($tutorCategories));
    }
}
