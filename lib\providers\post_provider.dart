import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/tutor_post.dart';
import '../services/post_service.dart';

enum PostState { initial, loading, loaded, error }

class PostProvider with ChangeNotifier {
  final PostService _postService = PostService();

  // State management
  PostState _state = PostState.initial;
  String? _errorMessage;
  bool _isLoading = false;

  // Data storage
  List<TutorPost> _posts = [];
  List<TutorPost> _featuredPosts = [];
  List<TutorPost> _searchResults = [];
  PaginatedPostResponse? _paginatedPosts;
  TutorPost? _currentPost;

  // Pagination
  int _currentPage = 1;
  bool _hasMore = true;

  // Getters
  PostState get state => _state;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  List<TutorPost> get posts => _posts;
  List<TutorPost> get featuredPosts => _featuredPosts;
  List<TutorPost> get searchResults => _searchResults;
  PaginatedPostResponse? get paginatedPosts => _paginatedPosts;
  TutorPost? get currentPost => _currentPost;
  bool get hasMore => _hasMore;
  int get currentPage => _currentPage;

  // Helper methods
  void _setState(PostState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _setState(PostState.error);
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Load posts with pagination
  Future<void> loadPosts({
    bool refresh = false,
    int? categoryId,
    String? postType,
    String? search,
    int? tutorId,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _posts.clear();
    }

    if (!_hasMore && !refresh) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.getPosts(
        categoryId: categoryId,
        postType: postType,
        search: search,
        tutorId: tutorId,
        page: _currentPage,
        perPage: 15,
      );

      if (response.isSuccess && response.data != null) {
        _paginatedPosts = response.data!;
        
        if (refresh) {
          _posts = response.data!.data;
        } else {
          _posts.addAll(response.data!.data);
        }

        _hasMore = response.data!.hasNextPage;
        if (_hasMore) {
          _currentPage++;
        }

        _setState(PostState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load posts');
      }
    } catch (e) {
      _setError('Failed to load posts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load featured posts
  Future<void> loadFeaturedPosts() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.getFeaturedPosts();

      if (response.isSuccess && response.data != null) {
        _featuredPosts = response.data!;
        _setState(PostState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load featured posts');
      }
    } catch (e) {
      _setError('Failed to load featured posts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get a specific post
  Future<void> loadPost(String slug) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.getPost(slug);

      if (response.isSuccess && response.data != null) {
        _currentPost = response.data!;
        _setState(PostState.loaded);
      } else {
        _setError(response.message ?? 'Failed to load post');
      }
    } catch (e) {
      _setError('Failed to load post: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search posts
  Future<void> searchPosts(String query, {
    bool refresh = false,
    int? categoryId,
    String? postType,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _searchResults.clear();
    }

    if (!_hasMore && !refresh) return;

    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.getPosts(
        search: query,
        categoryId: categoryId,
        postType: postType,
        page: _currentPage,
        perPage: 15,
      );

      if (response.isSuccess && response.data != null) {
        if (refresh) {
          _searchResults = response.data!.data;
        } else {
          _searchResults.addAll(response.data!.data);
        }

        _hasMore = response.data!.hasNextPage;
        if (_hasMore) {
          _currentPage++;
        }

        _setState(PostState.loaded);
      } else {
        _setError(response.message ?? 'Failed to search posts');
      }
    } catch (e) {
      _setError('Failed to search posts: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create a new post
  Future<bool> createPost(CreatePostRequest request, {
    File? featuredImage,
    List<File>? attachments,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.createPost(
        request,
        featuredImage: featuredImage,
        attachments: attachments,
      );

      if (response.isSuccess && response.data != null) {
        // Add the new post to the beginning of the list
        _posts.insert(0, response.data!);
        _setState(PostState.loaded);
        return true;
      } else {
        _setError(response.message ?? 'Failed to create post');
        return false;
      }
    } catch (e) {
      _setError('Failed to create post: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update a post
  Future<bool> updatePost(int postId, UpdatePostRequest request) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.updatePost(postId, request);

      if (response.isSuccess && response.data != null) {
        // Update the post in the list
        final index = _posts.indexWhere((post) => post.id == postId);
        if (index != -1) {
          _posts[index] = response.data!;
        }
        
        // Update current post if it's the same
        if (_currentPost?.id == postId) {
          _currentPost = response.data!;
        }
        
        _setState(PostState.loaded);
        return true;
      } else {
        _setError(response.message ?? 'Failed to update post');
        return false;
      }
    } catch (e) {
      _setError('Failed to update post: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a post
  Future<bool> deletePost(int postId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _postService.deletePost(postId);

      if (response.isSuccess) {
        // Remove the post from the list
        _posts.removeWhere((post) => post.id == postId);
        _featuredPosts.removeWhere((post) => post.id == postId);
        _searchResults.removeWhere((post) => post.id == postId);
        
        // Clear current post if it's the same
        if (_currentPost?.id == postId) {
          _currentPost = null;
        }
        
        _setState(PostState.loaded);
        return true;
      } else {
        _setError(response.message ?? 'Failed to delete post');
        return false;
      }
    } catch (e) {
      _setError('Failed to delete post: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Toggle like
  Future<bool> toggleLike(int postId) async {
    try {
      final response = await _postService.toggleLike(postId);
      
      if (response.isSuccess) {
        // Update like count in the lists (this is a simplified approach)
        // In a real app, you'd want to get the updated like count from the server
        _updatePostLikeCount(postId);
        return true;
      } else {
        _setError(response.message ?? 'Failed to toggle like');
        return false;
      }
    } catch (e) {
      _setError('Failed to toggle like: $e');
      return false;
    }
  }

  // Toggle bookmark
  Future<bool> toggleBookmark(int postId) async {
    try {
      final response = await _postService.toggleBookmark(postId);
      
      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.message ?? 'Failed to toggle bookmark');
        return false;
      }
    } catch (e) {
      _setError('Failed to toggle bookmark: $e');
      return false;
    }
  }

  // Helper method to update like count (simplified)
  void _updatePostLikeCount(int postId) {
    // Update in main posts list
    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex != -1) {
      // This is a simplified approach - in reality you'd get the updated count from server
      notifyListeners();
    }
    
    // Update in featured posts list
    final featuredIndex = _featuredPosts.indexWhere((post) => post.id == postId);
    if (featuredIndex != -1) {
      notifyListeners();
    }
    
    // Update current post
    if (_currentPost?.id == postId) {
      notifyListeners();
    }
  }

  // Clear all data
  void clearData() {
    _posts.clear();
    _featuredPosts.clear();
    _searchResults.clear();
    _currentPost = null;
    _paginatedPosts = null;
    _currentPage = 1;
    _hasMore = true;
    _setState(PostState.initial);
  }

  // Refresh data
  Future<void> refreshData() async {
    await Future.wait([
      loadPosts(refresh: true),
      loadFeaturedPosts(),
    ]);
  }
}
