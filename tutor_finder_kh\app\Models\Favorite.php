<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Favorite extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'tutor_id',
    ];

    /**
     * Get the student that owns this favorite.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the tutor that is favorited.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class);
    }
}
