class ApiConfig {
  // Base URL for the separate Laravel API project
  // Try one of these URLs based on your setup:
  // static const String baseUrl = 'http://********:8000/api/v1'; // Android emulator
  // static const String baseUrl = 'http://localhost:8000/api/v1'; // Alternative for emulator
  static const String baseUrl = 'http://127.0.0.1:8000/api/v1'; // Windows/Desktop
  // static const String baseUrl = 'http://*************:8000/api/v1'; // Physical device (replace with your IP)
  // static const String baseUrl = 'https://your-domain.com/api/v1'; // Production

  // Timeout configurations - Optimized for better performance
  static const Duration connectTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 15);
  static const Duration sendTimeout = Duration(seconds: 10);

  // Default headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // API Endpoints
  static const String auth = '/auth';
  static const String tutors = '/tutors';
  static const String categories = '/categories';
  static const String favorites = '/favorites';
  static const String sessions = '/sessions';
  static const String reviews = '/reviews';
  static const String messages = '/messages';
  static const String notifications = '/notifications';

  // Auth endpoints
  static const String registerStudent = '$auth/register/student';
  static const String registerTutor = '$auth/register/tutor';
  static const String login = '$auth/login';
  static const String logout = '$auth/logout';
  static const String profile = '$auth/profile';

  // Tutor endpoints
  static const String tutorsList = tutors;
  static const String featuredTutors = '$tutors/featured';
  static const String searchTutors = '$tutors/search';
  static String tutorDetails(int id) => '$tutors/$id';
  static String tutorAvailability(int id) => '$tutors/$id/availability';
  static String tutorsByCategory(int categoryId) => '$tutors/category/$categoryId';

  // Category endpoints
  static const String categoriesList = categories;
  static const String popularCategories = '$categories/popular';
  static String categoryDetails(int id) => '$categories/$id';

  // Favorites endpoints
  static const String favoritesList = favorites;
  static const String addFavorite = favorites;
  static String removeFavorite(int tutorId) => '$favorites/$tutorId';
  static String checkFavorite(int tutorId) => '$favorites/check/$tutorId';
  static const String favoritesCount = '$favorites/count';

  // Session endpoints
  static const String sessionsList = sessions;
  static const String bookSession = sessions;
  static String sessionDetails(int id) => '$sessions/$id';
  static String cancelSession(int id) => '$sessions/$id/cancel';
  static const String upcomingSessions = '$sessions/upcoming';
  static const String pastSessions = '$sessions/past';

  // Tutor session endpoints
  static const String tutorSessions = '/tutor/sessions';
  static String confirmSession(int id) => '$tutorSessions/$id/confirm';
  static String startSession(int id) => '$tutorSessions/$id/start';
  static String completeSession(int id) => '$tutorSessions/$id/complete';

  // Review endpoints
  static const String createReview = reviews;
  static const String myReviews = '$reviews/my-reviews';
  static String tutorReviews(int tutorId) => '$reviews/tutor/$tutorId';
  static String updateReview(int id) => '$reviews/$id';
  static String deleteReview(int id) => '$reviews/$id';

  // Message endpoints
  static const String messagesList = messages;
  static const String sendMessage = '/test/messages/send'; // Test endpoint without auth
  static const String conversations = '$messages/conversations';
  static String conversation(int userId, String userType) => '$messages/conversation/$userId/$userType';
  static String markMessageAsRead(int id) => '$messages/$id/read';
  static const String unreadMessagesCount = '$messages/unread-count';

  // Notification endpoints
  static const String notificationsList = notifications;
  static String markNotificationAsRead(int id) => '$notifications/$id/read';
  static const String markAllNotificationsAsRead = '$notifications/mark-all-read';
  static String deleteNotification(int id) => '$notifications/$id';
  static const String unreadNotificationsCount = '$notifications/unread-count';

  // Session chat endpoints
  static String sessionChats(int sessionId) => '$sessions/$sessionId/chats';
  static String sendChatMessage(int sessionId) => '$sessions/$sessionId/chats';
  static String markChatAsRead(int sessionId, int chatId) => '$sessions/$sessionId/chats/$chatId/read';



  // Storage keys
  static const String tokenKey = 'auth_token';
  static const String userTypeKey = 'user_type';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';

  // Pagination
  static const int defaultPageSize = 15;
  static const int maxPageSize = 50;

  // File upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
}
