import '../models/api_response.dart';
import '../models/notification.dart';
import '../config/api_config.dart';
import 'api_client.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get user's notifications
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getNotifications({
    int? page,
    int? perPage,
    String? type,
    bool? isRead,
  }) async {
    final queryParameters = <String, dynamic>{};
    
    if (page != null) queryParameters['page'] = page;
    if (perPage != null) queryParameters['per_page'] = perPage;
    if (type != null) queryParameters['type'] = type;
    if (isRead != null) queryParameters['is_read'] = isRead;

    return await _apiClient.get<PaginatedResponse<NotificationModel>>(
      ApiConfig.notificationsList,
      queryParameters: queryParameters,
      fromJson: (data) => PaginatedResponse<NotificationModel>.fromJson(
        data as Map<String, dynamic>,
        (json) => NotificationModel.fromJson(json as Map<String, dynamic>),
      ),
    );
  }

  // Mark notification as read
  Future<ApiResponse<void>> markAsRead(int notificationId) async {
    return await _apiClient.put<void>(
      ApiConfig.markNotificationAsRead(notificationId),
    );
  }

  // Mark all notifications as read
  Future<ApiResponse<void>> markAllAsRead() async {
    return await _apiClient.put<void>(
      ApiConfig.markAllNotificationsAsRead,
    );
  }

  // Delete notification
  Future<ApiResponse<void>> deleteNotification(int notificationId) async {
    return await _apiClient.delete<void>(
      ApiConfig.deleteNotification(notificationId),
    );
  }

  // Get unread notifications count
  Future<ApiResponse<UnreadCount>> getUnreadCount() async {
    return await _apiClient.get<UnreadCount>(
      ApiConfig.unreadNotificationsCount,
      fromJson: (data) => UnreadCount.fromJson(data as Map<String, dynamic>),
    );
  }

  // Get unread notifications
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getUnreadNotifications({
    int? page,
    int? perPage,
  }) async {
    return await getNotifications(
      page: page,
      perPage: perPage,
      isRead: false,
    );
  }

  // Get notifications by type
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getNotificationsByType(
    String type, {
    int? page,
    int? perPage,
  }) async {
    return await getNotifications(
      page: page,
      perPage: perPage,
      type: type,
    );
  }

  // Get booking notifications
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getBookingNotifications({
    int? page,
    int? perPage,
  }) async {
    return await getNotificationsByType('booking', page: page, perPage: perPage);
  }

  // Get message notifications
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getMessageNotifications({
    int? page,
    int? perPage,
  }) async {
    return await getNotificationsByType('message', page: page, perPage: perPage);
  }

  // Get system notifications
  Future<ApiResponse<PaginatedResponse<NotificationModel>>> getSystemNotifications({
    int? page,
    int? perPage,
  }) async {
    return await getNotificationsByType('system', page: page, perPage: perPage);
  }

  // Get important notifications (booking and payment)
  Future<List<NotificationModel>> getImportantNotifications() async {
    try {
      final bookingResponse = await getBookingNotifications();
      final paymentResponse = await getNotificationsByType('payment');
      
      final important = <NotificationModel>[];
      
      if (bookingResponse.isSuccess && bookingResponse.data != null) {
        important.addAll(bookingResponse.data!.data);
      }
      
      if (paymentResponse.isSuccess && paymentResponse.data != null) {
        important.addAll(paymentResponse.data!.data);
      }
      
      // Sort by creation date (newest first)
      important.sort((a, b) {
        final aDate = a.createdDateTime ?? DateTime.now();
        final bDate = b.createdDateTime ?? DateTime.now();
        return bDate.compareTo(aDate);
      });
      
      return important;
    } catch (e) {
      print('Error getting important notifications: $e');
      return [];
    }
  }

  // Mark multiple notifications as read
  Future<List<ApiResponse<void>>> markMultipleAsRead(List<int> notificationIds) async {
    final results = <ApiResponse<void>>[];
    
    for (final id in notificationIds) {
      final response = await markAsRead(id);
      results.add(response);
    }
    
    return results;
  }

  // Delete multiple notifications
  Future<List<ApiResponse<void>>> deleteMultipleNotifications(List<int> notificationIds) async {
    final results = <ApiResponse<void>>[];
    
    for (final id in notificationIds) {
      final response = await deleteNotification(id);
      results.add(response);
    }
    
    return results;
  }

  // Clear all notifications
  Future<ApiResponse<void>> clearAllNotifications() async {
    try {
      final response = await getNotifications();
      if (response.isSuccess && response.data != null) {
        final notificationIds = response.data!.data
            .map((notification) => notification.id)
            .toList();
        
        final deleteResults = await deleteMultipleNotifications(notificationIds);
        
        // Check if all deletions were successful
        final allSuccessful = deleteResults.every((result) => result.isSuccess);
        
        if (allSuccessful) {
          return ApiResponse.success(message: 'All notifications cleared successfully');
        } else {
          return ApiResponse.error(message: 'Some notifications could not be deleted');
        }
      } else {
        return ApiResponse.error(message: 'Could not fetch notifications to clear');
      }
    } catch (e) {
      return ApiResponse.error(message: 'Error clearing notifications: $e');
    }
  }

  // Get notification statistics
  Map<String, dynamic> getNotificationStats(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      return {
        'total': 0,
        'unread': 0,
        'byType': <String, int>{},
        'readPercentage': 0.0,
      };
    }

    final unreadCount = notifications.where((n) => n.isUnread).length;
    final byType = <String, int>{};
    
    for (final notification in notifications) {
      byType[notification.type] = (byType[notification.type] ?? 0) + 1;
    }

    return {
      'total': notifications.length,
      'unread': unreadCount,
      'byType': byType,
      'readPercentage': ((notifications.length - unreadCount) / notifications.length) * 100,
    };
  }

  // Filter notifications by date range
  List<NotificationModel> filterNotificationsByDateRange(
    List<NotificationModel> notifications,
    DateTime startDate,
    DateTime endDate,
  ) {
    return notifications.where((notification) {
      final createdDate = notification.createdDateTime;
      if (createdDate == null) return false;
      
      return createdDate.isAfter(startDate) && createdDate.isBefore(endDate);
    }).toList();
  }

  // Get recent notifications (last 7 days)
  List<NotificationModel> getRecentNotifications(List<NotificationModel> notifications) {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return filterNotificationsByDateRange(notifications, sevenDaysAgo, DateTime.now());
  }
}
